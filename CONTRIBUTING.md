# Contributing to LLM Service Mapper

Thank you for your interest in contributing to the LLM Service Mapper project! This guide will help you get started with contributing code, documentation, or other improvements.

## 🎯 Ways to Contribute

### 🐛 Bug Reports
- Report bugs using GitHub Issues
- Include detailed reproduction steps
- Provide sample data when possible
- Specify your environment (OS, Python version, etc.)

### ✨ Feature Requests
- Describe your use case clearly
- Explain the expected behavior
- Consider contributing the implementation
- Discuss with maintainers before large changes

### 📚 Documentation
- Improve existing documentation
- Add examples and tutorials
- Fix typos and clarify instructions
- Translate documentation (future)

### 💻 Code Contributions
- Bug fixes
- New features
- Performance improvements
- Test coverage improvements

## 🚀 Getting Started

### 1. <PERSON> and <PERSON>lone
```bash
# Fork the repository on GitHub
# Then clone your fork
git clone https://github.com/YOUR_USERNAME/airflow-llm-service-mapper.git
cd airflow-llm-service-mapper

# Add upstream remote
git remote add upstream https://github.com/ORIGINAL_OWNER/airflow-llm-service-mapper.git
```

### 2. Set Up Development Environment
```bash
# Install development dependencies
pip install -r requirements-test.txt

# Install pre-commit hooks
pre-commit install

# Verify setup
pytest --version
flake8 --version
black --version
```

### 3. Create a Branch
```bash
# Create and switch to a new branch
git checkout -b feature/your-feature-name

# Or for bug fixes
git checkout -b fix/issue-description
```

## 🔧 Development Workflow

### Code Style
We use automated code formatting and linting:

```bash
# Format code
black shared/ standalone/src/

# Sort imports
isort shared/ standalone/src/

# Lint code
flake8 shared/ standalone/src/

# Type checking
mypy shared/ standalone/src/
```

### Running Tests
```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit                    # Unit tests only
pytest -m integration            # Integration tests only
pytest -m airflow                # Airflow-specific tests
pytest -m standalone             # Standalone-specific tests

# Run with coverage
pytest --cov=shared --cov=standalone/src --cov-report=html

# Run specific test file
pytest tests/unit/test_shared_common/test_data_parsers.py
```

### Testing Guidelines
- Write tests for all new functionality
- Maintain or improve test coverage
- Use descriptive test names
- Include both positive and negative test cases
- Mock external dependencies

### Documentation
- Update documentation for new features
- Include code examples
- Follow existing documentation style
- Test documentation examples

## 📋 Contribution Guidelines

### Code Quality Standards
- **PEP 8**: Follow Python style guidelines
- **Type Hints**: Use type hints for function signatures
- **Docstrings**: Document all public functions and classes
- **Error Handling**: Include appropriate error handling
- **Logging**: Use structured logging for debugging

### Commit Message Format
Use conventional commit format:
```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Examples:
```
feat(standalone): add support for GraphQL schema parsing

fix(airflow): resolve import error in DAG 03

docs(readme): update installation instructions

test(shared): add unit tests for data_parsers module
```

### Pull Request Process

#### Before Submitting
1. **Sync with upstream**:
   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

2. **Run full test suite**:
   ```bash
   pytest
   flake8 shared/ standalone/src/
   black --check shared/ standalone/src/
   ```

3. **Update documentation** if needed

4. **Add changelog entry** for significant changes

#### Pull Request Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Other (please describe)

## Testing
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Updated documentation

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] Changelog updated (if applicable)
```

#### Review Process
1. **Automated Checks**: CI/CD pipeline runs tests and quality checks
2. **Code Review**: Maintainers review code and provide feedback
3. **Discussion**: Address feedback and make necessary changes
4. **Approval**: Once approved, maintainers will merge the PR

## 🏗️ Project Structure

### Understanding the Codebase
```
airflow-llm-service-mapper/
├── airflow/                      # Airflow-specific components
│   ├── dags/                     # Airflow DAGs
│   └── docs/                     # Airflow documentation
├── standalone/                   # Standalone components
│   ├── src/                      # Source code
│   ├── tests/                    # Standalone tests
│   └── docs/                     # Standalone documentation
├── shared/                       # Shared components
│   ├── common/                   # Common modules
│   └── sample_data/              # Sample data
├── tests/                        # Project-wide tests
└── docs/                         # Project documentation
```

### Component Guidelines

#### Shared Modules (`shared/common/`)
- Must work in both standalone and Airflow contexts
- No Airflow-specific dependencies
- Comprehensive error handling
- Well-documented APIs

#### Standalone Components (`standalone/src/`)
- Self-contained functionality
- Minimal dependencies
- Command-line interface
- Docker-ready

#### Airflow Components (`airflow/dags/`)
- Use TaskFlow API (@task decorators)
- Import from shared modules
- Include comprehensive documentation
- Handle Airflow-specific concerns (XCom, etc.)

## 🧪 Testing Strategy

### Test Categories
- **Unit Tests**: Test individual functions and classes
- **Integration Tests**: Test component interactions
- **DAG Tests**: Test Airflow DAG structure and syntax
- **End-to-End Tests**: Test complete workflows

### Test Data
- Use fixtures for consistent test data
- Include realistic sample data
- Mock external dependencies
- Test edge cases and error conditions

### Performance Tests
- Include performance benchmarks for critical paths
- Test memory usage for large files
- Validate scalability assumptions

## 📚 Documentation Standards

### Code Documentation
```python
def parse_wsdl_content(wsdl_string: str) -> Dict[str, Any]:
    """
    Parse WSDL string content into a structured dictionary.
    
    Args:
        wsdl_string: Raw WSDL content as string
        
    Returns:
        Dictionary containing parsed WSDL structure with keys:
        - services: List of services defined in the WSDL
        - operations: List of operations with input/output messages
        - messages: List of message definitions
        - types: List of type definitions
        
    Raises:
        ValueError: If WSDL content is invalid
        
    Example:
        >>> wsdl_content = load_wsdl_file("service.wsdl")
        >>> parsed = parse_wsdl_content(wsdl_content)
        >>> print(parsed["services"])
    """
```

### README Updates
- Keep README.md current with new features
- Update quick start guides
- Maintain accurate documentation links

### Changelog
- Add entries for all user-facing changes
- Follow Keep a Changelog format
- Include migration notes for breaking changes

## 🔒 Security Guidelines

### Reporting Security Issues
- **DO NOT** open public issues for security vulnerabilities
- Email security issues to maintainers privately
- Include detailed reproduction steps
- Allow time for fix before public disclosure

### Security Best Practices
- Validate all input data
- Use secure defaults
- Avoid hardcoded secrets
- Follow principle of least privilege
- Keep dependencies updated

## 🤝 Community Guidelines

### Code of Conduct
- Be respectful and inclusive
- Welcome newcomers
- Provide constructive feedback
- Focus on the issue, not the person
- Help others learn and grow

### Communication
- Use GitHub Issues for bug reports and feature requests
- Use GitHub Discussions for questions and general discussion
- Be patient with response times
- Provide context and details in communications

## 🎯 Contribution Ideas

### Good First Issues
- Documentation improvements
- Adding test cases
- Fixing typos
- Small bug fixes
- Adding examples

### Advanced Contributions
- New parser implementations
- Performance optimizations
- ML model improvements
- Cloud integrations
- Advanced features

### Areas Needing Help
- Test coverage improvements
- Documentation enhancements
- Performance optimization
- Additional file format support
- Cloud deployment guides

## 📞 Getting Help

### Resources
- **Documentation**: Comprehensive guides in this repository
- **Issues**: Search existing issues before creating new ones
- **Discussions**: Ask questions and share ideas
- **Code Review**: Learn from feedback on pull requests

### Mentorship
- New contributors are welcome
- Maintainers provide guidance and feedback
- Pair programming sessions available for complex features
- Regular contributor meetings (planned)

---

## 🙏 Recognition

Contributors are recognized in:
- **CONTRIBUTORS.md**: List of all contributors
- **Release Notes**: Major contributions highlighted
- **Documentation**: Author attribution where appropriate

Thank you for contributing to the LLM Service Mapper project! Your contributions help make service transformation accessible to everyone. 🎯
