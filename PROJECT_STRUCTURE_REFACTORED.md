# Refactored Project Structure

This document describes the new, clean project structure that separates Airflow and standalone components.

## 🏗️ New Directory Structure

```
airflow-llm-service-mapper/
├── 🔄 airflow/                          # Airflow-specific components
│   ├── dags/                            # Airflow DAGs
│   │   ├── 01_ingest_and_parse_definitions_dag.py
│   │   ├── 02_generate_training_data_dag.py
│   │   ├── 03_train_ml_model_dag.py
│   │   ├── 04_operational_mapping_pipeline_dag.py
│   │   ├── 05_monitoring_and_retraining_trigger_dag.py
│   │   ├── 06_ingest_operational_logs_dag.py
│   │   └── common/                      # Airflow-specific common modules
│   ├── plugins/                         # Airflow plugins
│   ├── docs/                            # Airflow documentation
│   ├── docker-compose.yaml              # Airflow deployment
│   ├── Dockerfile                       # Airflow custom image
│   └── requirements.txt                 # Airflow dependencies
├── ⚡ standalone/                        # Standalone components
│   ├── src/                             # Source code
│   │   ├── standalone_ingest_parser.py  # Main executable
│   │   ├── constants.py                 # Configuration
│   │   ├── data_parsers.py              # WSDL/OpenAPI parsers
│   │   └── validation_utils.py          # Data validation
│   ├── tests/                           # Standalone tests
│   │   └── test_standalone_parser.py    # Comprehensive test suite
│   ├── docs/                            # Standalone documentation
│   │   ├── README_standalone.md         # Complete usage guide
│   │   ├── QUICK_REFERENCE.md           # Quick reference card
│   │   ├── CHANGELOG_STANDALONE.md      # Version history
│   │   └── STANDALONE_IMPLEMENTATION_SUMMARY.md
│   ├── examples/                        # Usage examples
│   │   ├── basic_usage.sh               # Local usage examples
│   │   └── docker_usage.sh              # Docker usage examples
│   ├── Dockerfile                       # Standalone Docker image
│   ├── docker-compose.yaml              # Standalone deployment
│   ├── requirements.txt                 # Standalone dependencies
│   ├── run_parser.sh                    # Convenience script
│   └── README.md                        # Standalone overview
├── 🔗 shared/                           # Shared components
│   ├── common/                          # Common modules (used by both)
│   │   ├── constants.py                 # Shared constants
│   │   ├── data_parsers.py              # Shared parsers
│   │   ├── validation_utils.py          # Shared validation
│   │   ├── definition_store.py          # Service catalog
│   │   ├── model_management.py          # ML model utilities
│   │   ├── llm_utils.py                 # LLM integration
│   │   ├── heuristic_engine.py          # Rule-based mapping
│   │   ├── training_data_store.py       # Training data management
│   │   ├── service_mapper_factory.py    # Service mapping factory
│   │   ├── models/                      # Data models
│   │   ├── parsers/                     # Specialized parsers
│   │   └── ml/                          # ML utilities
│   ├── sample_data/                     # Sample WSDL/OpenAPI files
│   │   ├── wsdl_input/                  # Sample WSDL files
│   │   ├── osdmp_target/                # Sample OpenAPI files
│   │   ├── java_source/                 # Sample Java code
│   │   ├── logs/                        # Sample log files
│   │   └── training_data/               # Sample training data
│   └── schemas/                         # Data schemas and validation
├── 📁 data/                             # Data storage (runtime)
│   ├── parsed_definitions/              # Parsed service definitions
│   ├── training_data/                   # ML training datasets
│   └── operational_logs/                # Operational log data
├── 🤖 models/                           # ML models (runtime)
│   └── model_pointers/                  # Model version pointers
├── 📚 docs/                             # Project-wide documentation
│   ├── DEPLOYMENT_ARCHITECTURE_GUIDE.md
│   ├── SAMPLE_DATA_PRODUCT_OVERVIEW.md
│   └── SAMPLE_DATA_TECHNICAL_GUIDE.md
├── 🛠️ scripts/                          # Utility scripts
│   └── verify_environment.sh
├── README.md                            # Main project README
└── project_structure.md                # Original structure (deprecated)
```

## 🔄 Migration Benefits

### ✅ Clear Separation of Concerns
- **Airflow components** are isolated in `airflow/`
- **Standalone components** are isolated in `standalone/`
- **Shared resources** are in `shared/` for reuse

### ✅ Independent Development
- **Airflow team** can work in `airflow/` without affecting standalone
- **Standalone team** can work in `standalone/` without affecting Airflow
- **Shared modules** can be updated independently

### ✅ Deployment Flexibility
- **Standalone deployment**: Only need `standalone/` directory
- **Airflow deployment**: Only need `airflow/` and `shared/` directories
- **Hybrid deployment**: Can use both simultaneously

### ✅ Docker Optimization
- **Standalone Docker**: Lightweight image (~200MB)
- **Airflow Docker**: Full-featured image (~2GB)
- **Separate build contexts**: Faster builds and smaller images

## 🚀 Usage Patterns

### Standalone Development
```bash
cd standalone/
pip install -r requirements.txt
python tests/test_standalone_parser.py
python src/standalone_ingest_parser.py --help
```

### Airflow Development
```bash
cd airflow/
docker-compose up -d
# Access UI at http://localhost:8080
```

### Shared Resource Updates
```bash
# Update shared modules
vim shared/common/data_parsers.py

# Test in standalone
cd standalone/
python tests/test_standalone_parser.py

# Test in Airflow
cd airflow/
docker-compose restart airflow-webserver
```

## 🐳 Docker Deployment

### Standalone Docker
```bash
cd standalone/
docker build -t standalone-parser:latest .
docker run --rm -v $(pwd)/data:/app/data standalone-parser:latest
```

### Airflow Docker
```bash
cd airflow/
docker-compose up -d
```

## 📊 File Organization

### Before Refactoring (Mixed)
```
project/
├── standalone_ingest_parser.py          # Mixed with DAGs
├── standalone_common/                   # Duplicate code
├── dags/                                # Mixed with standalone
├── requirements_standalone.txt          # Mixed dependencies
└── README_standalone.md                 # Mixed documentation
```

### After Refactoring (Clean)
```
project/
├── airflow/                             # ✅ Airflow-only
├── standalone/                          # ✅ Standalone-only
├── shared/                              # ✅ Shared resources
└── docs/                                # ✅ Project documentation
```

## 🔧 Development Workflow

### Adding New Features

#### Standalone Feature
1. Work in `standalone/src/`
2. Add tests in `standalone/tests/`
3. Update docs in `standalone/docs/`
4. Test: `cd standalone && python tests/test_standalone_parser.py`

#### Airflow Feature
1. Work in `airflow/dags/`
2. Add tests in `airflow/tests/`
3. Update docs in `airflow/docs/`
4. Test: `cd airflow && docker-compose restart`

#### Shared Feature
1. Work in `shared/common/`
2. Test in both environments
3. Update documentation in both places

### Testing Strategy
```bash
# Test standalone
cd standalone/
python tests/test_standalone_parser.py
./examples/basic_usage.sh

# Test Airflow
cd airflow/
docker-compose exec airflow-webserver python -m pytest tests/

# Test integration
cd standalone/
python src/standalone_ingest_parser.py \
  --service-name integration_test \
  --wsdl-uri ../shared/sample_data/wsdl_input/test.wsdl \
  --openapi-uri ../shared/sample_data/osdmp_target/test.yaml
```

## 📚 Documentation Strategy

### Audience-Specific Documentation
- **`standalone/README.md`**: Standalone users
- **`airflow/README.md`**: Airflow users (to be created)
- **`README.md`**: Project overview and choice guidance
- **`docs/`**: Technical and architectural documentation

### Cross-References
- Main README links to both deployment options
- Standalone docs reference shared resources
- Airflow docs reference shared resources
- Shared docs explain usage in both contexts

## 🔄 Migration Checklist

### ✅ Completed
- [x] Created new directory structure
- [x] Moved Airflow components to `airflow/`
- [x] Moved standalone components to `standalone/`
- [x] Moved shared resources to `shared/`
- [x] Updated import paths in standalone code
- [x] Created standalone Dockerfile
- [x] Created standalone docker-compose.yaml
- [x] Updated documentation structure
- [x] Created usage examples
- [x] Tested standalone functionality

### 🔄 Next Steps
- [ ] Create Airflow-specific documentation
- [ ] Update Airflow DAGs to use shared modules
- [ ] Create integration tests
- [ ] Update CI/CD pipelines
- [ ] Create deployment guides
- [ ] Clean up old files

## 🧹 Cleanup Commands

### Remove Old Files (After Verification)
```bash
# Remove old standalone files from root
rm standalone_ingest_parser.py
rm -rf standalone_common/
rm test_standalone_parser.py
rm run_parser.sh
rm requirements_standalone.txt
rm README_standalone.md
rm QUICK_REFERENCE.md
rm CHANGELOG_STANDALONE.md
rm STANDALONE_IMPLEMENTATION_SUMMARY.md

# Remove old mixed directories
rm -rf include/  # Moved to shared/
rm -rf plugins/ # Moved to airflow/
```

### Verify New Structure
```bash
# Test standalone
cd standalone/
python tests/test_standalone_parser.py

# Test Airflow (when Docker is available)
cd airflow/
docker-compose up -d

# Test shared resources
ls -la shared/sample_data/
ls -la shared/common/
```

## 🎯 Benefits Achieved

### 🔧 Development
- **Faster development**: Clear separation reduces confusion
- **Independent testing**: Each component can be tested separately
- **Reduced conflicts**: Teams can work independently

### 🚀 Deployment
- **Smaller images**: Standalone Docker image is much smaller
- **Flexible deployment**: Choose what you need
- **Better resource usage**: Deploy only required components

### 📚 Documentation
- **Targeted docs**: Each audience gets relevant information
- **Easier maintenance**: Updates affect only relevant sections
- **Better onboarding**: Clear paths for different use cases

### 🔄 Maintenance
- **Easier updates**: Changes are isolated to relevant components
- **Better testing**: Focused test suites for each component
- **Cleaner codebase**: No mixed concerns or duplicate code

---

**🎉 Refactoring Complete!**

The project now has a clean, maintainable structure that supports both Airflow and standalone deployment patterns while sharing common resources efficiently.
