"""
Pytest configuration and shared fixtures for the LLM Service Mapper project.
"""

import pytest
import os
import sys
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any

# Add project paths to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "shared"))
sys.path.insert(0, str(project_root / "standalone" / "src"))

# Import shared modules for testing
from shared.common import constants, data_parsers, validation_utils
from shared.common.definition_store import DefinitionStore


@pytest.fixture(scope="session")
def project_root_path():
    """Return the project root path."""
    return Path(__file__).parent.parent


@pytest.fixture(scope="session")
def sample_data_path(project_root_path):
    """Return the sample data directory path."""
    return project_root_path / "shared" / "sample_data"


@pytest.fixture
def sample_wsdl_content(sample_data_path):
    """Load sample WSDL content for testing."""
    wsdl_file = sample_data_path / "wsdl_input" / "todo-service.wsdl"
    if wsdl_file.exists():
        with open(wsdl_file, 'r', encoding='utf-8') as f:
            return f.read()
    else:
        # Return minimal WSDL for testing if file doesn't exist
        return """<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:tns="http://example.com/todo"
             targetNamespace="http://example.com/todo">
    <types>
        <schema xmlns="http://www.w3.org/2001/XMLSchema"
                targetNamespace="http://example.com/todo">
            <element name="getTodosRequest">
                <complexType>
                    <sequence>
                        <element name="userId" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="getTodosResponse">
                <complexType>
                    <sequence>
                        <element name="todos" type="tns:TodoList"/>
                    </sequence>
                </complexType>
            </element>
        </schema>
    </types>
    <message name="getTodosRequest">
        <part name="parameters" element="tns:getTodosRequest"/>
    </message>
    <message name="getTodosResponse">
        <part name="parameters" element="tns:getTodosResponse"/>
    </message>
    <portType name="TodoServicePortType">
        <operation name="getTodos">
            <input message="tns:getTodosRequest"/>
            <output message="tns:getTodosResponse"/>
        </operation>
    </portType>
</definitions>"""


@pytest.fixture
def sample_xsd_content(sample_data_path):
    """Load sample XSD content for testing."""
    xsd_file = sample_data_path / "xsd_input" / "todo-types.xsd"
    if xsd_file.exists():
        with open(xsd_file, 'r', encoding='utf-8') as f:
            return f.read()
    else:
        # Return minimal XSD for testing if file doesn't exist
        return """<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema"
        targetNamespace="http://example.com/todo"
        xmlns:tns="http://example.com/todo">
    <complexType name="Todo">
        <sequence>
            <element name="id" type="int"/>
            <element name="title" type="string"/>
            <element name="completed" type="boolean"/>
        </sequence>
    </complexType>
    <complexType name="TodoList">
        <sequence>
            <element name="todo" type="tns:Todo" maxOccurs="unbounded"/>
        </sequence>
    </complexType>
</schema>"""


@pytest.fixture
def sample_openapi_content(sample_data_path):
    """Load sample OpenAPI content for testing."""
    openapi_file = sample_data_path / "osdmp_target" / "todo-api.yaml"
    if openapi_file.exists():
        with open(openapi_file, 'r', encoding='utf-8') as f:
            return f.read()
    else:
        # Return minimal OpenAPI for testing if file doesn't exist
        return """openapi: 3.0.0
info:
  title: Todo API
  version: 1.0.0
  description: A simple Todo API
paths:
  /todos:
    get:
      summary: Get all todos
      parameters:
        - name: userId
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: List of todos
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Todo'
components:
  schemas:
    Todo:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        completed:
          type: boolean
      required:
        - id
        - title
        - completed"""


@pytest.fixture
def sample_java_content(sample_data_path):
    """Load sample Java content for testing."""
    java_file = sample_data_path / "java_source" / "Todo.java"
    if java_file.exists():
        with open(java_file, 'r', encoding='utf-8') as f:
            return f.read()
    else:
        # Return minimal Java class for testing if file doesn't exist
        return """package com.example.todo;

public class Todo {
    private int id;
    private String title;
    private boolean completed;
    
    public Todo() {}
    
    public Todo(int id, String title, boolean completed) {
        this.id = id;
        this.title = title;
        this.completed = completed;
    }
    
    // Getters and setters
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public boolean isCompleted() { return completed; }
    public void setCompleted(boolean completed) { this.completed = completed; }
}"""


@pytest.fixture
def temp_directory():
    """Create a temporary directory for testing."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def mock_service_catalog():
    """Mock service catalog for testing."""
    return {
        "services": {
            "test_service": {
                "service_name": "test_service",
                "wsdl_path": "/test/path/service.wsdl",
                "openapi_path": "/test/path/api.yaml",
                "parsed_definitions": {
                    "wsdl": {"operations": ["getTodos"], "messages": ["getTodosRequest"]},
                    "openapi": {"paths": ["/todos"], "schemas": ["Todo"]}
                },
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        },
        "last_updated": "2024-01-01T00:00:00Z"
    }


@pytest.fixture
def mock_training_data():
    """Mock training data for testing."""
    return {
        "examples": [
            {
                "id": "test_001",
                "source_type": "wsdl_to_openapi",
                "service_name": "test_service",
                "input": "WSDL operation: getTodos(userId: string) -> TodoList",
                "target": "GET /todos?userId={userId} -> Todo[]",
                "metadata": {
                    "operation_type": "query",
                    "complexity": "simple"
                }
            }
        ],
        "metadata": {
            "total_examples": 1,
            "service_name": "test_service",
            "created_at": "2024-01-01T00:00:00Z"
        }
    }


@pytest.fixture
def mock_parsed_wsdl():
    """Mock parsed WSDL data for testing."""
    return {
        "services": [
            {
                "name": "TodoService",
                "port_type": "TodoServicePortType",
                "binding": "TodoServiceBinding"
            }
        ],
        "operations": [
            {
                "name": "getTodos",
                "input_message": "getTodosRequest",
                "output_message": "getTodosResponse",
                "port_type": "TodoServicePortType"
            }
        ],
        "messages": [
            {
                "name": "getTodosRequest",
                "parts": [{"name": "parameters", "element": "getTodosRequest"}]
            },
            {
                "name": "getTodosResponse", 
                "parts": [{"name": "parameters", "element": "getTodosResponse"}]
            }
        ],
        "types": [
            {
                "name": "getTodosRequest",
                "type": "complexType",
                "elements": [{"name": "userId", "type": "string"}]
            }
        ]
    }


@pytest.fixture
def mock_parsed_openapi():
    """Mock parsed OpenAPI data for testing."""
    return {
        "openapi": "3.0.0",
        "info": {
            "title": "Todo API",
            "version": "1.0.0"
        },
        "paths": {
            "/todos": {
                "get": {
                    "summary": "Get todos",
                    "parameters": [
                        {
                            "name": "userId",
                            "in": "query",
                            "required": True,
                            "schema": {"type": "string"}
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "List of todos",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "array",
                                        "items": {"$ref": "#/components/schemas/Todo"}
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        "components": {
            "schemas": {
                "Todo": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "integer"},
                        "title": {"type": "string"},
                        "completed": {"type": "boolean"}
                    }
                }
            }
        }
    }


# Test configuration
def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "airflow: mark test as an Airflow-specific test"
    )
    config.addinivalue_line(
        "markers", "standalone: mark test as a standalone-specific test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers based on file paths."""
    for item in items:
        # Add markers based on file path
        if "test_airflow" in str(item.fspath):
            item.add_marker(pytest.mark.airflow)
        elif "test_standalone" in str(item.fspath):
            item.add_marker(pytest.mark.standalone)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        else:
            item.add_marker(pytest.mark.unit)
