"""
Unit tests for shared.common.data_parsers module.
"""

import pytest
import json
from unittest.mock import patch, mock_open
from shared.common.data_parsers import (
    parse_wsdl_content,
    parse_xsd_content, 
    parse_openapi_content
)


class TestParseWsdlContent:
    """Test WSDL parsing functionality."""
    
    def test_parse_valid_wsdl(self, sample_wsdl_content):
        """Test parsing valid WSDL content."""
        result = parse_wsdl_content(sample_wsdl_content)
        
        assert isinstance(result, dict)
        assert "services" in result
        assert "operations" in result
        assert "messages" in result
        assert "types" in result
        
        # Check that we found some operations
        assert len(result["operations"]) > 0
        
        # Check operation structure
        operation = result["operations"][0]
        assert "name" in operation
        assert "input_message" in operation
        assert "output_message" in operation
    
    def test_parse_invalid_wsdl(self):
        """Test parsing invalid WSDL content."""
        invalid_wsdl = "This is not valid XML"
        result = parse_wsdl_content(invalid_wsdl)
        
        # Should return error structure instead of raising exception
        assert "parsed_wsdl" in result
        assert "error" in result["parsed_wsdl"]
    
    def test_parse_empty_wsdl(self):
        """Test parsing empty WSDL content."""
        result = parse_wsdl_content("")
        
        assert "parsed_wsdl" in result
        assert "error" in result["parsed_wsdl"]
    
    def test_parse_wsdl_with_namespaces(self):
        """Test parsing WSDL with complex namespaces."""
        wsdl_with_ns = """<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:tns="http://example.com/service"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  targetNamespace="http://example.com/service">
    <wsdl:types>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
                    targetNamespace="http://example.com/service">
            <xsd:element name="testElement" type="xsd:string"/>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="testMessage">
        <wsdl:part name="parameters" element="tns:testElement"/>
    </wsdl:message>
    <wsdl:portType name="TestPortType">
        <wsdl:operation name="testOperation">
            <wsdl:input message="tns:testMessage"/>
            <wsdl:output message="tns:testMessage"/>
        </wsdl:operation>
    </wsdl:portType>
</wsdl:definitions>"""
        
        result = parse_wsdl_content(wsdl_with_ns)
        
        assert isinstance(result, dict)
        assert "raw_namespaces" in result
        assert len(result["operations"]) > 0
        assert result["operations"][0]["name"] == "testOperation"


class TestParseXsdContent:
    """Test XSD parsing functionality."""
    
    def test_parse_valid_xsd(self, sample_xsd_content):
        """Test parsing valid XSD content."""
        result = parse_xsd_content(sample_xsd_content)
        
        assert isinstance(result, dict)
        assert "elements" in result
        assert "complex_types" in result
        assert "simple_types" in result
        assert "target_namespace" in result
    
    def test_parse_invalid_xsd(self):
        """Test parsing invalid XSD content."""
        invalid_xsd = "This is not valid XML"
        result = parse_xsd_content(invalid_xsd)
        
        assert "parsed_xsd" in result
        assert "error" in result["parsed_xsd"]
    
    def test_parse_xsd_with_complex_types(self):
        """Test parsing XSD with complex types."""
        xsd_with_complex = """<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://example.com/types"
            xmlns:tns="http://example.com/types">
    <xsd:complexType name="PersonType">
        <xsd:sequence>
            <xsd:element name="name" type="xsd:string"/>
            <xsd:element name="age" type="xsd:int"/>
            <xsd:element name="email" type="xsd:string" minOccurs="0"/>
        </xsd:sequence>
    </xsd:complexType>
    <xsd:element name="Person" type="tns:PersonType"/>
</xsd:schema>"""
        
        result = parse_xsd_content(xsd_with_complex)
        
        assert len(result["complex_types"]) > 0
        complex_type = result["complex_types"][0]
        assert complex_type["name"] == "PersonType"
        assert len(complex_type["elements"]) == 3
    
    def test_parse_xsd_with_simple_types(self):
        """Test parsing XSD with simple types and restrictions."""
        xsd_with_simple = """<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            targetNamespace="http://example.com/types">
    <xsd:simpleType name="StatusType">
        <xsd:restriction base="xsd:string">
            <xsd:enumeration value="active"/>
            <xsd:enumeration value="inactive"/>
            <xsd:enumeration value="pending"/>
        </xsd:restriction>
    </xsd:simpleType>
</xsd:schema>"""
        
        result = parse_xsd_content(xsd_with_simple)
        
        assert len(result["simple_types"]) > 0
        simple_type = result["simple_types"][0]
        assert simple_type["name"] == "StatusType"
        assert len(simple_type["restrictions"]) > 0


class TestParseOpenApiContent:
    """Test OpenAPI parsing functionality."""
    
    def test_parse_valid_openapi_yaml(self, sample_openapi_content):
        """Test parsing valid OpenAPI YAML content."""
        result = parse_openapi_content(sample_openapi_content)
        
        assert isinstance(result, dict)
        assert "openapi" in result or "swagger" in result
        assert "info" in result
        assert "paths" in result
    
    def test_parse_valid_openapi_json(self):
        """Test parsing valid OpenAPI JSON content."""
        openapi_json = json.dumps({
            "openapi": "3.0.0",
            "info": {
                "title": "Test API",
                "version": "1.0.0"
            },
            "paths": {
                "/test": {
                    "get": {
                        "summary": "Test endpoint",
                        "responses": {
                            "200": {
                                "description": "Success"
                            }
                        }
                    }
                }
            }
        })
        
        result = parse_openapi_content(openapi_json)
        
        assert result["openapi"] == "3.0.0"
        assert result["info"]["title"] == "Test API"
        assert "/test" in result["paths"]
    
    def test_parse_swagger_2_0(self):
        """Test parsing Swagger 2.0 specification."""
        swagger_content = """
swagger: "2.0"
info:
  title: "Test API"
  version: "1.0.0"
paths:
  /test:
    get:
      summary: "Test endpoint"
      responses:
        200:
          description: "Success"
"""
        
        result = parse_openapi_content(swagger_content)
        
        assert result["swagger"] == "2.0"
        assert result["info"]["title"] == "Test API"
    
    def test_parse_invalid_openapi(self):
        """Test parsing invalid OpenAPI content."""
        invalid_content = "This is not valid YAML or JSON"
        result = parse_openapi_content(invalid_content)
        
        # Should handle gracefully and return some structure
        assert isinstance(result, dict)
    
    def test_parse_openapi_with_components(self):
        """Test parsing OpenAPI with components/schemas."""
        openapi_with_components = """
openapi: 3.0.0
info:
  title: Test API
  version: 1.0.0
paths:
  /users:
    get:
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/User'
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        email:
          type: string
          format: email
      required:
        - id
        - name
"""
        
        result = parse_openapi_content(openapi_with_components)
        
        assert "components" in result
        assert "schemas" in result["components"]
        assert "User" in result["components"]["schemas"]
        
        user_schema = result["components"]["schemas"]["User"]
        assert user_schema["type"] == "object"
        assert "properties" in user_schema
        assert len(user_schema["properties"]) == 3


class TestDataParsersIntegration:
    """Integration tests for data parsers."""
    
    def test_parse_all_formats(self, sample_wsdl_content, sample_xsd_content, sample_openapi_content):
        """Test parsing all supported formats together."""
        wsdl_result = parse_wsdl_content(sample_wsdl_content)
        xsd_result = parse_xsd_content(sample_xsd_content)
        openapi_result = parse_openapi_content(sample_openapi_content)
        
        # All should parse successfully
        assert isinstance(wsdl_result, dict)
        assert isinstance(xsd_result, dict)
        assert isinstance(openapi_result, dict)
        
        # Check for expected structures
        assert "operations" in wsdl_result
        assert "complex_types" in xsd_result or "elements" in xsd_result
        assert "paths" in openapi_result
    
    @pytest.mark.parametrize("content,parser", [
        ("", parse_wsdl_content),
        ("", parse_xsd_content),
        ("", parse_openapi_content),
        ("invalid xml", parse_wsdl_content),
        ("invalid xml", parse_xsd_content),
        ("invalid yaml/json", parse_openapi_content),
    ])
    def test_parsers_handle_invalid_input_gracefully(self, content, parser):
        """Test that all parsers handle invalid input gracefully."""
        result = parser(content)
        
        # Should not raise exceptions
        assert isinstance(result, dict)
        
        # Should indicate error in some way
        if not content:  # Empty content
            assert any("error" in str(v) for v in result.values()) or len(result) == 0
    
    def test_parser_performance(self, sample_wsdl_content):
        """Test parser performance with timing."""
        import time
        
        start_time = time.time()
        result = parse_wsdl_content(sample_wsdl_content)
        end_time = time.time()
        
        # Should complete within reasonable time (1 second for sample data)
        assert (end_time - start_time) < 1.0
        assert isinstance(result, dict)
