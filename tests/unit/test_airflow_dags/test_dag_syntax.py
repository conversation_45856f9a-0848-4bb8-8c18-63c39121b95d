"""
Unit tests for Airflow DAG syntax and structure validation.
"""

import pytest
import os
import sys
from pathlib import Path
from unittest.mock import patch, Magic<PERSON>ock

# Add airflow to path for testing
try:
    from airflow.models import DagBag
    from airflow.utils.dag_cycle import check_cycle
    AIRFLOW_AVAILABLE = True
except ImportError:
    AIRFLOW_AVAILABLE = False


@pytest.mark.skipif(not AIRFLOW_AVAILABLE, reason="Airflow not available")
class TestDAGSyntax:
    """Test DAG syntax and loading."""
    
    @pytest.fixture
    def dag_folder(self):
        """Return the DAG folder path."""
        return Path(__file__).parent.parent.parent.parent / "airflow" / "dags"
    
    def test_dag_folder_exists(self, dag_folder):
        """Test that DAG folder exists."""
        assert dag_folder.exists(), f"DAG folder not found: {dag_folder}"
        assert dag_folder.is_dir(), f"DAG folder is not a directory: {dag_folder}"
    
    def test_dag_files_exist(self, dag_folder):
        """Test that expected DAG files exist."""
        expected_dags = [
            "01_ingest_and_parse_definitions_dag.py",
            "02_generate_training_data_dag.py", 
            "03_train_ml_model_dag.py",
            "04_operational_mapping_pipeline_dag.py",
            "05_monitoring_and_retraining_trigger_dag.py",
            "06_ingest_operational_logs_dag.py"
        ]
        
        for dag_file in expected_dags:
            dag_path = dag_folder / dag_file
            assert dag_path.exists(), f"DAG file not found: {dag_file}"
    
    @patch('shared.common.constants')
    @patch('shared.common.data_parsers')
    @patch('shared.common.validation_utils')
    def test_dag_syntax_validation(self, mock_validation, mock_parsers, mock_constants, dag_folder):
        """Test that all DAGs can be imported without syntax errors."""
        # Mock the shared modules to avoid import issues during testing
        mock_constants.PARSED_DEFINITIONS_DATASET_URI = "test://parsed_definitions"
        mock_constants.TRAINING_DATA_DATASET_URI = "test://training_data"
        mock_constants.TRAINED_MODEL_DATASET_URI = "test://trained_model"
        mock_constants.OPERATIONAL_MAPPING_DATASET_URI = "test://operational_mapping"
        mock_constants.MONITORING_METRICS_DATASET_URI = "test://monitoring_metrics"
        mock_constants.OPERATIONAL_LOGS_DATASET_URI = "test://operational_logs"
        
        # Set up environment for DAG loading
        os.environ['AIRFLOW__CORE__DAGS_FOLDER'] = str(dag_folder)
        os.environ['AIRFLOW__CORE__LOAD_EXAMPLES'] = 'False'
        
        try:
            dag_bag = DagBag(dag_folder=str(dag_folder), include_examples=False)
            
            # Check for import errors
            if dag_bag.import_errors:
                error_messages = []
                for filename, error in dag_bag.import_errors.items():
                    error_messages.append(f"{filename}: {error}")
                pytest.fail(f"DAG import errors found:\n" + "\n".join(error_messages))
            
            # Verify DAGs were loaded
            assert len(dag_bag.dags) > 0, "No DAGs were loaded"
            
        except Exception as e:
            pytest.fail(f"Failed to load DAGs: {str(e)}")
    
    @patch('shared.common.constants')
    def test_dag_structure_validation(self, mock_constants, dag_folder):
        """Test DAG structure and expected properties."""
        mock_constants.PARSED_DEFINITIONS_DATASET_URI = "test://parsed_definitions"
        mock_constants.TRAINING_DATA_DATASET_URI = "test://training_data"
        mock_constants.TRAINED_MODEL_DATASET_URI = "test://trained_model"
        mock_constants.OPERATIONAL_MAPPING_DATASET_URI = "test://operational_mapping"
        mock_constants.MONITORING_METRICS_DATASET_URI = "test://monitoring_metrics"
        mock_constants.OPERATIONAL_LOGS_DATASET_URI = "test://operational_logs"
        
        os.environ['AIRFLOW__CORE__DAGS_FOLDER'] = str(dag_folder)
        
        try:
            dag_bag = DagBag(dag_folder=str(dag_folder), include_examples=False)
            
            expected_dag_ids = [
                "01_ingest_and_parse_definitions",
                "02_generate_training_data",
                "03_train_ml_model",
                "04_operational_mapping_pipeline",
                "05_monitoring_and_retraining_trigger",
                "06_ingest_operational_logs"
            ]
            
            for dag_id in expected_dag_ids:
                assert dag_id in dag_bag.dags, f"DAG {dag_id} not found in loaded DAGs"
                
                dag = dag_bag.dags[dag_id]
                
                # Check basic DAG properties
                assert dag.dag_id == dag_id
                assert dag.description is not None and len(dag.description) > 0
                assert hasattr(dag, 'default_args')
                assert hasattr(dag, 'tags')
                
                # Check that DAG has tasks
                assert len(dag.tasks) > 0, f"DAG {dag_id} has no tasks"
                
        except Exception as e:
            pytest.fail(f"Failed to validate DAG structure: {str(e)}")
    
    @patch('shared.common.constants')
    def test_dag_cycles(self, mock_constants, dag_folder):
        """Test that DAGs don't have circular dependencies."""
        mock_constants.PARSED_DEFINITIONS_DATASET_URI = "test://parsed_definitions"
        mock_constants.TRAINING_DATA_DATASET_URI = "test://training_data"
        mock_constants.TRAINED_MODEL_DATASET_URI = "test://trained_model"
        mock_constants.OPERATIONAL_MAPPING_DATASET_URI = "test://operational_mapping"
        mock_constants.MONITORING_METRICS_DATASET_URI = "test://monitoring_metrics"
        mock_constants.OPERATIONAL_LOGS_DATASET_URI = "test://operational_logs"
        
        os.environ['AIRFLOW__CORE__DAGS_FOLDER'] = str(dag_folder)
        
        try:
            dag_bag = DagBag(dag_folder=str(dag_folder), include_examples=False)
            
            for dag_id, dag in dag_bag.dags.items():
                # Check for cycles in task dependencies
                try:
                    check_cycle(dag)
                except Exception as e:
                    pytest.fail(f"Cycle detected in DAG {dag_id}: {str(e)}")
                    
        except Exception as e:
            pytest.fail(f"Failed to check DAG cycles: {str(e)}")


class TestDAGImports:
    """Test DAG import statements and dependencies."""
    
    def test_shared_module_imports(self):
        """Test that DAGs correctly import shared modules."""
        dag_folder = Path(__file__).parent.parent.parent.parent / "airflow" / "dags"
        
        for dag_file in dag_folder.glob("*.py"):
            if dag_file.name.startswith("0") and dag_file.name.endswith("_dag.py"):
                with open(dag_file, 'r') as f:
                    content = f.read()
                
                # Check that shared modules are imported correctly
                assert "from shared.common" in content, f"DAG {dag_file.name} doesn't import from shared.common"
                
                # Check that old common imports are not present
                assert "from common import" not in content, f"DAG {dag_file.name} still has old common imports"
                assert "from common." not in content, f"DAG {dag_file.name} still has old common imports"
    
    def test_python_syntax(self):
        """Test Python syntax of all DAG files."""
        import ast
        
        dag_folder = Path(__file__).parent.parent.parent.parent / "airflow" / "dags"
        
        for dag_file in dag_folder.glob("*.py"):
            if dag_file.name.startswith("0") and dag_file.name.endswith("_dag.py"):
                try:
                    with open(dag_file, 'r') as f:
                        content = f.read()
                    
                    # Parse the file to check syntax
                    ast.parse(content)
                    
                except SyntaxError as e:
                    pytest.fail(f"Syntax error in {dag_file.name}: {str(e)}")
                except Exception as e:
                    pytest.fail(f"Error parsing {dag_file.name}: {str(e)}")
    
    def test_required_imports_present(self):
        """Test that required imports are present in DAG files."""
        dag_folder = Path(__file__).parent.parent.parent.parent / "airflow" / "dags"
        
        required_airflow_imports = [
            "from airflow.decorators import dag, task",
            "from airflow.datasets import Dataset"
        ]
        
        for dag_file in dag_folder.glob("*.py"):
            if dag_file.name.startswith("0") and dag_file.name.endswith("_dag.py"):
                with open(dag_file, 'r') as f:
                    content = f.read()
                
                for required_import in required_airflow_imports:
                    assert required_import in content, f"DAG {dag_file.name} missing: {required_import}"


class TestDAGConfiguration:
    """Test DAG configuration and parameters."""
    
    def test_dag_default_args(self):
        """Test that DAGs have proper default arguments."""
        dag_folder = Path(__file__).parent.parent.parent.parent / "airflow" / "dags"
        
        for dag_file in dag_folder.glob("*.py"):
            if dag_file.name.startswith("0") and dag_file.name.endswith("_dag.py"):
                with open(dag_file, 'r') as f:
                    content = f.read()
                
                # Check for common default args
                assert "owner" in content, f"DAG {dag_file.name} missing owner in default_args"
                assert "retries" in content, f"DAG {dag_file.name} missing retries in default_args"
    
    def test_dag_tags(self):
        """Test that DAGs have appropriate tags."""
        dag_folder = Path(__file__).parent.parent.parent.parent / "airflow" / "dags"
        
        for dag_file in dag_folder.glob("*.py"):
            if dag_file.name.startswith("0") and dag_file.name.endswith("_dag.py"):
                with open(dag_file, 'r') as f:
                    content = f.read()
                
                # Check for tags
                assert "tags=" in content, f"DAG {dag_file.name} missing tags"
                assert "llm-mapper" in content, f"DAG {dag_file.name} missing llm-mapper tag"
    
    def test_dag_documentation(self):
        """Test that DAGs have proper documentation."""
        dag_folder = Path(__file__).parent.parent.parent.parent / "airflow" / "dags"
        
        for dag_file in dag_folder.glob("*.py"):
            if dag_file.name.startswith("0") and dag_file.name.endswith("_dag.py"):
                with open(dag_file, 'r') as f:
                    content = f.read()
                
                # Check for docstring at top of file
                lines = content.split('\n')
                found_docstring = False
                for i, line in enumerate(lines[:10]):  # Check first 10 lines
                    if '"""' in line:
                        found_docstring = True
                        break
                
                assert found_docstring, f"DAG {dag_file.name} missing module docstring"
                
                # Check for doc_md parameter
                assert "doc_md=" in content, f"DAG {dag_file.name} missing doc_md parameter"


@pytest.mark.skipif(not AIRFLOW_AVAILABLE, reason="Airflow not available")
class TestDAGExecution:
    """Test DAG execution capabilities (without actually running)."""
    
    @patch('shared.common.constants')
    def test_dag_task_structure(self, mock_constants):
        """Test that DAGs have proper task structure."""
        mock_constants.PARSED_DEFINITIONS_DATASET_URI = "test://parsed_definitions"
        mock_constants.TRAINING_DATA_DATASET_URI = "test://training_data"
        mock_constants.TRAINED_MODEL_DATASET_URI = "test://trained_model"
        mock_constants.OPERATIONAL_MAPPING_DATASET_URI = "test://operational_mapping"
        mock_constants.MONITORING_METRICS_DATASET_URI = "test://monitoring_metrics"
        mock_constants.OPERATIONAL_LOGS_DATASET_URI = "test://operational_logs"
        
        dag_folder = Path(__file__).parent.parent.parent.parent / "airflow" / "dags"
        os.environ['AIRFLOW__CORE__DAGS_FOLDER'] = str(dag_folder)
        
        try:
            dag_bag = DagBag(dag_folder=str(dag_folder), include_examples=False)
            
            for dag_id, dag in dag_bag.dags.items():
                # Check that tasks have proper structure
                for task in dag.tasks:
                    assert hasattr(task, 'task_id')
                    assert hasattr(task, 'dag')
                    assert task.dag == dag
                    
                    # Check task dependencies are properly set
                    if hasattr(task, 'upstream_task_ids'):
                        for upstream_id in task.upstream_task_ids:
                            assert upstream_id in [t.task_id for t in dag.tasks]
                            
        except Exception as e:
            pytest.fail(f"Failed to validate task structure: {str(e)}")
