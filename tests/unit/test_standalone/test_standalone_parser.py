"""
Unit tests for standalone parser functionality.
"""

import pytest
import json
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, mock_open, MagicMock

# Import standalone modules
try:
    from standalone_ingest_parser import (
        parse_arguments,
        validate_inputs,
        process_service_definitions,
        main
    )
    from constants import (
        DEFAULT_OUTPUT_BASE_PATH,
        SUPPORTED_WSDL_VERSIONS,
        SUPPORTED_OPENAPI_VERSIONS
    )
    from data_parsers import parse_wsdl_file, parse_openapi_file
    from validation_utils import validate_file_exists, validate_output_path
    STANDALONE_AVAILABLE = True
except ImportError:
    STANDALONE_AVAILABLE = False


@pytest.mark.skipif(not STANDALONE_AVAILABLE, reason="Standalone modules not available")
class TestStandaloneParser:
    """Test standalone parser main functionality."""
    
    def test_parse_arguments_minimal(self):
        """Test parsing minimal required arguments."""
        args = parse_arguments([
            "--service-name", "test_service",
            "--wsdl-uri", "test.wsdl",
            "--openapi-uri", "test.yaml"
        ])
        
        assert args.service_name == "test_service"
        assert args.wsdl_uri == "test.wsdl"
        assert args.openapi_uri == "test.yaml"
        assert args.output_base_path == DEFAULT_OUTPUT_BASE_PATH
    
    def test_parse_arguments_full(self):
        """Test parsing all arguments."""
        args = parse_arguments([
            "--service-name", "test_service",
            "--wsdl-uri", "test.wsdl",
            "--xsd-uri", "test.xsd",
            "--openapi-uri", "test.yaml",
            "--output-base-path", "/custom/output",
            "--log-level", "DEBUG",
            "--log-file", "custom.log",
            "--include-type-definitions",
            "--include-java-generation"
        ])
        
        assert args.service_name == "test_service"
        assert args.wsdl_uri == "test.wsdl"
        assert args.xsd_uri == "test.xsd"
        assert args.openapi_uri == "test.yaml"
        assert args.output_base_path == "/custom/output"
        assert args.log_level == "DEBUG"
        assert args.log_file == "custom.log"
        assert args.include_type_definitions is True
        assert args.include_java_generation is True
    
    def test_parse_arguments_missing_required(self):
        """Test that missing required arguments raise error."""
        with pytest.raises(SystemExit):
            parse_arguments(["--service-name", "test"])
    
    def test_validate_inputs_valid(self, temp_directory):
        """Test input validation with valid inputs."""
        # Create temporary files
        wsdl_file = Path(temp_directory) / "test.wsdl"
        openapi_file = Path(temp_directory) / "test.yaml"
        
        wsdl_file.write_text("<?xml version='1.0'?><definitions/>")
        openapi_file.write_text("openapi: 3.0.0\ninfo:\n  title: Test")
        
        # Mock arguments
        args = MagicMock()
        args.service_name = "test_service"
        args.wsdl_uri = str(wsdl_file)
        args.xsd_uri = None
        args.openapi_uri = str(openapi_file)
        args.output_base_path = temp_directory
        
        # Should not raise exception
        validate_inputs(args)
    
    def test_validate_inputs_missing_files(self):
        """Test input validation with missing files."""
        args = MagicMock()
        args.service_name = "test_service"
        args.wsdl_uri = "/nonexistent/file.wsdl"
        args.xsd_uri = None
        args.openapi_uri = "/nonexistent/file.yaml"
        args.output_base_path = "/tmp"
        
        with pytest.raises(FileNotFoundError):
            validate_inputs(args)
    
    def test_validate_inputs_invalid_service_name(self, temp_directory):
        """Test input validation with invalid service name."""
        args = MagicMock()
        args.service_name = "invalid-service-name!"  # Invalid characters
        args.wsdl_uri = "test.wsdl"
        args.openapi_uri = "test.yaml"
        args.output_base_path = temp_directory
        
        with pytest.raises(ValueError):
            validate_inputs(args)


@pytest.mark.skipif(not STANDALONE_AVAILABLE, reason="Standalone modules not available")
class TestStandaloneDataParsers:
    """Test standalone data parsing functions."""
    
    @patch('builtins.open', new_callable=mock_open, read_data='<?xml version="1.0"?><definitions/>')
    def test_parse_wsdl_file(self, mock_file):
        """Test WSDL file parsing."""
        result = parse_wsdl_file("test.wsdl")
        
        assert isinstance(result, dict)
        mock_file.assert_called_once_with("test.wsdl", 'r', encoding='utf-8')
    
    @patch('builtins.open', new_callable=mock_open, read_data='openapi: 3.0.0\ninfo:\n  title: Test')
    def test_parse_openapi_file(self, mock_file):
        """Test OpenAPI file parsing."""
        result = parse_openapi_file("test.yaml")
        
        assert isinstance(result, dict)
        mock_file.assert_called_once_with("test.yaml", 'r', encoding='utf-8')
    
    def test_parse_wsdl_file_not_found(self):
        """Test WSDL file parsing with missing file."""
        with pytest.raises(FileNotFoundError):
            parse_wsdl_file("/nonexistent/file.wsdl")
    
    def test_parse_openapi_file_not_found(self):
        """Test OpenAPI file parsing with missing file."""
        with pytest.raises(FileNotFoundError):
            parse_openapi_file("/nonexistent/file.yaml")


@pytest.mark.skipif(not STANDALONE_AVAILABLE, reason="Standalone modules not available")
class TestStandaloneValidation:
    """Test standalone validation utilities."""
    
    def test_validate_file_exists_valid(self, temp_directory):
        """Test file existence validation with valid file."""
        test_file = Path(temp_directory) / "test.txt"
        test_file.write_text("test content")
        
        # Should not raise exception
        validate_file_exists(str(test_file))
    
    def test_validate_file_exists_missing(self):
        """Test file existence validation with missing file."""
        with pytest.raises(FileNotFoundError):
            validate_file_exists("/nonexistent/file.txt")
    
    def test_validate_output_path_valid(self, temp_directory):
        """Test output path validation with valid directory."""
        # Should not raise exception
        validate_output_path(temp_directory)
    
    def test_validate_output_path_create_directory(self, temp_directory):
        """Test output path validation with directory creation."""
        new_dir = Path(temp_directory) / "new_output_dir"
        
        # Should create directory and not raise exception
        validate_output_path(str(new_dir))
        assert new_dir.exists()
        assert new_dir.is_dir()


@pytest.mark.skipif(not STANDALONE_AVAILABLE, reason="Standalone modules not available")
class TestStandaloneProcessing:
    """Test standalone service processing functionality."""
    
    @patch('standalone_ingest_parser.parse_wsdl_file')
    @patch('standalone_ingest_parser.parse_openapi_file')
    @patch('standalone_ingest_parser.save_parsed_output')
    def test_process_service_definitions(self, mock_save, mock_parse_openapi, mock_parse_wsdl, temp_directory):
        """Test complete service definition processing."""
        # Mock parsing results
        mock_parse_wsdl.return_value = {
            "services": [{"name": "TestService"}],
            "operations": [{"name": "testOperation"}]
        }
        mock_parse_openapi.return_value = {
            "openapi": "3.0.0",
            "paths": {"/test": {"get": {"summary": "Test"}}}
        }
        
        # Mock arguments
        args = MagicMock()
        args.service_name = "test_service"
        args.wsdl_uri = "test.wsdl"
        args.xsd_uri = None
        args.openapi_uri = "test.yaml"
        args.output_base_path = temp_directory
        args.include_type_definitions = True
        args.include_java_generation = False
        
        # Process service definitions
        result = process_service_definitions(args)
        
        # Verify parsing was called
        mock_parse_wsdl.assert_called_once_with("test.wsdl")
        mock_parse_openapi.assert_called_once_with("test.yaml")
        
        # Verify save was called
        assert mock_save.call_count >= 2  # At least WSDL and OpenAPI
        
        # Verify result structure
        assert isinstance(result, dict)
        assert "wsdl_parsed" in result
        assert "openapi_parsed" in result
    
    @patch('standalone_ingest_parser.parse_wsdl_file')
    def test_process_service_definitions_wsdl_only(self, mock_parse_wsdl, temp_directory):
        """Test processing with only WSDL file."""
        mock_parse_wsdl.return_value = {"services": []}
        
        args = MagicMock()
        args.service_name = "test_service"
        args.wsdl_uri = "test.wsdl"
        args.xsd_uri = None
        args.openapi_uri = None
        args.output_base_path = temp_directory
        args.include_type_definitions = False
        args.include_java_generation = False
        
        result = process_service_definitions(args)
        
        mock_parse_wsdl.assert_called_once()
        assert "wsdl_parsed" in result
        assert "openapi_parsed" not in result


@pytest.mark.skipif(not STANDALONE_AVAILABLE, reason="Standalone modules not available")
class TestStandaloneIntegration:
    """Integration tests for standalone parser."""
    
    @patch('standalone_ingest_parser.validate_inputs')
    @patch('standalone_ingest_parser.process_service_definitions')
    @patch('standalone_ingest_parser.setup_logging')
    def test_main_function_success(self, mock_logging, mock_process, mock_validate):
        """Test main function with successful execution."""
        # Mock successful processing
        mock_process.return_value = {
            "wsdl_parsed": {"services": []},
            "openapi_parsed": {"paths": {}}
        }
        
        # Test arguments
        test_args = [
            "--service-name", "test_service",
            "--wsdl-uri", "test.wsdl",
            "--openapi-uri", "test.yaml"
        ]
        
        # Should complete without exception
        with patch('sys.argv', ['standalone_ingest_parser.py'] + test_args):
            result = main()
            
        mock_validate.assert_called_once()
        mock_process.assert_called_once()
        assert result == 0  # Success exit code
    
    @patch('standalone_ingest_parser.validate_inputs')
    def test_main_function_validation_error(self, mock_validate):
        """Test main function with validation error."""
        # Mock validation error
        mock_validate.side_effect = ValueError("Invalid input")
        
        test_args = [
            "--service-name", "test_service",
            "--wsdl-uri", "test.wsdl",
            "--openapi-uri", "test.yaml"
        ]
        
        with patch('sys.argv', ['standalone_ingest_parser.py'] + test_args):
            result = main()
            
        assert result == 1  # Error exit code
    
    def test_end_to_end_with_sample_data(self, sample_wsdl_content, sample_openapi_content, temp_directory):
        """Test end-to-end processing with sample data."""
        # Create temporary files with sample content
        wsdl_file = Path(temp_directory) / "test.wsdl"
        openapi_file = Path(temp_directory) / "test.yaml"
        
        wsdl_file.write_text(sample_wsdl_content)
        openapi_file.write_text(sample_openapi_content)
        
        # Test arguments
        test_args = [
            "--service-name", "test_service",
            "--wsdl-uri", str(wsdl_file),
            "--openapi-uri", str(openapi_file),
            "--output-base-path", temp_directory
        ]
        
        with patch('sys.argv', ['standalone_ingest_parser.py'] + test_args):
            result = main()
        
        # Should complete successfully
        assert result == 0
        
        # Check output files were created
        output_dir = Path(temp_directory) / "test_service"
        assert output_dir.exists()
        
        # Check for expected output files
        expected_files = [
            "wsdl_parsed.json",
            "openapi_parsed.json",
            "service_metadata.json"
        ]
        
        for expected_file in expected_files:
            output_file = output_dir / expected_file
            if output_file.exists():  # Some files might be optional
                # Verify it's valid JSON
                with open(output_file) as f:
                    json.load(f)  # Should not raise exception


@pytest.mark.skipif(not STANDALONE_AVAILABLE, reason="Standalone modules not available")
class TestStandaloneConstants:
    """Test standalone constants and configuration."""
    
    def test_constants_defined(self):
        """Test that required constants are defined."""
        assert DEFAULT_OUTPUT_BASE_PATH is not None
        assert isinstance(SUPPORTED_WSDL_VERSIONS, (list, tuple))
        assert isinstance(SUPPORTED_OPENAPI_VERSIONS, (list, tuple))
        assert len(SUPPORTED_WSDL_VERSIONS) > 0
        assert len(SUPPORTED_OPENAPI_VERSIONS) > 0
    
    def test_supported_versions(self):
        """Test that supported versions are reasonable."""
        # WSDL versions
        assert "1.1" in SUPPORTED_WSDL_VERSIONS
        assert "2.0" in SUPPORTED_WSDL_VERSIONS
        
        # OpenAPI versions
        assert any(v.startswith("3.") for v in SUPPORTED_OPENAPI_VERSIONS)


class TestStandaloneErrorHandling:
    """Test error handling in standalone parser."""
    
    @pytest.mark.skipif(not STANDALONE_AVAILABLE, reason="Standalone modules not available")
    def test_graceful_error_handling(self):
        """Test that errors are handled gracefully."""
        # Test with invalid arguments
        with patch('sys.argv', ['standalone_ingest_parser.py', '--invalid-arg']):
            with pytest.raises(SystemExit):
                main()
    
    @pytest.mark.skipif(not STANDALONE_AVAILABLE, reason="Standalone modules not available")
    @patch('standalone_ingest_parser.process_service_definitions')
    def test_processing_error_handling(self, mock_process):
        """Test handling of processing errors."""
        # Mock processing error
        mock_process.side_effect = Exception("Processing failed")
        
        test_args = [
            "--service-name", "test_service",
            "--wsdl-uri", "test.wsdl",
            "--openapi-uri", "test.yaml"
        ]
        
        with patch('sys.argv', ['standalone_ingest_parser.py'] + test_args):
            result = main()
        
        # Should return error code but not crash
        assert result == 1
