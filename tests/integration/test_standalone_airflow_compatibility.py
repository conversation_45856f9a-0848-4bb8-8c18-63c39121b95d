"""
Integration tests for standalone and Airflow compatibility.
Tests that shared modules work consistently across both deployment modes.
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock

# Import shared modules
from shared.common import data_parsers, validation_utils, constants
from shared.common.definition_store import DefinitionStore


class TestSharedModuleCompatibility:
    """Test that shared modules work consistently across standalone and Airflow."""
    
    def test_data_parsers_consistency(self, sample_wsdl_content, sample_openapi_content):
        """Test that data parsers produce consistent results."""
        # Parse WSDL
        wsdl_result = data_parsers.parse_wsdl_content(sample_wsdl_content)
        assert isinstance(wsdl_result, dict)
        assert "operations" in wsdl_result
        
        # Parse OpenAPI
        openapi_result = data_parsers.parse_openapi_content(sample_openapi_content)
        assert isinstance(openapi_result, dict)
        assert "paths" in openapi_result
        
        # Results should be serializable (for Airflow XCom)
        json.dumps(wsdl_result)
        json.dumps(openapi_result)
    
    def test_validation_utils_consistency(self, temp_directory):
        """Test that validation utilities work consistently."""
        # Create test file
        test_file = Path(temp_directory) / "test.txt"
        test_file.write_text("test content")
        
        # Test file validation
        result = validation_utils.validate_parsed_data_schema(
            {"test": "data"}, 
            "test_schema"
        )
        assert isinstance(result, bool)
    
    def test_constants_accessibility(self):
        """Test that constants are accessible from both contexts."""
        # Test that key constants exist
        assert hasattr(constants, 'LOCAL_DATA_PATH')
        assert hasattr(constants, 'PROJECT_VERSION')
        
        # Constants should be strings or basic types
        assert isinstance(constants.PROJECT_VERSION, str)
        assert isinstance(constants.LOCAL_DATA_PATH, str)


class TestDefinitionStoreCompatibility:
    """Test DefinitionStore compatibility between standalone and Airflow."""
    
    @patch('shared.common.definition_store.Variable')
    def test_definition_store_airflow_mode(self, mock_variable):
        """Test DefinitionStore in Airflow mode (with Variables)."""
        # Mock Airflow Variable
        mock_catalog = {
            "services": {
                "test_service": {
                    "service_name": "test_service",
                    "wsdl_path": "/test/service.wsdl"
                }
            }
        }
        mock_variable.get.return_value = mock_catalog
        
        # Test catalog retrieval
        catalog = DefinitionStore.get_service_catalog()
        assert isinstance(catalog, dict)
        assert "services" in catalog
        
        # Test service listing
        services = DefinitionStore.list_available_services()
        assert isinstance(services, list)
        assert "test_service" in services
    
    @patch('shared.common.definition_store.Variable')
    def test_definition_store_standalone_mode(self, mock_variable):
        """Test DefinitionStore in standalone mode (without Variables)."""
        # Mock Variable not found (standalone mode)
        mock_variable.get.side_effect = KeyError("Variable not found")
        
        # Should return empty catalog gracefully
        catalog = DefinitionStore.get_service_catalog()
        assert isinstance(catalog, dict)
        assert catalog["services"] == {}
        
        # Should return empty service list
        services = DefinitionStore.list_available_services()
        assert isinstance(services, list)
        assert len(services) == 0


class TestDataFormatCompatibility:
    """Test data format compatibility between standalone and Airflow."""
    
    def test_parsed_output_format(self, sample_wsdl_content, sample_openapi_content):
        """Test that parsed output format is consistent."""
        # Parse using shared modules
        wsdl_parsed = data_parsers.parse_wsdl_content(sample_wsdl_content)
        openapi_parsed = data_parsers.parse_openapi_content(sample_openapi_content)
        
        # Create service definition structure
        service_definition = {
            "service_name": "test_service",
            "wsdl_parsed": wsdl_parsed,
            "openapi_parsed": openapi_parsed,
            "metadata": {
                "created_at": "2024-01-01T00:00:00Z",
                "parser_version": constants.PROJECT_VERSION
            }
        }
        
        # Should be JSON serializable (for file storage and XCom)
        serialized = json.dumps(service_definition)
        deserialized = json.loads(serialized)
        
        # Structure should be preserved
        assert deserialized["service_name"] == "test_service"
        assert "wsdl_parsed" in deserialized
        assert "openapi_parsed" in deserialized
        assert "metadata" in deserialized
    
    def test_training_data_format(self, mock_training_data):
        """Test that training data format is compatible."""
        # Training data should be JSON serializable
        serialized = json.dumps(mock_training_data)
        deserialized = json.loads(serialized)
        
        # Structure should be preserved
        assert "examples" in deserialized
        assert "metadata" in deserialized
        assert len(deserialized["examples"]) > 0
        
        # Example structure should be consistent
        example = deserialized["examples"][0]
        assert "id" in example
        assert "source_type" in example
        assert "input" in example
        assert "target" in example


class TestFileSystemCompatibility:
    """Test file system operations compatibility."""
    
    def test_output_path_handling(self, temp_directory):
        """Test that output path handling is consistent."""
        # Test path creation and validation
        service_output_path = Path(temp_directory) / "test_service"
        service_output_path.mkdir(exist_ok=True)
        
        # Test file writing (standalone style)
        wsdl_output = service_output_path / "wsdl_parsed.json"
        test_data = {"test": "data"}
        
        with open(wsdl_output, 'w') as f:
            json.dump(test_data, f, indent=2)
        
        # Test file reading (Airflow style)
        with open(wsdl_output, 'r') as f:
            loaded_data = json.load(f)
        
        assert loaded_data == test_data
    
    def test_path_resolution(self, temp_directory):
        """Test that path resolution works consistently."""
        # Test relative path handling
        base_path = Path(temp_directory)
        relative_path = "data/parsed_definitions/test_service"
        
        # Resolve path consistently
        full_path = base_path / relative_path
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        assert full_path.parent.exists()
        assert full_path.parent.is_dir()


class TestErrorHandlingCompatibility:
    """Test error handling compatibility between modes."""
    
    def test_parsing_error_handling(self):
        """Test that parsing errors are handled consistently."""
        # Test with invalid content
        invalid_wsdl = "This is not valid XML"
        invalid_openapi = "This is not valid YAML or JSON"
        
        # Both should handle errors gracefully
        wsdl_result = data_parsers.parse_wsdl_content(invalid_wsdl)
        openapi_result = data_parsers.parse_openapi_content(invalid_openapi)
        
        # Should return error structures, not raise exceptions
        assert isinstance(wsdl_result, dict)
        assert isinstance(openapi_result, dict)
        
        # Should be JSON serializable even with errors
        json.dumps(wsdl_result)
        json.dumps(openapi_result)
    
    def test_file_not_found_handling(self):
        """Test file not found error handling."""
        # This should be handled consistently in both modes
        with pytest.raises(FileNotFoundError):
            with open("/nonexistent/file.txt", 'r') as f:
                f.read()


class TestConfigurationCompatibility:
    """Test configuration compatibility between modes."""
    
    def test_constants_values(self):
        """Test that constants have reasonable values."""
        # Test data paths
        assert constants.LOCAL_DATA_PATH.startswith('/')
        assert 'airflow' in constants.LOCAL_DATA_PATH.lower()
        
        # Test version info
        assert len(constants.PROJECT_VERSION.split('.')) >= 2
        
        # Test feature flags
        assert isinstance(constants.ENABLE_JAVA_CODE_GENERATION, bool)
        assert isinstance(constants.ENABLE_ADVANCED_HEURISTICS, bool)
    
    def test_dataset_uris(self):
        """Test that dataset URIs are properly formatted."""
        # Test dataset URI format
        assert constants.PARSED_DEFINITIONS_DATASET_URI.startswith('data/')
        assert constants.TRAINING_DATA_DATASET_URI.startswith('data/')
        
        # Should be valid URI format
        assert '://' not in constants.PARSED_DEFINITIONS_DATASET_URI or \
               constants.PARSED_DEFINITIONS_DATASET_URI.startswith('data://')


class TestPerformanceCompatibility:
    """Test performance characteristics are similar between modes."""
    
    def test_parsing_performance(self, sample_wsdl_content, sample_openapi_content):
        """Test that parsing performance is reasonable in both modes."""
        import time
        
        # Time WSDL parsing
        start_time = time.time()
        wsdl_result = data_parsers.parse_wsdl_content(sample_wsdl_content)
        wsdl_time = time.time() - start_time
        
        # Time OpenAPI parsing
        start_time = time.time()
        openapi_result = data_parsers.parse_openapi_content(sample_openapi_content)
        openapi_time = time.time() - start_time
        
        # Should complete within reasonable time
        assert wsdl_time < 5.0  # 5 seconds max
        assert openapi_time < 5.0  # 5 seconds max
        
        # Results should be valid
        assert isinstance(wsdl_result, dict)
        assert isinstance(openapi_result, dict)
    
    def test_memory_usage_reasonable(self, sample_wsdl_content, sample_openapi_content):
        """Test that memory usage is reasonable."""
        import sys
        
        # Get initial memory usage
        initial_size = sys.getsizeof(sample_wsdl_content) + sys.getsizeof(sample_openapi_content)
        
        # Parse content
        wsdl_result = data_parsers.parse_wsdl_content(sample_wsdl_content)
        openapi_result = data_parsers.parse_openapi_content(sample_openapi_content)
        
        # Get result size
        result_size = sys.getsizeof(str(wsdl_result)) + sys.getsizeof(str(openapi_result))
        
        # Result should not be excessively larger than input
        # Allow up to 10x expansion for structured data
        assert result_size < initial_size * 10


class TestIntegrationWorkflow:
    """Test complete integration workflow."""
    
    def test_end_to_end_compatibility(self, sample_wsdl_content, sample_openapi_content, temp_directory):
        """Test end-to-end workflow compatibility."""
        # Step 1: Parse definitions (shared by both modes)
        wsdl_parsed = data_parsers.parse_wsdl_content(sample_wsdl_content)
        openapi_parsed = data_parsers.parse_openapi_content(sample_openapi_content)
        
        # Step 2: Create service definition
        service_definition = {
            "service_name": "integration_test",
            "wsdl_parsed": wsdl_parsed,
            "openapi_parsed": openapi_parsed,
            "metadata": {
                "created_at": "2024-01-01T00:00:00Z",
                "source": "integration_test"
            }
        }
        
        # Step 3: Save to file (standalone mode)
        output_file = Path(temp_directory) / "service_definition.json"
        with open(output_file, 'w') as f:
            json.dump(service_definition, f, indent=2)
        
        # Step 4: Load from file (Airflow mode)
        with open(output_file, 'r') as f:
            loaded_definition = json.load(f)
        
        # Step 5: Validate consistency
        assert loaded_definition == service_definition
        assert loaded_definition["service_name"] == "integration_test"
        assert "wsdl_parsed" in loaded_definition
        assert "openapi_parsed" in loaded_definition
        
        # Step 6: Validate structure for downstream processing
        assert "operations" in loaded_definition["wsdl_parsed"]
        assert "paths" in loaded_definition["openapi_parsed"]
