{"version": "v1", "description": "Initial set of heuristic rules for WSDL to OpenAPI mapping.", "rules": [{"name": "default_description_if_missing_in_llm_output", "condition_on_llm_output": {"field_exists": "openapi_path_object", "field_not_exists": "openapi_path_object.description"}, "action": "add_field", "target_field": "openapi_path_object.description", "value": "Default description added by heuristic rule."}, {"name": "map_xsd_string_to_openapi_string", "condition_on_parsed_wsdl": {"type_name_match_pattern": ".*String$", "type_category": "simpleXsdType"}, "action": "suggest_openapi_type", "value": {"type": "string", "format": "default"}}]}