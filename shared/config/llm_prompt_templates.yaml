# include/config/llm_prompt_templates.yaml
default_mapping_prompt: |
  You are an expert API migration assistant.
  Given the following WSDL/XSD service definition excerpts and a target OpenAPI context,
  please describe how the specified WSDL operation or type would be best represented in the OpenAPI specification.

  WSDL Schemas Context:
  {wsdl_schemas}

  WSDL Operations Context:
  {wsdl_operations}

  Target OpenAPI Schemas Context (for reference):
  {openapi_schemas_context}

  Target OpenAPI Paths Context (for reference):
  {openapi_paths_context}

  Specific Task:
  How would the WSDL operation named '{{ specific_wsdl_operation_name }}' (details provided above)
  be mapped to an OpenAPI path, HTTP method, request body, and responses?
  Focus on semantic equivalence and RESTful best practices.

schema_transformation_prompt: |
  You are an expert data modeler.
  Transform the following WSDL/XSD type definition into an equivalent OpenAPI schema object.
  Pay attention to data types, constraints, and naming conventions.

  WSDL/XSD Type Definition:
  {wsdl_type_definition_json}

  Provide the OpenAPI schema object in JSON format.