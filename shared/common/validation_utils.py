# dags/common/validation_utils.py
import json
from typing import Dict, Any, List, Optional

def validate_parsed_data_schema(data: dict, expected_schema_type: str) -> bool:
    """
    Validates if parsed data conforms to an expected basic structure.

    Args:
        data: The parsed data to validate
        expected_schema_type: Type of schema to validate against ("wsdl_parsed", "xsd_parsed", "openapi_parsed")

    Returns:
        bool: True if validation passes, False otherwise
    """
    print(f"Validating schema for type: {expected_schema_type}...")

    if expected_schema_type == "wsdl_parsed":
        # Check for required WSDL structure
        if not isinstance(data, dict):
            print("Error: WSDL data must be a dictionary")
            return False

        # Check for required keys
        required_keys = ["services", "operations", "messages"]
        for key in required_keys:
            if key not in data:
                print(f"Error: Missing required key '{key}' in WSDL data")
                return False
            if not isinstance(data[key], list):
                print(f"Error: '{key}' must be a list in WSDL data")
                return False

        # Check if operations have required fields
        for op in data["operations"]:
            if not isinstance(op, dict) or "name" not in op:
                print("Error: Each operation must be a dictionary with a 'name' field")
                return False

        return True

    elif expected_schema_type == "xsd_parsed":
        # Check for required XSD structure
        if not isinstance(data, dict):
            print("Error: XSD data must be a dictionary")
            return False

        # Check for required keys
        if "elements" not in data or not isinstance(data["elements"], list):
            print("Error: Missing or invalid 'elements' key in XSD data")
            return False

        if "complex_types" not in data or not isinstance(data["complex_types"], list):
            print("Error: Missing or invalid 'complex_types' key in XSD data")
            return False

        return True

    elif expected_schema_type == "openapi_parsed":
        # Check for required OpenAPI structure
        if not isinstance(data, dict):
            print("Error: OpenAPI data must be a dictionary")
            return False

        # Check for full_spec or extracted
        if "full_spec" not in data and "extracted" not in data:
            # Legacy check for older parser versions
            return "openapi" in data or ("paths" in data and "components" in data)

        if "full_spec" in data:
            spec = data["full_spec"]
            # Check for OpenAPI version
            if "openapi" not in spec and "swagger" not in spec:
                print("Error: Missing OpenAPI/Swagger version")
                return False

            # Check for required sections
            if "paths" not in spec:
                print("Error: Missing 'paths' section in OpenAPI spec")
                return False

        return True

    print(f"Warning: Unknown schema type '{expected_schema_type}'")
    return True  # Default to true for unknown types

def detect_mapping_deviations(mapped_output: dict, golden_dataset_stats_path: str = None) -> dict:
    """
    Detects deviations in the final mapped output compared to expected patterns or a golden dataset.

    Args:
        mapped_output: The mapping output to validate
        golden_dataset_stats_path: Optional path to golden dataset statistics for comparison

    Returns:
        dict: Report of deviations found
    """
    deviations_report = {"issues_found": 0, "warnings": [], "errors": []}
    print("Detecting deviations in mapped output...")

    # Check for required sections in mapped output
    if not mapped_output.get("final_openapi_spec"):
        deviations_report["errors"].append("Final OpenAPI spec is missing.")
        deviations_report["issues_found"] += 1

    # Check OpenAPI spec structure if present
    if "final_openapi_spec" in mapped_output:
        spec = mapped_output["final_openapi_spec"]

        # Check OpenAPI version
        if "openapi" not in spec:
            deviations_report["errors"].append("OpenAPI version field is missing.")
            deviations_report["issues_found"] += 1

        # Check for empty paths
        if not spec.get("paths"):
            deviations_report["warnings"].append("OpenAPI spec has no paths defined.")
            deviations_report["issues_found"] += 1

        # Check for components/schemas
        if not spec.get("components", {}).get("schemas"):
            deviations_report["warnings"].append("OpenAPI spec has no schemas defined in components.")
            deviations_report["issues_found"] += 1

    # Compare with golden dataset if provided
    if golden_dataset_stats_path:
        try:
            with open(golden_dataset_stats_path, 'r') as f:
                golden_stats = json.load(f)

            # Compare number of paths
            if "paths_count" in golden_stats:
                actual_paths = len(mapped_output.get("final_openapi_spec", {}).get("paths", {}))
                if actual_paths < golden_stats["paths_count"] * 0.8:  # Allow 20% deviation
                    deviations_report["warnings"].append(
                        f"Mapped output has significantly fewer paths ({actual_paths}) than expected ({golden_stats['paths_count']})."
                    )
                    deviations_report["issues_found"] += 1

            # Compare number of schemas
            if "schemas_count" in golden_stats:
                actual_schemas = len(mapped_output.get("final_openapi_spec", {}).get("components", {}).get("schemas", {}))
                if actual_schemas < golden_stats["schemas_count"] * 0.8:  # Allow 20% deviation
                    deviations_report["warnings"].append(
                        f"Mapped output has significantly fewer schemas ({actual_schemas}) than expected ({golden_stats['schemas_count']})."
                    )
                    deviations_report["issues_found"] += 1
        except Exception as e:
            deviations_report["warnings"].append(f"Could not compare with golden dataset: {e}")

    print(f"Deviation detection complete. Issues found: {deviations_report['issues_found']}")
    return deviations_report