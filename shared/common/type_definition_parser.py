# shared/common/type_definition_parser.py
"""
Type Definition Parser Module

Extracts and normalizes type definitions from WSDL, XSD, and OpenAPI files
into a common format suitable for ML training data preparation.
"""

import json
from typing import Dict, List, Any, Optional
from lxml import etree
import pendulum


def extract_wsdl_types(wsdl_content: str) -> Dict[str, Any]:
    """
    Extract type definitions from WSDL content.
    
    Args:
        wsdl_content: Raw WSDL content as string
        
    Returns:
        Dictionary containing normalized type definitions from WSDL
    """
    try:
        root = etree.fromstring(wsdl_content.encode('utf-8'))
        nsmap = root.nsmap.copy() if root.nsmap else {}
        
        # Handle default namespace
        if None in nsmap:
            if nsmap[None] == "http://schemas.xmlsoap.org/wsdl/":
                nsmap['wsdl'] = nsmap[None]
            else:
                nsmap['default'] = nsmap[None]
            del nsmap[None]
        
        # Ensure required namespaces
        if 'wsdl' not in nsmap:
            nsmap['wsdl'] = "http://schemas.xmlsoap.org/wsdl/"
        if 'xs' not in nsmap and 'xsd' not in nsmap:
            nsmap['xs'] = "http://www.w3.org/2001/XMLSchema"
        
        types = []
        target_namespace = root.get('targetNamespace', '')
        
        # Extract types from wsdl:types section
        types_section = root.xpath('.//wsdl:types', namespaces=nsmap)
        if types_section:
            schemas = types_section[0].xpath('.//xs:schema|.//xsd:schema', namespaces=nsmap)
            for schema in schemas:
                schema_namespace = schema.get('targetNamespace', target_namespace)
                
                # Extract complex types
                complex_types = schema.xpath('.//xs:complexType|.//xsd:complexType', namespaces=nsmap)
                for ct in complex_types:
                    type_def = {
                        "name": ct.get('name'),
                        "category": "complex",
                        "namespace": schema_namespace,
                        "properties": [],
                        "restrictions": [],
                        "documentation": _extract_documentation(ct, nsmap),
                        "source_location": f"wsdl:types/schema[@targetNamespace='{schema_namespace}']"
                    }
                    
                    # Extract sequence elements
                    sequences = ct.xpath('.//xs:sequence|.//xsd:sequence', namespaces=nsmap)
                    for seq in sequences:
                        elements = seq.xpath('.//xs:element|.//xsd:element', namespaces=nsmap)
                        for elem in elements:
                            prop = {
                                "name": elem.get('name'),
                                "type": elem.get('type'),
                                "min_occurs": elem.get('minOccurs', '1'),
                                "max_occurs": elem.get('maxOccurs', '1'),
                                "documentation": _extract_documentation(elem, nsmap)
                            }
                            type_def["properties"].append(prop)
                    
                    types.append(type_def)
                
                # Extract simple types
                simple_types = schema.xpath('.//xs:simpleType|.//xsd:simpleType', namespaces=nsmap)
                for st in simple_types:
                    type_def = {
                        "name": st.get('name'),
                        "category": "simple",
                        "namespace": schema_namespace,
                        "properties": [],
                        "restrictions": [],
                        "documentation": _extract_documentation(st, nsmap),
                        "source_location": f"wsdl:types/schema[@targetNamespace='{schema_namespace}']"
                    }
                    
                    # Extract restrictions
                    restrictions = st.xpath('.//xs:restriction|.//xsd:restriction', namespaces=nsmap)
                    for restr in restrictions:
                        restriction_data = {
                            "base": restr.get('base'),
                            "facets": []
                        }
                        
                        # Extract facets (enumeration, pattern, etc.)
                        facets = restr.xpath('.//xs:enumeration|.//xsd:enumeration|.//xs:pattern|.//xsd:pattern|.//xs:minLength|.//xsd:minLength|.//xs:maxLength|.//xsd:maxLength', namespaces=nsmap)
                        for facet in facets:
                            facet_data = {
                                "type": facet.tag.split('}')[-1] if '}' in facet.tag else facet.tag,
                                "value": facet.get('value')
                            }
                            restriction_data["facets"].append(facet_data)
                        
                        type_def["restrictions"].append(restriction_data)
                    
                    types.append(type_def)
                
                # Extract top-level elements
                elements = schema.xpath('./xs:element|./xsd:element', namespaces=nsmap)
                for elem in elements:
                    type_def = {
                        "name": elem.get('name'),
                        "category": "element",
                        "namespace": schema_namespace,
                        "properties": [{
                            "type": elem.get('type'),
                            "min_occurs": elem.get('minOccurs', '1'),
                            "max_occurs": elem.get('maxOccurs', '1')
                        }],
                        "restrictions": [],
                        "documentation": _extract_documentation(elem, nsmap),
                        "source_location": f"wsdl:types/schema[@targetNamespace='{schema_namespace}']"
                    }
                    types.append(type_def)
        
        return {
            "source_format": "wsdl",
            "target_namespace": target_namespace,
            "types": types,
            "metadata": {
                "extraction_timestamp": pendulum.now().isoformat(),
                "total_types": len(types),
                "namespaces": {k: v for k, v in nsmap.items() if k is not None}
            }
        }
        
    except Exception as e:
        return {
            "source_format": "wsdl",
            "target_namespace": "",
            "types": [],
            "metadata": {
                "extraction_timestamp": pendulum.now().isoformat(),
                "total_types": 0,
                "error": str(e),
                "namespaces": {}
            }
        }


def extract_xsd_types(xsd_content: str) -> Dict[str, Any]:
    """
    Extract type definitions from XSD content.
    
    Args:
        xsd_content: Raw XSD content as string
        
    Returns:
        Dictionary containing normalized type definitions from XSD
    """
    try:
        root = etree.fromstring(xsd_content.encode('utf-8'))
        nsmap = root.nsmap.copy() if root.nsmap else {}
        
        # Handle default namespace
        if None in nsmap:
            if nsmap[None] == "http://www.w3.org/2001/XMLSchema":
                nsmap['xs'] = nsmap[None]
            else:
                nsmap['default'] = nsmap[None]
            del nsmap[None]
        
        # Ensure XSD namespace
        if 'xs' not in nsmap and 'xsd' not in nsmap:
            nsmap['xs'] = "http://www.w3.org/2001/XMLSchema"
        
        types = []
        target_namespace = root.get('targetNamespace', '')
        
        # Extract complex types
        complex_types = root.xpath('.//xs:complexType|.//xsd:complexType', namespaces=nsmap)
        for ct in complex_types:
            type_def = {
                "name": ct.get('name'),
                "category": "complex",
                "namespace": target_namespace,
                "properties": [],
                "restrictions": [],
                "documentation": _extract_documentation(ct, nsmap),
                "source_location": "xsd:schema"
            }
            
            # Extract sequence elements
            sequences = ct.xpath('.//xs:sequence|.//xsd:sequence', namespaces=nsmap)
            for seq in sequences:
                elements = seq.xpath('.//xs:element|.//xsd:element', namespaces=nsmap)
                for elem in elements:
                    prop = {
                        "name": elem.get('name'),
                        "type": elem.get('type'),
                        "min_occurs": elem.get('minOccurs', '1'),
                        "max_occurs": elem.get('maxOccurs', '1'),
                        "documentation": _extract_documentation(elem, nsmap)
                    }
                    type_def["properties"].append(prop)
            
            # Extract choice elements
            choices = ct.xpath('.//xs:choice|.//xsd:choice', namespaces=nsmap)
            for choice in choices:
                elements = choice.xpath('.//xs:element|.//xsd:element', namespaces=nsmap)
                for elem in elements:
                    prop = {
                        "name": elem.get('name'),
                        "type": elem.get('type'),
                        "min_occurs": elem.get('minOccurs', '0'),
                        "max_occurs": elem.get('maxOccurs', '1'),
                        "choice_group": True,
                        "documentation": _extract_documentation(elem, nsmap)
                    }
                    type_def["properties"].append(prop)
            
            types.append(type_def)
        
        # Extract simple types
        simple_types = root.xpath('.//xs:simpleType|.//xsd:simpleType', namespaces=nsmap)
        for st in simple_types:
            type_def = {
                "name": st.get('name'),
                "category": "simple",
                "namespace": target_namespace,
                "properties": [],
                "restrictions": [],
                "documentation": _extract_documentation(st, nsmap),
                "source_location": "xsd:schema"
            }
            
            # Extract restrictions
            restrictions = st.xpath('.//xs:restriction|.//xsd:restriction', namespaces=nsmap)
            for restr in restrictions:
                restriction_data = {
                    "base": restr.get('base'),
                    "facets": []
                }
                
                # Extract all facet types
                facets = restr.xpath('.//xs:enumeration|.//xsd:enumeration|.//xs:pattern|.//xsd:pattern|.//xs:minLength|.//xsd:minLength|.//xs:maxLength|.//xsd:maxLength|.//xs:minInclusive|.//xsd:minInclusive|.//xs:maxInclusive|.//xsd:maxInclusive', namespaces=nsmap)
                for facet in facets:
                    facet_data = {
                        "type": facet.tag.split('}')[-1] if '}' in facet.tag else facet.tag,
                        "value": facet.get('value')
                    }
                    restriction_data["facets"].append(facet_data)
                
                type_def["restrictions"].append(restriction_data)
            
            types.append(type_def)
        
        # Extract top-level elements
        elements = root.xpath('./xs:element|./xsd:element', namespaces=nsmap)
        for elem in elements:
            type_def = {
                "name": elem.get('name'),
                "category": "element",
                "namespace": target_namespace,
                "properties": [{
                    "type": elem.get('type'),
                    "min_occurs": elem.get('minOccurs', '1'),
                    "max_occurs": elem.get('maxOccurs', '1')
                }],
                "restrictions": [],
                "documentation": _extract_documentation(elem, nsmap),
                "source_location": "xsd:schema"
            }
            types.append(type_def)
        
        return {
            "source_format": "xsd",
            "target_namespace": target_namespace,
            "types": types,
            "metadata": {
                "extraction_timestamp": pendulum.now().isoformat(),
                "total_types": len(types),
                "namespaces": {k: v for k, v in nsmap.items() if k is not None}
            }
        }
        
    except Exception as e:
        return {
            "source_format": "xsd",
            "target_namespace": "",
            "types": [],
            "metadata": {
                "extraction_timestamp": pendulum.now().isoformat(),
                "total_types": 0,
                "error": str(e),
                "namespaces": {}
            }
        }


def extract_openapi_types(openapi_content: str) -> Dict[str, Any]:
    """
    Extract type definitions from OpenAPI content.

    Args:
        openapi_content: Raw OpenAPI content as string (JSON or YAML)

    Returns:
        Dictionary containing normalized type definitions from OpenAPI
    """
    try:
        # Parse OpenAPI content
        try:
            openapi_dict = json.loads(openapi_content)
        except json.JSONDecodeError:
            import yaml
            openapi_dict = yaml.safe_load(openapi_content)

        types = []

        # Extract schemas from components section
        components = openapi_dict.get('components', {})
        schemas = components.get('schemas', {})

        for schema_name, schema_def in schemas.items():
            type_def = {
                "name": schema_name,
                "category": "schema",
                "namespace": openapi_dict.get('info', {}).get('title', 'default'),
                "properties": [],
                "restrictions": [],
                "documentation": schema_def.get('description'),
                "source_location": f"components/schemas/{schema_name}"
            }

            # Extract properties for object types
            if schema_def.get('type') == 'object' or 'properties' in schema_def:
                properties = schema_def.get('properties', {})
                required_fields = schema_def.get('required', [])

                for prop_name, prop_def in properties.items():
                    prop = {
                        "name": prop_name,
                        "type": prop_def.get('type', 'unknown'),
                        "format": prop_def.get('format'),
                        "required": prop_name in required_fields,
                        "documentation": prop_def.get('description'),
                        "example": prop_def.get('example'),
                        "enum": prop_def.get('enum'),
                        "min_length": prop_def.get('minLength'),
                        "max_length": prop_def.get('maxLength'),
                        "minimum": prop_def.get('minimum'),
                        "maximum": prop_def.get('maximum'),
                        "pattern": prop_def.get('pattern')
                    }

                    # Handle array types
                    if prop_def.get('type') == 'array':
                        items = prop_def.get('items', {})
                        prop["items_type"] = items.get('type')
                        prop["items_ref"] = items.get('$ref')

                    # Handle reference types
                    if '$ref' in prop_def:
                        prop["ref"] = prop_def['$ref']
                        prop["type"] = "reference"

                    type_def["properties"].append(prop)

            # Extract restrictions for primitive types
            if schema_def.get('type') in ['string', 'number', 'integer']:
                restrictions = []

                if 'enum' in schema_def:
                    restrictions.append({
                        "type": "enumeration",
                        "values": schema_def['enum']
                    })

                if 'pattern' in schema_def:
                    restrictions.append({
                        "type": "pattern",
                        "value": schema_def['pattern']
                    })

                if 'minLength' in schema_def or 'maxLength' in schema_def:
                    restrictions.append({
                        "type": "length",
                        "min": schema_def.get('minLength'),
                        "max": schema_def.get('maxLength')
                    })

                if 'minimum' in schema_def or 'maximum' in schema_def:
                    restrictions.append({
                        "type": "range",
                        "min": schema_def.get('minimum'),
                        "max": schema_def.get('maximum')
                    })

                type_def["restrictions"] = restrictions

            # Add schema metadata
            type_def["schema_metadata"] = {
                "openapi_type": schema_def.get('type'),
                "format": schema_def.get('format'),
                "nullable": schema_def.get('nullable', False),
                "read_only": schema_def.get('readOnly', False),
                "write_only": schema_def.get('writeOnly', False),
                "deprecated": schema_def.get('deprecated', False)
            }

            types.append(type_def)

        # Extract parameter schemas
        parameters = components.get('parameters', {})
        for param_name, param_def in parameters.items():
            schema = param_def.get('schema', {})
            if schema:
                type_def = {
                    "name": param_name,
                    "category": "parameter",
                    "namespace": openapi_dict.get('info', {}).get('title', 'default'),
                    "properties": [{
                        "name": param_def.get('name', param_name),
                        "type": schema.get('type', 'unknown'),
                        "required": param_def.get('required', False),
                        "documentation": param_def.get('description'),
                        "location": param_def.get('in')  # query, header, path, cookie
                    }],
                    "restrictions": [],
                    "documentation": param_def.get('description'),
                    "source_location": f"components/parameters/{param_name}"
                }
                types.append(type_def)

        return {
            "source_format": "openapi",
            "target_namespace": openapi_dict.get('info', {}).get('title', 'default'),
            "types": types,
            "metadata": {
                "extraction_timestamp": pendulum.now().isoformat(),
                "total_types": len(types),
                "openapi_version": openapi_dict.get('openapi', openapi_dict.get('swagger')),
                "api_info": openapi_dict.get('info', {}),
                "namespaces": {}
            }
        }

    except Exception as e:
        return {
            "source_format": "openapi",
            "target_namespace": "",
            "types": [],
            "metadata": {
                "extraction_timestamp": pendulum.now().isoformat(),
                "total_types": 0,
                "error": str(e),
                "namespaces": {}
            }
        }


def _extract_documentation(element, nsmap: Dict[str, str]) -> Optional[str]:
    """Extract documentation/annotation from XML element."""
    try:
        # Look for xs:annotation/xs:documentation
        docs = element.xpath('.//xs:annotation/xs:documentation|.//xsd:annotation/xsd:documentation', namespaces=nsmap)
        if docs:
            return docs[0].text
        return None
    except:
        return None
