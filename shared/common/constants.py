"""
Configuration constants for the Airflow LLM Service Mapper project.

This module contains all configuration constants used across the DAGs and
common modules. It includes dataset URIs, model configurations, storage
paths, and other system-wide settings.

Author: Airflow LLM Service Mapper Team
Version: 1.0.0
"""

# =============================================================================
# Storage Configuration
# =============================================================================

# S3 Bucket Names (for cloud deployment)
S3_BUCKET_PARSED_DEFINITIONS = "my-airflow-llm-project-parsed-definitions"
S3_BUCKET_LLM_TRAINING_DATA = "my-airflow-llm-project-training-data"
S3_BUCKET_MODELS = "my-airflow-llm-project-models"
S3_BUCKET_OPERATIONAL_OUTPUTS = "my-airflow-llm-project-operational-outputs"
S3_BUCKET_OPERATIONAL_LOGS = "my-airflow-llm-project-operational-logs"

# Local Storage Paths (for development)
LOCAL_DATA_PATH = "/opt/airflow/data"
LOCAL_MODELS_PATH = "/opt/airflow/models"
LOCAL_LOGS_PATH = "/opt/airflow/logs"

# =============================================================================
# Dataset URIs for Airflow Lineage Tracking
# =============================================================================

PARSED_DEFINITIONS_DATASET_URI = "data://parsed_definitions"
TRAINING_DATA_DATASET_URI = "data://training_data"
TRAINED_MODEL_DATASET_URI = "data://trained_models"
OPERATIONAL_MAPPING_DATASET_URI = "data://operational_mappings"
OPERATIONAL_LOGS_DATASET_URI = "data://operational_logs"
MONITORING_METRICS_DATASET_URI = "data://monitoring_metrics"

# =============================================================================
# Model Configuration
# =============================================================================

# Default LLM Models
DEFAULT_LLM_MODEL_ID = "gpt-3.5-turbo"
DEFAULT_HUGGINGFACE_MODEL = "google/flan-t5-small"
DEFAULT_EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"

# Model Training Parameters
DEFAULT_EPOCHS = 3
DEFAULT_BATCH_SIZE = 8
DEFAULT_LEARNING_RATE = 5e-5
DEFAULT_MAX_LENGTH = 512
DEFAULT_VALIDATION_SPLIT = 0.2

# Model Performance Thresholds
MIN_ACCURACY_THRESHOLD = 0.85
MIN_CONFIDENCE_THRESHOLD = 0.8
MAX_ERROR_RATE_THRESHOLD = 0.1

# =============================================================================
# Heuristic Engine Configuration
# =============================================================================

DEFAULT_HEURISTICS_VERSION = "v1"
HEURISTIC_CONFIDENCE_THRESHOLD = 0.7
ENABLE_HEURISTIC_FALLBACK = True

# =============================================================================
# Operational Configuration
# =============================================================================

# Monitoring Settings
MONITORING_LOOKBACK_HOURS = 24
MIN_SAMPLES_FOR_ANALYSIS = 100
RETRAINING_TRIGGER_THRESHOLD = 0.05  # 5% performance drop

# Request Processing
MAX_CONCURRENT_REQUESTS = 10
REQUEST_TIMEOUT_SECONDS = 30
MAX_RETRY_ATTEMPTS = 3

# =============================================================================
# Data Processing Configuration
# =============================================================================

# Parser Settings
MAX_FILE_SIZE_MB = 100
SUPPORTED_WSDL_VERSIONS = ["1.1", "2.0"]
SUPPORTED_OPENAPI_VERSIONS = ["3.0", "3.1"]

# Training Data Generation
MAX_EXAMPLES_PER_OPERATION = 100
MIN_EXAMPLES_FOR_TRAINING = 50
TRAINING_DATA_FORMATS = ["json", "jsonl", "parquet"]

# =============================================================================
# Logging and Monitoring
# =============================================================================

# Log Levels
DEFAULT_LOG_LEVEL = "INFO"
DEBUG_MODE = False

# Metrics Collection
ENABLE_METRICS_COLLECTION = True
METRICS_RETENTION_DAYS = 30

# Alerting
ENABLE_EMAIL_ALERTS = True
ALERT_EMAIL_RECIPIENTS = ["<EMAIL>"]
SLACK_WEBHOOK_URL = None  # Set in environment variables

# =============================================================================
# Security Configuration
# =============================================================================

# API Keys (loaded from environment variables)
OPENAI_API_KEY_VAR = "OPENAI_API_KEY"
HUGGINGFACE_TOKEN_VAR = "HUGGINGFACE_TOKEN"
AWS_ACCESS_KEY_VAR = "AWS_ACCESS_KEY_ID"
AWS_SECRET_KEY_VAR = "AWS_SECRET_ACCESS_KEY"

# Encryption
ENABLE_DATA_ENCRYPTION = False
ENCRYPTION_KEY_VAR = "ENCRYPTION_KEY"

# =============================================================================
# Feature Flags
# =============================================================================

# Experimental Features
ENABLE_JAVA_CODE_GENERATION = True
ENABLE_CSHARP_CODE_GENERATION = False
ENABLE_ADVANCED_HEURISTICS = True
ENABLE_MODEL_QUANTIZATION = False
ENABLE_DISTRIBUTED_TRAINING = False

# =============================================================================
# Version Information
# =============================================================================

PROJECT_VERSION = "1.0.0"
AIRFLOW_VERSION_REQUIRED = "3.0.0"
PYTHON_VERSION_REQUIRED = "3.9"
TRAINING_DATA_DATASET_URI = "data/training_data"
MODEL_TRAINING_DATASET_URI = "data/model_training"
MODEL_EVALUATION_DATASET_URI = "data/model_evaluation"

# Service definition catalog
SERVICE_CATALOG_VARIABLE_NAME = "service_definitions_catalog"

# Default paths
DEFAULT_OUTPUT_BASE_PATH = "data/parsed_definitions"
DEFAULT_TRAINING_DATA_PATH = "data/training_data"
DEFAULT_MODELS_PATH = "models/trained"

# Schema validation
SCHEMA_VALIDATION_ENABLED = True
