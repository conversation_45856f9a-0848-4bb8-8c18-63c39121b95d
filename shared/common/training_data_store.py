"""
Storage and retrieval of training data for ML models.
"""

import os
import json
import glob
import time
import hashlib
import shutil
from typing import Dict, List, Any, Optional, Union, Tuple

import pendulum
from airflow.models import Variable

from common import constants
from common.training_data_formats import TrainingDataFormats

class TrainingDataStore:
    """Manages storage and retrieval of training data"""
    
    @staticmethod
    def store_training_examples(
        examples: List[Dict[str, Any]],
        service_name: str,
        data_type: str,
        base_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Store training examples in a standardized format.
        
        Args:
            examples: List of training examples
            service_name: Name of the service
            data_type: Type of data (wsdl, openapi, logs, etc.)
            base_path: Base path for storage (defaults to constants.DEFAULT_TRAINING_DATA_PATH)
            
        Returns:
            Metadata about the stored examples
        """
        # Use default path if not specified
        if base_path is None:
            base_path = constants.DEFAULT_TRAINING_DATA_PATH
        
        # Create timestamp for versioning
        timestamp = pendulum.now().timestamp()
        
        # Create output directory
        output_dir = f"{base_path}/{service_name}/{data_type}/v{timestamp}"
        os.makedirs(output_dir, exist_ok=True)
        
        # Ensure examples have unique IDs
        for i, example in enumerate(examples):
            if "id" not in example or not example["id"]:
                # Create a unique ID if not present
                content_hash = hashlib.md5(f"{service_name}:{data_type}:{i}:{timestamp}".encode()).hexdigest()
                example["id"] = f"{data_type}_{content_hash[:10]}"
        
        # Store examples by type for efficient retrieval
        examples_by_type = {}
        for example in examples:
            element_type = example.get("element_type", "default")
            if element_type not in examples_by_type:
                examples_by_type[element_type] = []
            examples_by_type[element_type].append(example)
        
        # Store each type in a separate file
        for element_type, type_examples in examples_by_type.items():
            file_path = f"{output_dir}/{element_type}_examples.json"
            with open(file_path, "w") as f:
                json.dump(type_examples, f, indent=2)
        
        # Store all examples in a single file as well
        all_examples_path = f"{output_dir}/all_examples.json"
        with open(all_examples_path, "w") as f:
            json.dump(examples, f, indent=2)
        
        # Create metadata
        metadata = {
            "service_name": service_name,
            "data_type": data_type,
            "version": f"v{timestamp}",
            "timestamp": timestamp,
            "example_count": len(examples),
            "element_types": list(examples_by_type.keys()),
            "type_counts": {t: len(e) for t, e in examples_by_type.items()},
            "storage_path": output_dir
        }
        
        # Store metadata
        metadata_path = f"{output_dir}/metadata.json"
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)
        
        # Create latest symlink/copy
        latest_dir = f"{base_path}/{service_name}/{data_type}/latest"
        if os.path.exists(latest_dir) and os.path.isdir(latest_dir):
            shutil.rmtree(latest_dir)
        
        os.makedirs(latest_dir, exist_ok=True)
        
        # Copy files to latest
        for filename in os.listdir(output_dir):
            src_file = f"{output_dir}/{filename}"
            dst_file = f"{latest_dir}/{filename}"
            if os.path.isfile(src_file):
                shutil.copy2(src_file, dst_file)
        
        # Update catalog
        TrainingDataStore._update_catalog(service_name, data_type, metadata)
        
        return metadata
    
    @staticmethod
    def load_training_examples(
        service_name: str,
        data_type: str,
        element_type: Optional[str] = None,
        version: Optional[str] = "latest",
        base_path: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Load training examples for a service.
        
        Args:
            service_name: Name of the service
            data_type: Type of data (wsdl, openapi, logs, etc.)
            element_type: Optional type of elements to load (operations, schemas, etc.)
            version: Version to load (defaults to "latest")
            base_path: Base path for storage (defaults to constants.DEFAULT_TRAINING_DATA_PATH)
            
        Returns:
            List of training examples
        """
        # Use default path if not specified
        if base_path is None:
            base_path = constants.DEFAULT_TRAINING_DATA_PATH
        
        # Determine the directory to load from
        load_dir = f"{base_path}/{service_name}/{data_type}/{version}"
        
        if not os.path.exists(load_dir):
            print(f"Warning: Training data not found at {load_dir}")
            return []
        
        # If element_type is specified, load only that type
        if element_type:
            file_path = f"{load_dir}/{element_type}_examples.json"
            if os.path.exists(file_path):
                with open(file_path, "r") as f:
                    return json.load(f)
            else:
                print(f"Warning: No {element_type} examples found for {service_name}/{data_type}")
                return []
        
        # Otherwise, load all examples
        all_examples_path = f"{load_dir}/all_examples.json"
        if os.path.exists(all_examples_path):
            with open(all_examples_path, "r") as f:
                return json.load(f)
        
        # If all_examples.json doesn't exist, try to load from individual files
        examples = []
        for file_path in glob.glob(f"{load_dir}/*_examples.json"):
            if os.path.basename(file_path) != "all_examples.json":
                with open(file_path, "r") as f:
                    examples.extend(json.load(f))
        
        return examples
    
    @staticmethod
    def get_available_services(
        data_type: Optional[str] = None,
        base_path: Optional[str] = None
    ) -> List[str]:
        """
        Get a list of services with available training data.
        
        Args:
            data_type: Optional type of data to filter by
            base_path: Base path for storage (defaults to constants.DEFAULT_TRAINING_DATA_PATH)
            
        Returns:
            List of service names
        """
        # Use default path if not specified
        if base_path is None:
            base_path = constants.DEFAULT_TRAINING_DATA_PATH
        
        if not os.path.exists(base_path):
            return []
        
        # Get all service directories
        services = [d for d in os.listdir(base_path) 
                   if os.path.isdir(os.path.join(base_path, d))]
        
        # Filter by data type if specified
        if data_type:
            services = [s for s in services 
                       if os.path.exists(os.path.join(base_path, s, data_type))]
        
        return services
    
    @staticmethod
    def get_service_versions(
        service_name: str,
        data_type: str,
        base_path: Optional[str] = None
    ) -> List[str]:
        """
        Get available versions for a service and data type.
        
        Args:
            service_name: Name of the service
            data_type: Type of data
            base_path: Base path for storage (defaults to constants.DEFAULT_TRAINING_DATA_PATH)
            
        Returns:
            List of version strings
        """
        # Use default path if not specified
        if base_path is None:
            base_path = constants.DEFAULT_TRAINING_DATA_PATH
        
        service_dir = f"{base_path}/{service_name}/{data_type}"
        
        if not os.path.exists(service_dir):
            return []
        
        # Get all version directories (excluding "latest")
        versions = [d for d in os.listdir(service_dir) 
                   if os.path.isdir(os.path.join(service_dir, d)) and d != "latest"]
        
        # Sort by version number (assuming vXXXXXXXXXX format)
        versions.sort(key=lambda v: float(v[1:]) if v.startswith("v") and v[1:].isdigit() else 0, reverse=True)
        
        return versions
    
    @staticmethod
    def get_service_metadata(
        service_name: str,
        data_type: str,
        version: str = "latest",
        base_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Get metadata for a service's training data.
        
        Args:
            service_name: Name of the service
            data_type: Type of data
            version: Version to get metadata for (defaults to "latest")
            base_path: Base path for storage (defaults to constants.DEFAULT_TRAINING_DATA_PATH)
            
        Returns:
            Metadata dictionary
        """
        # Use default path if not specified
        if base_path is None:
            base_path = constants.DEFAULT_TRAINING_DATA_PATH
        
        metadata_path = f"{base_path}/{service_name}/{data_type}/{version}/metadata.json"
        
        if not os.path.exists(metadata_path):
            return {}
        
        with open(metadata_path, "r") as f:
            return json.load(f)
    
    @staticmethod
    def _update_catalog(
        service_name: str,
        data_type: str,
        metadata: Dict[str, Any]
    ) -> None:
        """
        Update the training data catalog with new metadata.
        
        Args:
            service_name: Name of the service
            data_type: Type of data
            metadata: Metadata to add to the catalog
        """
        try:
            # Try to get existing catalog
            try:
                catalog = Variable.get("training_data_catalog", deserialize_json=True)
            except (KeyError, ValueError):
                catalog = {"services": {}, "last_updated": None}
            
            # Ensure services dict exists
            if "services" not in catalog:
                catalog["services"] = {}
            
            # Add or update service entry
            if service_name not in catalog["services"]:
                catalog["services"][service_name] = {}
            
            # Add or update data type entry
            catalog["services"][service_name][data_type] = {
                "latest_version": metadata["version"],
                "example_count": metadata["example_count"],
                "last_updated": pendulum.now().isoformat()
            }
            
            # Update last_updated timestamp
            catalog["last_updated"] = pendulum.now().isoformat()
            
            # Save updated catalog
            Variable.set("training_data_catalog", json.dumps(catalog))
        except Exception as e:
            print(f"Warning: Could not update training data catalog: {e}")