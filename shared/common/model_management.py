# dags/common/model_management.py
import json
import os
import time
import shutil
from .constants import S3_BUCKET_MODELS

# For S3 interaction, you'd use boto3 and configure Airflow Connections
# import boto3
# from airflow.providers.amazon.aws.hooks.s3 import S3Hook

def save_fine_tuned_model(model_object, tokenizer_object, model_name: str, version: str, metrics: dict):
    """Saves a fine-tuned model and its assets."""
    base_path = f"models_store/{model_name}/{version}"  # Local path for simulation
    os.makedirs(base_path, exist_ok=True)

    # Save model and tokenizer if provided
    if model_object is not None:
        model_object.save_pretrained(f"{base_path}/model")

    if tokenizer_object is not None:
        tokenizer_object.save_pretrained(f"{base_path}/tokenizer")

    # Save metrics
    with open(f"{base_path}/metrics.json", "w") as f:
        json.dump(metrics, f, indent=2)

    # Example S3 upload
    # s3_hook = S3Hook(aws_conn_id='aws_default')  # Assumes 'aws_default' connection exists
    # for root, dirs, files in os.walk(base_path):
    #     for file in files:
    #         local_file_path = os.path.join(root, file)
    #         s3_key = f"models/{model_name}/{version}/{os.path.relpath(local_file_path, base_path)}"
    #         s3_hook.load_file(filename=local_file_path, key=s3_key, bucket_name=S3_BUCKET_MODELS, replace=True)

    print(f"Saved model '{model_name}' version '{version}' with metrics: {metrics} to {base_path}")
    print(f"In production, this would be saved to a proper model registry or cloud storage like s3://{S3_BUCKET_MODELS}/models/{model_name}/{version}")
    return f"{base_path}"  # or s3_path

def load_model_for_inference(model_name: str, version: str = "latest"):
    """Loads a model for inference."""
    if version == "latest":
        # Logic to determine the actual latest version path
        pointer_file = f"models/model_pointers/latest_{model_name}_model.json"
        if os.path.exists(pointer_file):
            with open(pointer_file, 'r') as f:
                pointer_data = json.load(f)
                version = pointer_data.get("latest_version", "v1_simulated_prod")
        else:
            version = "v1_simulated_prod"  # Fallback

    model_path = f"models_store/{model_name}/{version}"  # Local path for simulation

    # Check if this is a Hugging Face model
    if os.path.exists(f"{model_path}/model") and os.path.exists(f"{model_path}/tokenizer"):
        try:
            from transformers import AutoModelForSeq2SeqLM, AutoTokenizer

            # Load model and tokenizer
            model = AutoModelForSeq2SeqLM.from_pretrained(f"{model_path}/model")
            tokenizer = AutoTokenizer.from_pretrained(f"{model_path}/tokenizer")

            print(f"Loaded Hugging Face model '{model_name}' version '{version}' from {model_path}")
            return model, tokenizer
        except ImportError:
            print("Transformers library not available, returning simulated model")
            return {"model_name": model_name, "version": version, "status": "loaded_simulated"}, {"tokenizer": "simulated"}
    else:
        print(f"Simulating loading model '{model_name}' version '{version}' from {model_path}")
        return {"model_name": model_name, "version": version, "status": "loaded_simulated"}, {"tokenizer": "simulated"}

def update_latest_model_pointer(model_name: str, new_version: str, pointer_file_dir: str = "models/model_pointers"):
    """Updates a pointer file to indicate the latest production model."""
    os.makedirs(pointer_file_dir, exist_ok=True)
    pointer_file = os.path.join(pointer_file_dir, f"latest_{model_name}_model.json")
    with open(pointer_file, "w") as f:
        json.dump({"model_name": model_name, "latest_version": new_version, "updated_at": time.time()}, f)
    print(f"Updated latest model pointer for '{model_name}' to version '{new_version}' in {pointer_file}")
    return pointer_file

def run_inference_with_hf_model(model, tokenizer, input_text: str, max_length: int = 512):
    """Run inference with a Hugging Face model."""
    try:
        import torch

        # Tokenize input
        inputs = tokenizer(input_text, return_tensors="pt", max_length=max_length, truncation=True)

        # Generate output
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_length=max_length,
                num_beams=4,
                early_stopping=True
            )

        # Decode output
        decoded_output = tokenizer.decode(outputs[0], skip_special_tokens=True)

        return decoded_output
    except Exception as e:
        print(f"Error running inference: {e}")
        return f"Error: {str(e)}"
