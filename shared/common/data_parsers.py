# dags/common/data_parsers.py
import json
import yaml
from lxml import etree
import xmlschema
import os
import requests
from urllib.parse import urljoin, urlparse
from typing import Dict, List, Any, Optional, Tuple

def parse_wsdl_content(wsdl_string: str) -> dict:
    """
    Parses WSDL string content into a structured dictionary.

    Returns a dictionary with:
    - services: List of services defined in the WSDL
    - operations: List of operations with their input/output messages
    - messages: List of message definitions
    - types: List of type definitions (may reference XSD)
    """
    try:
        # Parse XML
        root = etree.fromstring(wsdl_string.encode('utf-8'))

        # Extract namespaces for XPath queries
        nsmap = root.nsmap
        # Add WSDL namespace if not present
        if None not in nsmap and 'wsdl' not in nsmap:
            nsmap['wsdl'] = "http://schemas.xmlsoap.org/wsdl/"

        # Parse services
        services = []
        for service in root.xpath('.//wsdl:service', namespaces=nsmap):
            service_data = {
                'name': service.get('name'),
                'ports': []
            }
            for port in service.xpath('./wsdl:port', namespaces=nsmap):
                port_data = {
                    'name': port.get('name'),
                    'binding': port.get('binding')
                }
                # Get SOAP address if available
                soap_address = port.xpath('./soap:address', namespaces=nsmap)
                if soap_address:
                    port_data['address'] = soap_address[0].get('location')
                service_data['ports'].append(port_data)
            services.append(service_data)

        # Parse operations
        operations = []
        for portType in root.xpath('.//wsdl:portType', namespaces=nsmap):
            for operation in portType.xpath('./wsdl:operation', namespaces=nsmap):
                op_data = {
                    'name': operation.get('name'),
                    'port_type': portType.get('name')
                }

                # Get input message
                input_msg = operation.xpath('./wsdl:input', namespaces=nsmap)
                if input_msg:
                    op_data['input_message'] = input_msg[0].get('message')

                # Get output message
                output_msg = operation.xpath('./wsdl:output', namespaces=nsmap)
                if output_msg:
                    op_data['output_message'] = output_msg[0].get('message')

                operations.append(op_data)

        # Parse messages
        messages = []
        for message in root.xpath('.//wsdl:message', namespaces=nsmap):
            msg_data = {
                'name': message.get('name'),
                'parts': []
            }
            for part in message.xpath('./wsdl:part', namespaces=nsmap):
                part_data = {
                    'name': part.get('name'),
                    'type': part.get('type'),
                    'element': part.get('element')
                }
                msg_data['parts'].append(part_data)
            messages.append(msg_data)

        # Parse types (simplified)
        types_section = root.xpath('.//wsdl:types', namespaces=nsmap)
        types = []
        if types_section:
            # Extract schema elements
            schemas = types_section[0].xpath('.//xs:schema|.//xsd:schema', namespaces=nsmap)
            for schema in schemas:
                # This is a simplified extraction - in a real implementation,
                # you'd want to extract complex types, simple types, elements, etc.
                schema_data = {
                    'target_namespace': schema.get('targetNamespace'),
                    'elements': [{'name': el.get('name')} for el in schema.xpath('.//xs:element|.//xsd:element', namespaces=nsmap)],
                    'complex_types': [{'name': ct.get('name')} for ct in schema.xpath('.//xs:complexType|.//xsd:complexType', namespaces=nsmap)]
                }
                types.append(schema_data)

        return {
            "services": services,
            "operations": operations,
            "messages": messages,
            "types": types,
            "raw_namespaces": {k: v for k, v in nsmap.items() if k is not None}
        }
    except Exception as e:
        print(f"Error parsing WSDL: {e}")
        # Return a minimal structure to avoid breaking downstream tasks
        return {"parsed_wsdl": {"error": str(e), "operations_found": 0}}

def parse_xsd_content(xsd_string: str) -> dict:
    """
    Parses XSD string content into a structured dictionary.

    Returns a dictionary with:
    - elements: List of element definitions
    - complex_types: List of complex type definitions
    - simple_types: List of simple type definitions
    """
    try:
        # Parse XML
        root = etree.fromstring(xsd_string.encode('utf-8'))

        # Extract namespaces for XPath queries
        nsmap = root.nsmap
        # Add XSD namespace if not present
        if 'xs' not in nsmap and 'xsd' not in nsmap:
            nsmap['xs'] = "http://www.w3.org/2001/XMLSchema"

        # Use xmlschema to parse the XSD
        schema = xmlschema.XMLSchema(xsd_string)

        # Extract elements
        elements = []
        for element in root.xpath('.//xs:element|.//xsd:element', namespaces=nsmap):
            element_data = {
                'name': element.get('name'),
                'type': element.get('type'),
                'min_occurs': element.get('minOccurs', '1'),
                'max_occurs': element.get('maxOccurs', '1')
            }
            elements.append(element_data)

        # Extract complex types
        complex_types = []
        for complex_type in root.xpath('.//xs:complexType|.//xsd:complexType', namespaces=nsmap):
            ct_data = {
                'name': complex_type.get('name'),
                'attributes': [],
                'elements': []
            }

            # Get attributes
            for attr in complex_type.xpath('.//xs:attribute|.//xsd:attribute', namespaces=nsmap):
                attr_data = {
                    'name': attr.get('name'),
                    'type': attr.get('type'),
                    'use': attr.get('use', 'optional')
                }
                ct_data['attributes'].append(attr_data)

            # Get child elements
            for el in complex_type.xpath('.//xs:element|.//xsd:element', namespaces=nsmap):
                el_data = {
                    'name': el.get('name'),
                    'type': el.get('type'),
                    'min_occurs': el.get('minOccurs', '1'),
                    'max_occurs': el.get('maxOccurs', '1')
                }
                ct_data['elements'].append(el_data)

            complex_types.append(ct_data)

        # Extract simple types
        simple_types = []
        for simple_type in root.xpath('.//xs:simpleType|.//xsd:simpleType', namespaces=nsmap):
            st_data = {
                'name': simple_type.get('name'),
                'restrictions': []
            }

            # Get restrictions
            for restriction in simple_type.xpath('.//xs:restriction|.//xsd:restriction', namespaces=nsmap):
                r_data = {
                    'base': restriction.get('base'),
                    'enumerations': [],
                    'patterns': [],
                    'min_length': None,
                    'max_length': None
                }

                # Get enumerations
                for enum in restriction.xpath('.//xs:enumeration|.//xsd:enumeration', namespaces=nsmap):
                    r_data['enumerations'].append(enum.get('value'))

                # Get patterns
                for pattern in restriction.xpath('.//xs:pattern|.//xsd:pattern', namespaces=nsmap):
                    r_data['patterns'].append(pattern.get('value'))

                # Get length constraints
                min_length = restriction.xpath('.//xs:minLength|.//xsd:minLength', namespaces=nsmap)
                if min_length:
                    r_data['min_length'] = min_length[0].get('value')

                max_length = restriction.xpath('.//xs:maxLength|.//xsd:maxLength', namespaces=nsmap)
                if max_length:
                    r_data['max_length'] = max_length[0].get('value')

                st_data['restrictions'].append(r_data)

            simple_types.append(st_data)

        return {
            "elements": elements,
            "complex_types": complex_types,
            "simple_types": simple_types,
            "target_namespace": root.get('targetNamespace'),
            "imports": [{'namespace': imp.get('namespace'), 'schema_location': imp.get('schemaLocation')}
                       for imp in root.xpath('.//xs:import|.//xsd:import', namespaces=nsmap)]
        }
    except Exception as e:
        print(f"Error parsing XSD: {e}")
        return {"parsed_xsd": {"error": str(e), "types_found": 0}}


def parse_multiple_wsdl_with_xsd(wsdl_definitions: List[Dict[str, str]], base_path: str = None) -> Dict[str, Any]:
    """
    Parse multiple WSDL service definitions with their associated XSD data definitions.

    Args:
        wsdl_definitions: List of dictionaries containing WSDL definition info:
            [
                {
                    "service_name": "service1",
                    "wsdl_uri": "path/to/service1.wsdl",
                    "xsd_uris": ["path/to/schema1.xsd", "path/to/schema2.xsd"]  # optional
                },
                ...
            ]
        base_path: Base path for resolving relative URIs

    Returns:
        Dictionary containing parsed results for all services:
        {
            "services": {
                "service1": {
                    "wsdl": {...parsed_wsdl...},
                    "xsd_schemas": {...parsed_xsd_schemas...},
                    "resolved_imports": [...list_of_resolved_xsd_imports...]
                },
                ...
            },
            "metadata": {
                "total_services": int,
                "total_xsd_files": int,
                "parsing_errors": [...],
                "processing_timestamp": str
            }
        }
    """
    import pendulum

    results = {
        "services": {},
        "metadata": {
            "total_services": 0,
            "total_xsd_files": 0,
            "parsing_errors": [],
            "processing_timestamp": pendulum.now().isoformat()
        }
    }

    for wsdl_def in wsdl_definitions:
        service_name = wsdl_def.get("service_name")
        wsdl_uri = wsdl_def.get("wsdl_uri")
        xsd_uris = wsdl_def.get("xsd_uris", [])

        if not service_name or not wsdl_uri:
            error_msg = f"Invalid WSDL definition: missing service_name or wsdl_uri"
            results["metadata"]["parsing_errors"].append(error_msg)
            continue

        try:
            # Parse WSDL
            wsdl_content = _load_content_from_uri(wsdl_uri, base_path)
            parsed_wsdl = parse_wsdl_content(wsdl_content)

            # Extract XSD imports from WSDL
            resolved_imports = _resolve_xsd_imports_from_wsdl(wsdl_content, wsdl_uri, base_path)

            # Parse explicitly provided XSD files
            xsd_schemas = {}
            for xsd_uri in xsd_uris:
                try:
                    xsd_content = _load_content_from_uri(xsd_uri, base_path)
                    parsed_xsd = parse_xsd_content(xsd_content)
                    xsd_schemas[xsd_uri] = parsed_xsd
                    results["metadata"]["total_xsd_files"] += 1
                except Exception as e:
                    error_msg = f"Error parsing XSD {xsd_uri} for service {service_name}: {str(e)}"
                    results["metadata"]["parsing_errors"].append(error_msg)

            # Parse resolved XSD imports
            for import_info in resolved_imports:
                if import_info["resolved_uri"] not in xsd_schemas:
                    try:
                        xsd_content = _load_content_from_uri(import_info["resolved_uri"], base_path)
                        parsed_xsd = parse_xsd_content(xsd_content)
                        xsd_schemas[import_info["resolved_uri"]] = parsed_xsd
                        results["metadata"]["total_xsd_files"] += 1
                    except Exception as e:
                        error_msg = f"Error parsing imported XSD {import_info['resolved_uri']} for service {service_name}: {str(e)}"
                        results["metadata"]["parsing_errors"].append(error_msg)

            results["services"][service_name] = {
                "wsdl": parsed_wsdl,
                "xsd_schemas": xsd_schemas,
                "resolved_imports": resolved_imports,
                "source_wsdl_uri": wsdl_uri,
                "source_xsd_uris": xsd_uris
            }
            results["metadata"]["total_services"] += 1

        except Exception as e:
            error_msg = f"Error processing service {service_name}: {str(e)}"
            results["metadata"]["parsing_errors"].append(error_msg)

    return results


def _load_content_from_uri(uri: str, base_path: str = None) -> str:
    """
    Load content from a URI (local file or URL).

    Args:
        uri: URI to load content from
        base_path: Base path for resolving relative URIs

    Returns:
        Content as string
    """
    # Handle absolute URLs
    if uri.startswith(("http://", "https://")):
        response = requests.get(uri, timeout=30)
        response.raise_for_status()
        return response.text

    # Handle local files
    if base_path and not os.path.isabs(uri):
        uri = os.path.join(base_path, uri)

    with open(uri, 'r', encoding='utf-8') as f:
        return f.read()


def _resolve_xsd_imports_from_wsdl(wsdl_content: str, wsdl_uri: str, base_path: str = None) -> List[Dict[str, str]]:
    """
    Extract and resolve XSD import statements from WSDL content.

    Args:
        wsdl_content: WSDL content as string
        wsdl_uri: URI of the WSDL file (for resolving relative imports)
        base_path: Base path for resolving relative URIs

    Returns:
        List of resolved import information:
        [
            {
                "namespace": "http://example.com/types",
                "schema_location": "types.xsd",
                "resolved_uri": "/full/path/to/types.xsd"
            },
            ...
        ]
    """
    try:
        root = etree.fromstring(wsdl_content.encode('utf-8'))
        nsmap = root.nsmap.copy() if root.nsmap else {}

        # Handle default namespace
        if None in nsmap:
            if nsmap[None] == "http://schemas.xmlsoap.org/wsdl/":
                nsmap['wsdl'] = nsmap[None]
            else:
                nsmap['default'] = nsmap[None]
            del nsmap[None]

        # Ensure required namespaces
        if 'wsdl' not in nsmap:
            nsmap['wsdl'] = "http://schemas.xmlsoap.org/wsdl/"
        if 'xs' not in nsmap and 'xsd' not in nsmap:
            nsmap['xs'] = "http://www.w3.org/2001/XMLSchema"

        imports = []

        # Find XSD imports in wsdl:types section
        import_elements = root.xpath('.//xs:import|.//xsd:import', namespaces=nsmap)

        for imp in import_elements:
            namespace = imp.get('namespace')
            schema_location = imp.get('schemaLocation')

            if schema_location:
                resolved_uri = _resolve_relative_uri(schema_location, wsdl_uri, base_path)
                imports.append({
                    "namespace": namespace,
                    "schema_location": schema_location,
                    "resolved_uri": resolved_uri
                })

        return imports

    except Exception as e:
        print(f"Error resolving XSD imports from WSDL: {e}")
        return []


def _resolve_relative_uri(relative_uri: str, base_uri: str, base_path: str = None) -> str:
    """
    Resolve a relative URI against a base URI.

    Args:
        relative_uri: Relative URI to resolve
        base_uri: Base URI to resolve against
        base_path: Additional base path for local files

    Returns:
        Resolved absolute URI
    """
    # If relative_uri is already absolute, return as-is
    if relative_uri.startswith(("http://", "https://")) or os.path.isabs(relative_uri):
        return relative_uri

    # Handle URL base
    if base_uri.startswith(("http://", "https://")):
        return urljoin(base_uri, relative_uri)

    # Handle local file base
    if base_path and not os.path.isabs(base_uri):
        base_uri = os.path.join(base_path, base_uri)

    base_dir = os.path.dirname(base_uri)
    return os.path.join(base_dir, relative_uri)

def parse_openapi_content(openapi_string: str) -> dict:
    """
    Parses OpenAPI string content (JSON or YAML) into a dictionary.
    Validates basic OpenAPI structure.
    """
    try:
        # Try parsing as JSON first
        try:
            openapi_dict = json.loads(openapi_string)
        except json.JSONDecodeError:
            # If not JSON, try YAML
            openapi_dict = yaml.safe_load(openapi_string)

        # Basic validation of OpenAPI structure
        if 'openapi' not in openapi_dict:
            if 'swagger' in openapi_dict:
                print("Warning: This appears to be a Swagger/OpenAPI 2.0 document. Consider upgrading to OpenAPI 3.0+")
            else:
                print("Warning: Document doesn't contain 'openapi' version field")

        if 'info' not in openapi_dict:
            print("Warning: Document doesn't contain required 'info' section")

        if 'paths' not in openapi_dict:
            print("Warning: Document doesn't contain required 'paths' section")

        # Extract key components for easier processing
        extracted = {
            'openapi_version': openapi_dict.get('openapi', openapi_dict.get('swagger')),
            'info': openapi_dict.get('info', {}),
            'servers': openapi_dict.get('servers', []),
            'paths': {},
            'components': {
                'schemas': {},
                'parameters': {},
                'responses': {},
                'securitySchemes': {}
            }
        }

        # Extract paths
        for path, path_item in openapi_dict.get('paths', {}).items():
            extracted['paths'][path] = {}
            for method, operation in path_item.items():
                if method in ['get', 'post', 'put', 'delete', 'patch', 'options', 'head']:
                    extracted['paths'][path][method] = {
                        'operationId': operation.get('operationId'),
                        'summary': operation.get('summary'),
                        'description': operation.get('description'),
                        'parameters': operation.get('parameters', []),
                        'requestBody': operation.get('requestBody'),
                        'responses': operation.get('responses', {})
                    }

        # Extract components
        components = openapi_dict.get('components', {})
        for schema_name, schema in components.get('schemas', {}).items():
            extracted['components']['schemas'][schema_name] = schema

        for param_name, param in components.get('parameters', {}).items():
            extracted['components']['parameters'][param_name] = param

        for resp_name, resp in components.get('responses', {}).items():
            extracted['components']['responses'][resp_name] = resp

        for sec_name, sec in components.get('securitySchemes', {}).items():
            extracted['components']['securitySchemes'][sec_name] = sec

        # Return both the extracted structure and the full parsed document
        return {
            "extracted": extracted,
            "full_spec": openapi_dict
        }
    except Exception as e:
        print(f"Error parsing OpenAPI content: {e}")
        raise