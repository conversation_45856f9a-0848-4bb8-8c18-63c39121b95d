"""
Utilities for creating datasets for ML model training.

This module provides utilities for creating, loading, validating, and merging
Hugging Face datasets for machine learning model training. It supports various
task types including sequence-to-sequence mapping and classification.

Author: Airflow LLM Service Mapper Team
Version: 1.0.0
"""

import json
import os
import pendulum
from typing import Dict, List, Any, Optional, Union

class DatasetCreation:
    """Utilities for creating datasets for ML model training"""
    
    @staticmethod
    def create_hf_dataset(
        examples: List[Dict[str, Any]],
        task_type: str,
        output_dir: str,
        train_ratio: float = 0.8,
        seed: int = 42
    ) -> Dict[str, Any]:
        """
        Create a Hugging Face dataset from examples.
        
        Args:
            examples: List of training examples
            task_type: Type of task (mapping, classification, etc.)
            output_dir: Directory to save the dataset
            train_ratio: Ratio of examples to use for training
            seed: Random seed for reproducibility
            
        Returns:
            Dictionary with dataset information
        """
        from datasets import Dataset, DatasetDict
        import numpy as np
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Format examples for the task
        from common.ml.preprocessing import MLPreprocessing
        
        if task_type in ["mapping", "schema_mapping"]:
            formatted = MLPreprocessing.format_for_seq2seq(examples, task_type)
            
            # Convert to Hugging Face dataset
            dataset = Dataset.from_dict({
                "input": [ex["input"] for ex in formatted],
                "output": [ex["output"] for ex in formatted]
            })
            
        elif task_type == "classification":
            features, labels = MLPreprocessing.format_for_classification(
                examples, 
                feature_keys=["name_tokens", "parameter_count", "path_segments", "has_path_params"],
                label_key="operation_type",
                text_field="element_name"
            )
            
            # Convert to Hugging Face dataset
            dataset = Dataset.from_dict({
                "features": features,
                "labels": labels,
                "text": [f.get("text", "") for f in features]
            })
            
        else:
            raise ValueError(f"Unsupported task type: {task_type}")
        
        # Split into train and validation
        train_test_split = dataset.train_test_split(
            test_size=(1 - train_ratio),
            seed=seed
        )
        
        # Create dataset dictionary
        dataset_dict = DatasetDict({
            "train": train_test_split["train"],
            "validation": train_test_split["test"]
        })
        
        # Save to disk
        dataset_dict.save_to_disk(output_dir)
        
        # Save metadata
        metadata = {
            "task_type": task_type,
            "total_examples": len(examples),
            "train_examples": len(dataset_dict["train"]),
            "validation_examples": len(dataset_dict["validation"]),
            "train_ratio": train_ratio,
            "seed": seed,
            "output_dir": output_dir,
            "created_at": json.dumps(pendulum.now().isoformat())
        }

        metadata_path = os.path.join(output_dir, "metadata.json")
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        return {
            "dataset_dict": dataset_dict,
            "metadata": metadata,
            "output_dir": output_dir,
            "train_size": len(dataset_dict["train"]),
            "validation_size": len(dataset_dict["validation"])
        }

    @staticmethod
    def load_hf_dataset(dataset_path: str) -> Dict[str, Any]:
        """
        Load a previously created Hugging Face dataset.

        Args:
            dataset_path: Path to the saved dataset

        Returns:
            Dictionary with dataset and metadata

        Raises:
            FileNotFoundError: If dataset path doesn't exist
            ValueError: If dataset is corrupted or invalid
        """
        from datasets import DatasetDict

        if not os.path.exists(dataset_path):
            raise FileNotFoundError(f"Dataset path not found: {dataset_path}")

        try:
            # Load dataset
            dataset_dict = DatasetDict.load_from_disk(dataset_path)

            # Load metadata if available
            metadata_path = os.path.join(dataset_path, "metadata.json")
            metadata = {}
            if os.path.exists(metadata_path):
                with open(metadata_path, "r", encoding="utf-8") as f:
                    metadata = json.load(f)

            return {
                "dataset_dict": dataset_dict,
                "metadata": metadata,
                "dataset_path": dataset_path
            }

        except Exception as e:
            raise ValueError(f"Failed to load dataset from {dataset_path}: {str(e)}")

    @staticmethod
    def validate_dataset(
        dataset_dict: Any,
        task_type: str,
        min_examples: int = 10
    ) -> Dict[str, Any]:
        """
        Validate a Hugging Face dataset for training.

        Args:
            dataset_dict: Hugging Face DatasetDict
            task_type: Type of task (mapping, classification, etc.)
            min_examples: Minimum number of examples required

        Returns:
            Dictionary with validation results

        Raises:
            ValueError: If validation fails
        """
        from datasets import DatasetDict

        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "statistics": {}
        }

        # Check if it's a DatasetDict
        if not isinstance(dataset_dict, DatasetDict):
            validation_results["is_valid"] = False
            validation_results["errors"].append("Dataset must be a DatasetDict")
            return validation_results

        # Check required splits
        required_splits = ["train", "validation"]
        for split in required_splits:
            if split not in dataset_dict:
                validation_results["is_valid"] = False
                validation_results["errors"].append(f"Missing required split: {split}")

        if not validation_results["is_valid"]:
            return validation_results

        # Check minimum examples
        train_size = len(dataset_dict["train"])
        val_size = len(dataset_dict["validation"])

        if train_size < min_examples:
            validation_results["is_valid"] = False
            validation_results["errors"].append(
                f"Insufficient training examples: {train_size} < {min_examples}"
            )

        if val_size < 1:
            validation_results["is_valid"] = False
            validation_results["errors"].append("No validation examples found")

        # Task-specific validation
        if task_type in ["mapping", "schema_mapping"]:
            required_columns = ["input", "output"]
        elif task_type == "classification":
            required_columns = ["features", "labels", "text"]
        else:
            validation_results["warnings"].append(f"Unknown task type: {task_type}")
            required_columns = []

        # Check required columns
        for split in ["train", "validation"]:
            dataset = dataset_dict[split]
            for column in required_columns:
                if column not in dataset.column_names:
                    validation_results["is_valid"] = False
                    validation_results["errors"].append(
                        f"Missing required column '{column}' in {split} split"
                    )

        # Collect statistics
        validation_results["statistics"] = {
            "train_size": train_size,
            "validation_size": val_size,
            "total_size": train_size + val_size,
            "train_columns": dataset_dict["train"].column_names,
            "validation_columns": dataset_dict["validation"].column_names
        }

        # Add warnings for small datasets
        if train_size < 100:
            validation_results["warnings"].append(
                f"Small training set: {train_size} examples (recommended: 100+)"
            )

        if val_size < 10:
            validation_results["warnings"].append(
                f"Small validation set: {val_size} examples (recommended: 10+)"
            )

        return validation_results

    @staticmethod
    def merge_datasets(
        dataset_paths: List[str],
        output_path: str,
        task_type: str
    ) -> Dict[str, Any]:
        """
        Merge multiple datasets into a single dataset.

        Args:
            dataset_paths: List of paths to datasets to merge
            output_path: Path to save the merged dataset
            task_type: Type of task for validation

        Returns:
            Dictionary with merged dataset information

        Raises:
            ValueError: If datasets are incompatible or invalid
        """
        from datasets import DatasetDict, concatenate_datasets
        import pendulum

        if not dataset_paths:
            raise ValueError("No dataset paths provided")

        merged_datasets = {"train": [], "validation": []}
        total_examples = 0
        source_info = []

        # Load and validate each dataset
        for path in dataset_paths:
            try:
                dataset_info = DatasetCreation.load_hf_dataset(path)
                dataset_dict = dataset_info["dataset_dict"]

                # Validate dataset
                validation = DatasetCreation.validate_dataset(dataset_dict, task_type)
                if not validation["is_valid"]:
                    raise ValueError(f"Invalid dataset at {path}: {validation['errors']}")

                # Add to merge lists
                merged_datasets["train"].append(dataset_dict["train"])
                merged_datasets["validation"].append(dataset_dict["validation"])

                # Track statistics
                stats = validation["statistics"]
                total_examples += stats["total_size"]
                source_info.append({
                    "path": path,
                    "train_size": stats["train_size"],
                    "validation_size": stats["validation_size"],
                    "metadata": dataset_info.get("metadata", {})
                })

            except Exception as e:
                raise ValueError(f"Failed to load dataset from {path}: {str(e)}")

        # Merge datasets
        try:
            merged_train = concatenate_datasets(merged_datasets["train"])
            merged_validation = concatenate_datasets(merged_datasets["validation"])

            merged_dataset_dict = DatasetDict({
                "train": merged_train,
                "validation": merged_validation
            })

        except Exception as e:
            raise ValueError(f"Failed to merge datasets: {str(e)}")

        # Create output directory
        os.makedirs(output_path, exist_ok=True)

        # Save merged dataset
        merged_dataset_dict.save_to_disk(output_path)

        # Save metadata
        metadata = {
            "task_type": task_type,
            "total_examples": total_examples,
            "train_examples": len(merged_train),
            "validation_examples": len(merged_validation),
            "source_datasets": source_info,
            "merged_at": pendulum.now().isoformat(),
            "output_path": output_path
        }

        metadata_path = os.path.join(output_path, "metadata.json")
        with open(metadata_path, "w", encoding="utf-8") as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)

        return {
            "dataset_dict": merged_dataset_dict,
            "metadata": metadata,
            "output_path": output_path,
            "source_count": len(dataset_paths),
            "total_examples": total_examples
        }