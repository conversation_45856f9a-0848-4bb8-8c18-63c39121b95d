"""
Preprocessing utilities for ML model training.
"""

import json
from typing import Dict, <PERSON>, Any, Tu<PERSON>, Optional

class MLPreprocessing:
    """Preprocessing utilities for ML model training"""
    
    @staticmethod
    def format_for_seq2seq(
        examples: List[Dict[str, Any]],
        task_type: str,
        max_input_length: int = 512,
        max_output_length: int = 512
    ) -> List[Dict[str, str]]:
        """
        Format examples for sequence-to-sequence models.
        
        Args:
            examples: List of training examples
            task_type: Type of task (mapping, classification, etc.)
            max_input_length: Maximum input sequence length
            max_output_length: Maximum output sequence length
            
        Returns:
            List of formatted examples with "input" and "output" fields
        """
        formatted_examples = []
        
        for example in examples:
            if task_type == "mapping" and example["source_type"] == "mapping":
                # Format for WSDL to OpenAPI mapping
                wsdl_op = example.get("wsdl_operation", {})
                openapi_path = example.get("openapi_path", {})
                
                input_text = (f"Map WSDL operation: {wsdl_op.get('name', '')} "
                             f"from service: {example.get('service_name', '')}")
                
                output_text = (f"OpenAPI path: {openapi_path.get('path', '')} "
                              f"method: {openapi_path.get('method', '')} "
                              f"operationId: {openapi_path.get('operationId', 'none')}")
                
            elif task_type == "classification" and example["source_type"] in ["wsdl", "openapi"]:
                # Format for operation classification
                element_data = example.get("element_data", {})
                element_name = example.get("element_name", "")
                
                input_text = f"Classify operation: {element_name} from service: {example.get('service_name', '')}"
                output_text = f"Type: {example.get('features', {}).get('operation_type', 'unknown')}"
                
            elif task_type == "schema_mapping" and "schema" in example.get("element_type", ""):
                # Format for schema mapping
                element_data = example.get("element_data", {})
                element_name = example.get("element_name", "")
                
                input_text = (f"Convert {example.get('source_type', '')} schema: {element_name} "
                             f"from service: {example.get('service_name', '')}")
                
                # Truncate schema structure to fit max length
                schema_str = json.dumps(element_data)
                if len(schema_str) > max_output_length - 20:  # Leave room for prefix
                    schema_str = schema_str[:max_output_length - 23] + "..."
                
                output_text = f"Schema structure: {schema_str}"
                
            else:
                # Skip examples that don't match the task type
                continue
            
            # Truncate input if needed
            if len(input_text) > max_input_length:
                input_text = input_text[:max_input_length - 3] + "..."
            
            # Truncate output if needed
            if len(output_text) > max_output_length:
                output_text = output_text[:max_output_length - 3] + "..."
            
            formatted_examples.append({
                "input": input_text,
                "output": output_text
            })
        
        return formatted_examples
    
    @staticmethod
    def format_for_classification(
        examples: List[Dict[str, Any]],
        feature_keys: List[str],
        label_key: str,
        text_field: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        Format examples for classification models.
        
        Args:
            examples: List of training examples
            feature_keys: List of feature keys to include
            label_key: Key for the label
            text_field: Optional field to use as text input
            
        Returns:
            Tuple of (features, labels)
        """
        features = []
        labels = []
        
        for example in examples:
            # Extract features
            feature_dict = {}
            for key in feature_keys:
                if key in example.get("features", {}):
                    feature_dict[key] = example["features"][key]
                elif key in example:
                    feature_dict[key] = example[key]
            
            # Add text field if specified
            if text_field and text_field in example:
                feature_dict["text"] = example[text_field]
            
            # Extract label
            label = None
            if label_key in example.get("features", {}):
                label = example["features"][label_key]
            elif label_key in example:
                label = example[label_key]
            
            if label is not None:
                features.append(feature_dict)
                labels.append(label)
        
        return features, labels