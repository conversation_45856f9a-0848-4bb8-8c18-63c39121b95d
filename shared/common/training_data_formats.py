"""
Standardized formats for storing training data from various sources.
"""

import json
import os
from typing import Dict, List, Any, Optional, Union

class TrainingDataFormats:
    """Standard formats for training data storage"""
    
    @staticmethod
    def create_base_example(source_type: str, service_name: str) -> Dict[str, Any]:
        """Create a base example with common fields"""
        return {
            "id": "",  # Will be filled with a unique ID
            "source_type": source_type,  # e.g., "wsdl", "openapi", "soap_log", etc.
            "service_name": service_name,
            "timestamp": "",  # When the example was created
            "metadata": {},   # Additional metadata
            "features": {},   # Extracted features for ML
            "labels": {}      # Optional labels for supervised learning
        }
    
    @staticmethod
    def format_definition_example(
        definition_type: str,
        service_name: str,
        element_type: str,
        element_name: str,
        element_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Format a service definition example (WSDL or OpenAPI)"""
        import hashlib
        import time
        
        example = TrainingDataFormats.create_base_example(definition_type, service_name)
        
        # Create a unique ID based on content
        content_hash = hashlib.md5(f"{service_name}:{element_type}:{element_name}:{time.time()}".encode()).hexdigest()
        example["id"] = f"{definition_type}_{element_type}_{content_hash[:10]}"
        
        example["timestamp"] = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        example["element_type"] = element_type  # "operation", "schema", "path", etc.
        example["element_name"] = element_name
        example["element_data"] = element_data
        
        # Extract features based on element type
        if element_type == "operation" and definition_type == "wsdl":
            example["features"] = {
                "has_input": "input_message" in element_data and bool(element_data["input_message"]),
                "has_output": "output_message" in element_data and bool(element_data["output_message"]),
                "is_one_way": "output_message" not in element_data or not element_data["output_message"],
                "parameter_count": len(element_data.get("parameters", [])),
                "name_tokens": element_name.split()  # Simple tokenization
            }
        elif element_type == "path" and definition_type == "openapi":
            example["features"] = {
                "http_method": element_data.get("method", "").lower(),
                "path_segments": len(element_name.split("/")),
                "has_path_params": "{" in element_name,
                "has_query_params": bool(element_data.get("parameters", [])),
                "has_request_body": bool(element_data.get("requestBody")),
                "response_codes": list(element_data.get("responses", {}).keys())
            }
        elif element_type == "schema":
            example["features"] = {
                "property_count": len(element_data.get("properties", {})),
                "is_array": element_data.get("type") == "array",
                "is_object": element_data.get("type") == "object",
                "has_required_props": bool(element_data.get("required", [])),
                "nesting_level": TrainingDataFormats._calculate_nesting_level(element_data)
            }
        
        return example
    
    @staticmethod
    def format_log_example(
        log_type: str,
        service_name: str,
        operation_name: str,
        request_data: Dict[str, Any],
        response_data: Dict[str, Any],
        matched_definition: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Format a log entry example (SOAP or REST)"""
        import hashlib
        import time
        
        example = TrainingDataFormats.create_base_example(f"{log_type}_log", service_name)
        
        # Create a unique ID based on content
        content_hash = hashlib.md5(f"{service_name}:{operation_name}:{time.time()}".encode()).hexdigest()
        example["id"] = f"{log_type}_log_{content_hash[:10]}"
        
        example["timestamp"] = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        example["operation_name"] = operation_name
        example["request"] = request_data
        example["response"] = response_data
        
        if matched_definition:
            example["matched_definition"] = matched_definition
            example["is_matched"] = True
        else:
            example["is_matched"] = False
        
        # Extract features based on log type
        if log_type == "soap":
            example["features"] = {
                "request_size": len(str(request_data)),
                "response_size": len(str(response_data)),
                "is_fault": "Fault" in str(response_data),
                "operation_tokens": operation_name.split()  # Simple tokenization
            }
        elif log_type == "rest":
            example["features"] = {
                "http_method": request_data.get("method", "").lower(),
                "status_code": response_data.get("status", 0),
                "is_success": 200 <= response_data.get("status", 0) < 300,
                "is_error": response_data.get("status", 0) >= 400,
                "request_size": len(str(request_data.get("body", ""))),
                "response_size": len(str(response_data.get("body", "")))
            }
        
        return example
    
    @staticmethod
    def format_mapping_example(
        service_name: str,
        wsdl_operation: Dict[str, Any],
        openapi_path: Dict[str, Any],
        confidence: float = 0.0,
        mapping_source: str = "heuristic"
    ) -> Dict[str, Any]:
        """Format a mapping example (WSDL operation to OpenAPI path)"""
        import hashlib
        import time
        
        example = TrainingDataFormats.create_base_example("mapping", service_name)
        
        # Create a unique ID based on content
        content_hash = hashlib.md5(
            f"{service_name}:{wsdl_operation.get('name')}:{openapi_path.get('path')}:{time.time()}".encode()
        ).hexdigest()
        example["id"] = f"mapping_{content_hash[:10]}"
        
        example["timestamp"] = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
        example["wsdl_operation"] = wsdl_operation
        example["openapi_path"] = openapi_path
        example["mapping_confidence"] = confidence
        example["mapping_source"] = mapping_source  # "heuristic", "manual", "llm", etc.
        
        # Extract features for the mapping
        example["features"] = {
            "name_similarity": TrainingDataFormats._calculate_name_similarity(
                wsdl_operation.get("name", ""), 
                openapi_path.get("operationId", "")
            ),
            "wsdl_has_input": "input_message" in wsdl_operation and bool(wsdl_operation["input_message"]),
            "wsdl_has_output": "output_message" in wsdl_operation and bool(wsdl_operation["output_message"]),
            "openapi_method": openapi_path.get("method", "").lower(),
            "openapi_has_request_body": bool(openapi_path.get("requestBody")),
            "parameter_count_match": abs(
                len(wsdl_operation.get("parameters", [])) - 
                len(openapi_path.get("parameters", []))
            )
        }
        
        return example
    
    @staticmethod
    def _calculate_nesting_level(schema_data: Dict[str, Any], current_level: int = 0) -> int:
        """Calculate the nesting level of a schema"""
        if not isinstance(schema_data, dict):
            return current_level
        
        max_level = current_level
        
        # Check properties for objects
        for prop, prop_schema in schema_data.get("properties", {}).items():
            if isinstance(prop_schema, dict):
                prop_level = TrainingDataFormats._calculate_nesting_level(prop_schema, current_level + 1)
                max_level = max(max_level, prop_level)
        
        # Check items for arrays
        if "items" in schema_data and isinstance(schema_data["items"], dict):
            items_level = TrainingDataFormats._calculate_nesting_level(schema_data["items"], current_level + 1)
            max_level = max(max_level, items_level)
        
        return max_level
    
    @staticmethod
    def _calculate_name_similarity(name1: str, name2: str) -> float:
        """Calculate simple similarity between two names"""
        if not name1 or not name2:
            return 0.0
        
        # Convert to lowercase and split into tokens
        tokens1 = set(name1.lower().replace("_", " ").replace("-", " ").split())
        tokens2 = set(name2.lower().replace("_", " ").replace("-", " ").split())
        
        # Calculate Jaccard similarity
        intersection = len(tokens1.intersection(tokens2))
        union = len(tokens1.union(tokens2))
        
        return intersection / union if union > 0 else 0.0