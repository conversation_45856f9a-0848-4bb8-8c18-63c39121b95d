# dags/common/models/service_definition.py
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional

@dataclass
class Parameter:
    name: str
    type: str
    required: bool = True
    description: Optional[str] = None

@dataclass
class Operation:
    name: str
    input_message: Optional[str] = None
    output_message: Optional[str] = None
    parameters: List[Parameter] = field(default_factory=list)
    documentation: Optional[str] = None

@dataclass
class Service:
    name: str
    operations: List[Operation] = field(default_factory=list)
    ports: List[Dict[str, Any]] = field(default_factory=list)
    documentation: Optional[str] = None

@dataclass
class ServiceDefinition:
    """Unified representation of a service definition from any source"""
    service_name: str
    source_type: str  # "wsdl", "java", "csharp", etc.
    services: List[Service] = field(default_factory=list)
    types: List[Dict[str, Any]] = field(default_factory=list)
    raw_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for storage"""
        # Implementation
        pass
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ServiceDefinition':
        """Create from dictionary"""
        # Implementation
        pass