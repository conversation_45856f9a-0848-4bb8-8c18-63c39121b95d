# dags/common/definition_store.py
from airflow.models import Variable
from airflow.hooks.filesystem import FSHook
from airflow.providers.amazon.aws.hooks.s3 import S3Hook
import os
import json
from typing import Dict, Any, Optional, List, Tuple

class DefinitionStore:
    """
    Utility class to access stored service definitions from any DAG.
    Provides methods to retrieve parsed definitions without re-parsing.
    """
    
    @staticmethod
    def get_service_catalog() -> Dict[str, Any]:
        """Get the full service definitions catalog"""
        try:
            return Variable.get("service_definitions_catalog", deserialize_json=True)
        except (KeyError, ValueError):
            # Return empty catalog if not found
            return {"services": {}, "last_updated": None}
    
    @staticmethod
    def list_available_services() -> List[str]:
        """List all available service names in the catalog"""
        catalog = DefinitionStore.get_service_catalog()
        return list(catalog.get("services", {}).keys())
    
    @staticmethod
    def get_service_metadata(service_name: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific service"""
        catalog = DefinitionStore.get_service_catalog()
        return catalog.get("services", {}).get(service_name)
    
    @staticmethod
    def load_parsed_definition(service_name: str, definition_type: str = "all") -> Dict[str, Any]:
        """
        Load a parsed definition for a service.
        
        Args:
            service_name: Name of the service to load
            definition_type: Type of definition to load ("wsdl", "xsd", "openapi", or "all")
            
        Returns:
            Dictionary containing the parsed definition(s)
        """
        # Get service metadata
        metadata = DefinitionStore.get_service_metadata(service_name)
        if not metadata:
            raise ValueError(f"Service '{service_name}' not found in catalog")
        
        result = {}
        
        # Determine which files to load
        files_to_load = {}
        if definition_type == "all":
            files_to_load = {
                "wsdl": metadata.get("files", {}).get("parsed_wsdl_path"),
                "xsd": metadata.get("files", {}).get("parsed_xsd_path"),
                "openapi": metadata.get("files", {}).get("parsed_openapi_path")
            }
        else:
            key = f"parsed_{definition_type}_path"
            path = metadata.get("files", {}).get(key)
            if path:
                files_to_load[definition_type] = path
        
        # Load each file
        for def_type, file_path in files_to_load.items():
            if not file_path:
                continue
                
            try:
                # Handle different storage locations
                if file_path.startswith("s3://"):
                    # Extract bucket and key from S3 path
                    s3_path = file_path.replace("s3://", "")
                    bucket, key = s3_path.split("/", 1)
                    
                    # Use S3Hook to get the file
                    s3_hook = S3Hook()
                    file_content = s3_hook.read_key(key=key, bucket_name=bucket)
                    parsed_data = json.loads(file_content)
                else:
                    # Local file
                    with open(file_path, 'r') as f:
                        parsed_data = json.load(f)
                
                result[def_type] = parsed_data
            except Exception as e:
                print(f"Error loading {def_type} definition from {file_path}: {e}")
                result[f"{def_type}_error"] = str(e)
        
        return result
    
    @staticmethod
    def get_latest_version_path(service_name: str) -> str:
        """Get the path to the latest version of a service definition"""
        metadata = DefinitionStore.get_service_metadata(service_name)
        if not metadata:
            raise ValueError(f"Service '{service_name}' not found in catalog")
            
        # Extract base path from any file path
        for path_key, path in metadata.get("files", {}).items():
            if path:
                # Get directory containing the file
                dir_path = os.path.dirname(path)
                # Get parent directory (service version directory)
                service_dir = os.path.dirname(dir_path)
                return f"{service_dir}/latest"
        
        raise ValueError(f"No file paths found for service '{service_name}'")