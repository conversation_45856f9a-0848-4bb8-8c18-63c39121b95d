[{"id": "openapi_path_e5f4g3h2i1", "source_type": "openapi", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "path", "element_name": "/hello", "element_data": {"path": "/hello", "method": "post", "operationId": "sayHello", "summary": "Say Hello", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelloRequest"}}}}, "responses": {"200": {"description": "A greeting message", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelloResponse"}}}}}}, "features": {"http_method": "post", "path_segments": 1, "has_path_params": false, "has_query_params": false, "has_request_body": true, "response_codes": ["200"]}, "metadata": {"source_file": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"operation_type": "query"}}, {"id": "openapi_path_j5k4l3m2n1", "source_type": "openapi", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "path", "element_name": "/greetings/{id}", "element_data": {"path": "/greetings/{id}", "method": "put", "operationId": "updateGreeting", "summary": "Update a greeting", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}}}}, "responses": {"200": {"description": "Updated greeting", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelloResponse"}}}}, "404": {"description": "Greeting not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "features": {"http_method": "put", "path_segments": 2, "has_path_params": true, "has_query_params": false, "has_request_body": true, "response_codes": ["200", "404"]}, "metadata": {"source_file": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"operation_type": "mutation"}}, {"id": "openapi_path_o5p4q3r2s1", "source_type": "openapi", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "path", "element_name": "/greetings", "element_data": {"path": "/greetings", "method": "get", "operationId": "listGreetings", "summary": "List all greetings", "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "default": 0}}], "responses": {"200": {"description": "A list of greetings", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HelloResponse"}}}}}}}, "features": {"http_method": "get", "path_segments": 1, "has_path_params": false, "has_query_params": true, "has_request_body": false, "response_codes": ["200"]}, "metadata": {"source_file": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"operation_type": "query"}}]