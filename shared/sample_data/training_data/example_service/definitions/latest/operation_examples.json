[{"id": "wsdl_operation_a7b3c9d8e2", "source_type": "wsdl", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "operation", "element_name": "sayHello", "element_data": {"name": "sayHello", "input_message": "HelloRequest", "output_message": "HelloResponse", "documentation": "Says hello to the specified person", "parameters": [{"name": "firstName", "type": "string", "required": true}]}, "features": {"has_input": true, "has_output": true, "is_one_way": false, "parameter_count": 1, "name_tokens": ["say", "Hello"]}, "metadata": {"source_file": "include/sample_data/wsdl_source/HelloService.wsdl"}, "labels": {"operation_type": "query", "complexity": "simple"}}, {"id": "wsdl_operation_f5e4d3c2b1", "source_type": "wsdl", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "operation", "element_name": "updateGreeting", "element_data": {"name": "updateGreeting", "input_message": "UpdateGreetingRequest", "output_message": "UpdateGreetingResponse", "documentation": "Updates the greeting message", "parameters": [{"name": "greetingId", "type": "integer", "required": true}, {"name": "newMessage", "type": "string", "required": true}]}, "features": {"has_input": true, "has_output": true, "is_one_way": false, "parameter_count": 2, "name_tokens": ["update", "Greeting"]}, "metadata": {"source_file": "include/sample_data/wsdl_source/HelloService.wsdl"}, "labels": {"operation_type": "mutation", "complexity": "medium"}}, {"id": "wsdl_operation_1a2b3c4d5e", "source_type": "wsdl", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "operation", "element_name": "deleteGreeting", "element_data": {"name": "deleteGreeting", "input_message": "DeleteGreetingRequest", "output_message": null, "documentation": "Deletes a greeting", "parameters": [{"name": "greetingId", "type": "integer", "required": true}]}, "features": {"has_input": true, "has_output": false, "is_one_way": true, "parameter_count": 1, "name_tokens": ["delete", "Greeting"]}, "metadata": {"source_file": "include/sample_data/wsdl_source/HelloService.wsdl"}, "labels": {"operation_type": "mutation", "complexity": "simple"}}, {"id": "wsdl_operation_9z8y7x6w5v", "source_type": "wsdl", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "operation", "element_name": "listGreetings", "element_data": {"name": "listGreetings", "input_message": "ListGreetingsRequest", "output_message": "ListGreetingsResponse", "documentation": "Lists all available greetings", "parameters": [{"name": "limit", "type": "integer", "required": false}, {"name": "offset", "type": "integer", "required": false}]}, "features": {"has_input": true, "has_output": true, "is_one_way": false, "parameter_count": 2, "name_tokens": ["list", "Greetings"]}, "metadata": {"source_file": "include/sample_data/wsdl_source/HelloService.wsdl"}, "labels": {"operation_type": "query", "complexity": "medium"}}]