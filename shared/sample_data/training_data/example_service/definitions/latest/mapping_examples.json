[{"id": "mapping_t5u4v3w2x1", "source_type": "mapping", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "wsdl_operation": {"name": "sayHello", "input_message": "HelloRequest", "output_message": "HelloResponse", "documentation": "Says hello to the specified person", "parameters": [{"name": "firstName", "type": "string", "required": true}]}, "openapi_path": {"path": "/hello", "method": "post", "operationId": "sayHello", "summary": "Say Hello"}, "mapping_confidence": 0.95, "mapping_source": "heuristic", "features": {"name_similarity": 1.0, "wsdl_has_input": true, "wsdl_has_output": true, "openapi_method": "post", "openapi_has_request_body": true, "parameter_count_match": 0}, "metadata": {"wsdl_source": "include/sample_data/wsdl_source/HelloService.wsdl", "openapi_source": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"is_correct_mapping": true}}, {"id": "mapping_y5z4a3b2c1", "source_type": "mapping", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "wsdl_operation": {"name": "updateGreeting", "input_message": "UpdateGreetingRequest", "output_message": "UpdateGreetingResponse", "documentation": "Updates the greeting message", "parameters": [{"name": "greetingId", "type": "integer", "required": true}, {"name": "newMessage", "type": "string", "required": true}]}, "openapi_path": {"path": "/greetings/{id}", "method": "put", "operationId": "updateGreeting", "summary": "Update a greeting"}, "mapping_confidence": 0.9, "mapping_source": "heuristic", "features": {"name_similarity": 1.0, "wsdl_has_input": true, "wsdl_has_output": true, "openapi_method": "put", "openapi_has_request_body": true, "parameter_count_match": 0}, "metadata": {"wsdl_source": "include/sample_data/wsdl_source/HelloService.wsdl", "openapi_source": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"is_correct_mapping": true}}, {"id": "mapping_d5e4f3g2h1", "source_type": "mapping", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "wsdl_operation": {"name": "listGreetings", "input_message": "ListGreetingsRequest", "output_message": "ListGreetingsResponse", "documentation": "Lists all available greetings", "parameters": [{"name": "limit", "type": "integer", "required": false}, {"name": "offset", "type": "integer", "required": false}]}, "openapi_path": {"path": "/greetings", "method": "get", "operationId": "listGreetings", "summary": "List all greetings"}, "mapping_confidence": 0.95, "mapping_source": "heuristic", "features": {"name_similarity": 1.0, "wsdl_has_input": true, "wsdl_has_output": true, "openapi_method": "get", "openapi_has_request_body": false, "parameter_count_match": 0}, "metadata": {"wsdl_source": "include/sample_data/wsdl_source/HelloService.wsdl", "openapi_source": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"is_correct_mapping": true}}]