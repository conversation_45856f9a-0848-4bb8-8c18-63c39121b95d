[{"id": "wsdl_operation_a7b3c9d8e2", "source_type": "wsdl", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "operation", "element_name": "sayHello", "element_data": {"name": "sayHello", "input_message": "HelloRequest", "output_message": "HelloResponse", "documentation": "Says hello to the specified person", "parameters": [{"name": "firstName", "type": "string", "required": true}]}, "features": {"has_input": true, "has_output": true, "is_one_way": false, "parameter_count": 1, "name_tokens": ["say", "Hello"]}, "metadata": {"source_file": "include/sample_data/wsdl_source/HelloService.wsdl"}, "labels": {"operation_type": "query", "complexity": "simple"}}, {"id": "wsdl_schema_5f6g7h8i9j", "source_type": "wsdl", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "schema", "element_name": "HelloRequest", "element_data": {"name": "HelloRequest", "type": "object", "namespace": "http://example.org/hello", "properties": {"firstName": {"type": "string", "minLength": 1, "maxLength": 100}}, "required": ["firstName"]}, "features": {"property_count": 1, "is_array": false, "is_object": true, "has_required_props": true, "nesting_level": 1}, "metadata": {"source_file": "include/sample_data/wsdl_source/HelloService.wsdl"}, "labels": {"complexity": "simple"}}, {"id": "openapi_path_e5f4g3h2i1", "source_type": "openapi", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "path", "element_name": "/hello", "element_data": {"path": "/hello", "method": "post", "operationId": "sayHello", "summary": "Say Hello", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelloRequest"}}}}, "responses": {"200": {"description": "A greeting message", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelloResponse"}}}}}}, "features": {"http_method": "post", "path_segments": 1, "has_path_params": false, "has_query_params": false, "has_request_body": true, "response_codes": ["200"]}, "metadata": {"source_file": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"operation_type": "query"}}, {"id": "mapping_t5u4v3w2x1", "source_type": "mapping", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "wsdl_operation": {"name": "sayHello", "input_message": "HelloRequest", "output_message": "HelloResponse", "documentation": "Says hello to the specified person", "parameters": [{"name": "firstName", "type": "string", "required": true}]}, "openapi_path": {"path": "/hello", "method": "post", "operationId": "sayHello", "summary": "Say Hello"}, "mapping_confidence": 0.95, "mapping_source": "heuristic", "features": {"name_similarity": 1.0, "wsdl_has_input": true, "wsdl_has_output": true, "openapi_method": "post", "openapi_has_request_body": true, "parameter_count_match": 0}, "metadata": {"wsdl_source": "include/sample_data/wsdl_source/HelloService.wsdl", "openapi_source": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"is_correct_mapping": true}}]