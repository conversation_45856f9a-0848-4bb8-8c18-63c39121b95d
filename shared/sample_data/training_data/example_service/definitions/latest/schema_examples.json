[{"id": "wsdl_schema_5f6g7h8i9j", "source_type": "wsdl", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "schema", "element_name": "HelloRequest", "element_data": {"name": "HelloRequest", "type": "object", "namespace": "http://example.org/hello", "properties": {"firstName": {"type": "string", "minLength": 1, "maxLength": 100}}, "required": ["firstName"]}, "features": {"property_count": 1, "is_array": false, "is_object": true, "has_required_props": true, "nesting_level": 1}, "metadata": {"source_file": "include/sample_data/wsdl_source/HelloService.wsdl"}, "labels": {"complexity": "simple"}}, {"id": "wsdl_schema_k1l2m3n4o5", "source_type": "wsdl", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "schema", "element_name": "HelloResponse", "element_data": {"name": "HelloResponse", "type": "object", "namespace": "http://example.org/hello", "properties": {"greeting": {"type": "string"}}, "required": ["greeting"]}, "features": {"property_count": 1, "is_array": false, "is_object": true, "has_required_props": true, "nesting_level": 1}, "metadata": {"source_file": "include/sample_data/wsdl_source/HelloService.wsdl"}, "labels": {"complexity": "simple"}}, {"id": "openapi_schema_p6q7r8s9t0", "source_type": "openapi", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "schema", "element_name": "HelloRequest", "element_data": {"type": "object", "properties": {"firstName": {"type": "string", "description": "The first name of the person to greet.", "example": "World"}}, "required": ["firstName"]}, "features": {"property_count": 1, "is_array": false, "is_object": true, "has_required_props": true, "nesting_level": 1}, "metadata": {"source_file": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"complexity": "simple"}}, {"id": "openapi_schema_u1v2w3x4y5", "source_type": "openapi", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "schema", "element_name": "HelloResponse", "element_data": {"type": "object", "properties": {"greeting": {"type": "string", "description": "The greeting message.", "example": "Hello, <PERSON>!"}}, "required": ["greeting"]}, "features": {"property_count": 1, "is_array": false, "is_object": true, "has_required_props": true, "nesting_level": 1}, "metadata": {"source_file": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"complexity": "simple"}}, {"id": "openapi_schema_z5a4b3c2d1", "source_type": "openapi", "service_name": "example_service", "timestamp": "2023-05-30T12:13:09Z", "element_type": "schema", "element_name": "ErrorResponse", "element_data": {"type": "object", "properties": {"code": {"type": "integer", "description": "Error code"}, "message": {"type": "string", "description": "Error message"}, "details": {"type": "array", "items": {"type": "object", "properties": {"field": {"type": "string"}, "issue": {"type": "string"}}}}}, "required": ["code", "message"]}, "features": {"property_count": 3, "is_array": false, "is_object": true, "has_required_props": true, "nesting_level": 3}, "metadata": {"source_file": "include/sample_data/osdmp_target/test.yaml"}, "labels": {"complexity": "complex"}}]