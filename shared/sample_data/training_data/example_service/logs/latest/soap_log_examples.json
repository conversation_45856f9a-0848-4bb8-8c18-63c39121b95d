[
  {
    "id": "soap_log_i5j4k3l2m1",
    "source_type": "soap_log",
    "service_name": "example_service",
    "timestamp": "2023-05-30T12:13:10Z",
    "operation_name": "sayHello",
    "request": {
      "xml": "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:hel=\"http://example.org/hello\"><soapenv:Header/><soapenv:Body><hel:HelloRequest><hel:firstName>John</hel:firstName></hel:HelloRequest></soapenv:Body></soapenv:Envelope>",
      "parsed": {
        "firstName": "<PERSON>"
      }
    },
    "response": {
      "xml": "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:hel=\"http://example.org/hello\"><soapenv:Header/><soapenv:Body><hel:HelloResponse><hel:greeting>Hello, <PERSON>!</hel:greeting></hel:HelloResponse></soapenv:Body></soapenv:Envelope>",
      "parsed": {
        "greeting": "Hello, <PERSON>!"
      }
    },
    "is_matched": true,
    "matched_definition": {
      "name": "sayHello",
      "input_message": "HelloRequest",
      "output_message": "HelloResponse"
    },
    "features": {
      "request_size": 293,
      "response_size": 298,
      "is_fault": false,
      "operation_tokens": ["say", "Hello"]
    },
    "metadata": {
      "source_file": "include/sample_data/logs/soap/example_service_20230530.log",
      "client_ip": "*************",
      "timestamp": "2023-05-30T10:15:23Z"
    },
    "
</augment_code_snippet>