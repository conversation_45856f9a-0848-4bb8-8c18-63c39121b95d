2023-05-30 12:13:05,123 INFO  [org.example.service.HelloServiceImpl] Starting HelloService on host-1234 with PID 5678
2023-05-30 12:13:06,234 INFO  [org.example.service.HelloServiceImpl] Service initialized successfully
2023-05-30 12:13:07,345 INFO  [org.example.service.HelloServiceImpl] WSDL published at: http://localhost:8080/services/HelloService?wsdl
2023-05-30 12:13:10,456 DEBUG [org.example.service.HelloServiceImpl] Received SOAP request for operation: sayHello
2023-05-30 12:13:10,567 DEBUG [org.example.service.HelloServiceImpl] Request payload: <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:hel="http://example.org/hello"><soapenv:Header/><soapenv:Body><hel:HelloRequest><hel:firstName>John</hel:firstName></hel:HelloRequest></soapenv:Body></soapenv:Envelope>
2023-05-30 12:13:10,678 DEBUG [org.example.service.HelloServiceImpl] Processing request with firstName: John
2023-05-30 12:13:10,789 DEBUG [org.example.service.HelloServiceImpl] Sending response: <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:hel="http://example.org/hello"><soapenv:Header/><soapenv:Body><hel:HelloResponse><hel:greeting>Hello, John!</hel:greeting></hel:HelloResponse></soapenv:Body></soapenv:Envelope>
2023-05-30 12:13:15,890 DEBUG [org.example.service.HelloServiceImpl] Received SOAP request for operation: updateGreeting
2023-05-30 12:13:15,901 DEBUG [org.example.service.HelloServiceImpl] Request payload: <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:hel="http://example.org/hello"><soapenv:Header/><soapenv:Body><hel:UpdateGreetingRequest><hel:greetingId>123</hel:greetingId><hel:newMessage>Welcome, User!</hel:newMessage></hel:UpdateGreetingRequest></soapenv:Body></soapenv:Envelope>
2023-05-30 12:13:15,912 DEBUG [org.example.service.HelloServiceImpl] Processing updateGreeting request with ID: 123, newMessage: Welcome, User!
2023-05-30 12:13:15,923 DEBUG [org.example.service.HelloServiceImpl] Sending response: <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:hel="http://example.org/hello"><soapenv:Header/><soapenv:Body><hel:HelloResponse><hel:greeting>Welcome, User!</hel:greeting></hel:HelloResponse></soapenv:Body></soapenv:Envelope>
2023-05-30 12:13:20,034 DEBUG [org.example.service.HelloServiceImpl] Received SOAP request for operation: listGreetings
2023-05-30 12:13:20,045 DEBUG [org.example.service.HelloServiceImpl] Request payload: <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:hel="http://example.org/hello"><soapenv:Header/><soapenv:Body><hel:ListGreetingsRequest><hel:limit>10</hel:limit><hel:offset>0</hel:offset></hel:ListGreetingsRequest></soapenv:Body></soapenv:Envelope>
2023-05-30 12:13:20,056 DEBUG [org.example.service.HelloServiceImpl] Processing listGreetings request with limit: 10, offset: 0
2023-05-30 12:13:20,067 DEBUG [org.example.service.HelloServiceImpl] Sending response with 1 greetings
2023-05-30 12:13:25,178 DEBUG [org.example.service.HelloServiceImpl] Received SOAP request for operation: deleteGreeting
2023-05-30 12:13:25,189 DEBUG [org.example.service.HelloServiceImpl] Request payload: <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:hel="http://example.org/hello"><soapenv:Header/><soapenv:Body><hel:DeleteGreetingRequest><hel:greetingId>123</hel:greetingId></hel:DeleteGreetingRequest></soapenv:Body></soapenv:Envelope>
2023-05-30 12:13:25,200 DEBUG [org.example.service.HelloServiceImpl] Processing deleteGreeting request with ID: 123
2023-05-30 12:13:25,211 DEBUG [org.example.service.HelloServiceImpl] Deleted greeting with ID: 123
2023-05-30 12:13:25,222 INFO  [org.example.service.HelloServiceImpl] Operation completed successfully