[INFO] Scanning for projects...
[INFO] 
[INFO] -----------------< org.example:hello-service-example >-----------------
[INFO] Building hello-service-example 1.0-SNAPSHOT
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- maven-clean-plugin:3.1.0:clean (default-clean) @ hello-service-example ---
[INFO] Deleting /home/<USER>/projects/hello-service-example/target
[INFO] 
[INFO] --- maven-resources-plugin:3.0.2:resources (default-resources) @ hello-service-example ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] skip non existing resourceDirectory /home/<USER>/projects/hello-service-example/src/main/resources
[INFO] 
[INFO] --- maven-compiler-plugin:3.8.0:compile (default-compile) @ hello-service-example ---
[INFO] Changes detected - recompiling the module!
[INFO] Compiling 4 source files to /home/<USER>/projects/hello-service-example/target/classes
[INFO] 
[INFO] --- jaxws-maven-plugin:2.6:wsimport (generate-jaxws-sources) @ hello-service-example ---
[INFO] Processing: file:/home/<USER>/projects/hello-service-example/src/main/resources/wsdl/HelloService.wsdl
[INFO] jaxws:wsimport args: [-keep, -s, '/home/<USER>/projects/hello-service-example/target/generated-sources/wsdl', -d, '/home/<USER>/projects/hello-service-example/target/classes', -verbose, -extension, -Xnocompile, file:/home/<USER>/projects/hello-service-example/src/main/resources/wsdl/HelloService.wsdl]
parsing WSDL...

Generating code...

Compiling code...

[INFO] Generated 4 source files to /home/<USER>/projects/hello-service-example/target/generated-sources/wsdl
[INFO] 
[INFO] --- maven-resources-plugin:3.0.2:testResources (default-testResources) @ hello-service-example ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] skip non existing resourceDirectory /home/<USER>/projects/hello-service-example/src/test/resources
[INFO] 
[INFO] --- maven-compiler-plugin:3.8.0:testCompile (default-testCompile) @ hello-service-example ---
[INFO] No sources to compile
[INFO] 
[INFO] --- maven-surefire-plugin:2.22.1:test (default-test) @ hello-service-example ---
[INFO] No tests to run.
[INFO] 
[INFO] --- maven-jar-plugin:3.0.2:jar (default-jar) @ hello-service-example ---
[INFO] Building jar: /home/<USER>/projects/hello-service-example/target/hello-service-example-1.0-SNAPSHOT.jar
[INFO] 
[INFO] --- maven-install-plugin:2.5.2:install (default-install) @ hello-service-example ---
[INFO] Installing /home/<USER>/projects/hello-service-example/target/hello-service-example-1.0-SNAPSHOT.jar to /home/<USER>/.m2/repository/org/example/hello-service-example/1.0-SNAPSHOT/hello-service-example-1.0-SNAPSHOT.jar
[INFO] Installing /home/<USER>/projects/hello-service-example/pom.xml to /home/<USER>/.m2/repository/org/example/hello-service-example/1.0-SNAPSHOT/hello-service-example-1.0-SNAPSHOT.pom
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  3.542 s
[INFO] Finished at: 2023-05-30T14:25:37+00:00
[INFO] ------------------------------------------------------------------------