<?xml version="1.0" encoding="utf-8" ?>
<!--Created with Liquid XML Studio - FREE Community Edition 7.0.2.746 (http://www.liquid-technologies.com) -->
<xs:schema
	xmlns:tns="http://petra.linkon.se/gtssales/data/v1_27"
	xmlns:lce="http://petra.linkon.se/commonelements/data/v4_7"
	xmlns:lcce="http://petra.linkon.se/commoncomplexelements/data/v4_7"
	elementFormDefault="qualified"
	targetNamespace="http://petra.linkon.se/gtssales/data/v1_27"
	version="1.27.202550.3" xmlns:xs="http://www.w3.org/2001/XMLSchema">

	<xs:import schemaLocation="commonelements.xsd"
		namespace="http://petra.linkon.se/commonelements/data/v4_7" />

	<xs:import schemaLocation="commoncomplexelements.xsd"
		namespace="http://petra.linkon.se/commoncomplexelements/data/v4_7" />

	<xs:import schemaLocation="gtssales-booktransport.xsd"
		namespace="http://petra.linkon.se/gtssales/booktransport/data/v1_27" />

	<xs:import schemaLocation="gtssales-edittransport.xsd"
		namespace="http://petra.linkon.se/gtssales/edittransport/data/v1_27" />

	<xs:import schemaLocation="gtssales-itinerarycharacteristics.xsd"
		namespace="http://petra.linkon.se/gtssales/itinerarycharacteristics/data/v1_27" />

	<xs:import schemaLocation="gtssales-transportactions.xsd"
		namespace="http://petra.linkon.se/gtssales/transportactions/data/v1_27" />

	<xs:import schemaLocation="gtssales-validpricegroups.xsd"
		namespace="http://petra.linkon.se/gtssales/validpricegroups/data/v1_27" />

	<xs:import schemaLocation="gtssales-detailedtransportservicepricequote.xsd"
		namespace="http://petra.linkon.se/gtssales/detailedpricequote/data/v1_27" />

	<xs:import schemaLocation="gtssales-multiridealternativerelations.xsd"
		namespace="http://petra.linkon.se/gtssales/multiridealternativerelations/data/v1_27" />

	<xs:import schemaLocation="gtssales-seatavailability.xsd"
		namespace="http://petra.linkon.se/gtssales/seatavailability/data/v1_27" />

	<xs:import schemaLocation="gtssales-seatavailabilitydetail.xsd"
		namespace="http://petra.linkon.se/gtssales/seatavailability/detail/data/v1_27" />

	<xs:import schemaLocation="gtssales-validcodes.xsd"
		namespace="http://petra.linkon.se/gtssales/validcodes/data/v1_27" />

	<xs:import schemaLocation="gtssales-pricegroups.xsd"
		namespace="http://petra.linkon.se/gtssales/pricegroups/data/v1_27"></xs:import>

	<xs:import schemaLocation="gtssales-shared.xsd"
		namespace="http://petra.linkon.se/gtssales/shared/data/v1_27"></xs:import>

	<xs:import schemaLocation="gtssales-journeyadvicepricequotes-shared.xsd"
		namespace="http://petra.linkon.se/gtssales/journeyadvicepricequotes/shared/data/v1_27"></xs:import>

	<xs:import schemaLocation="gtssales-journeyadvicepricequotes-overview.xsd"
		namespace="http://petra.linkon.se/gtssales/journeyadvicepricequotes/overview/data/v1_27"></xs:import>

	<xs:import schemaLocation="gtssales-journeyadvicepricequotes-detailed.xsd"
		namespace="http://petra.linkon.se/gtssales/journeyadvicepricequotes/detailed/data/v1_27"></xs:import>

	<xs:import schemaLocation="gtssales-optionavailability.xsd"
		namespace="http://petra.linkon.se/gtssales/optionavailability/data/v1_27"></xs:import>
		
	<xs:import schemaLocation="gtssales-placementoptions.xsd"
        namespace="http://petra.linkon.se/gtssales/placementoptions/data/v1_27" />

	<xs:import schemaLocation="gtssales-passengercategories.xsd"
        namespace="http://petra.linkon.se/gtssales/passengercategories/data/v1_27" />

    <xs:import schemaLocation="gtssales-validatepromotioncode.xsd"
        namespace="http://petra.linkon.se/gtssales/validatepromotioncode/data/v1_27" />        

	<xs:import schemaLocation="gtssales-changedeparture.xsd"
        namespace="http://petra.linkon.se/gtssales/changedeparture/data/v1_27" />        

	<xs:import schemaLocation="gtssales-validatetravelpass.xsd"
        namespace="http://petra.linkon.se/gtssales/validatetravelpass/data/v1_27" />        



	<xs:annotation>
		<xs:documentation>
			Schema for GTS Sales service.
    </xs:documentation>
	</xs:annotation>

	<!-- Exception -->
	<xs:element name="GtsSalesExceptionData">
		<xs:annotation>
			<xs:documentation>Exception data from the GtsSales service.
			</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:sequence>
				<xs:element ref="lcce:ExceptionData" />
			</xs:sequence>
		</xs:complexType>
	</xs:element>




</xs:schema>
