<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://api.todoservice.com/types"
           xmlns:tns="http://api.todoservice.com/types"
           elementFormDefault="qualified">

    <!-- Todo item data type -->
    <xs:complexType name="TodoType">
        <xs:annotation>
            <xs:documentation>Represents a single todo item with all its properties</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Unique identifier for the todo item (auto-generated)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="title" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Brief title or summary of the todo item</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Detailed description of the todo item</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="status" type="tns:TodoStatusType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Current status of the todo item</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="priority" type="tns:PriorityType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Priority level of the todo item</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dueDate" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Due date and time for the todo item</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="createdAt" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Timestamp when the todo item was created</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="updatedAt" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Timestamp when the todo item was last updated</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="assignee" type="tns:UserType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>User assigned to this todo item</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="tags" type="tns:TagListType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>List of tags associated with this todo item</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- Todo status enumeration -->
    <xs:simpleType name="TodoStatusType">
        <xs:annotation>
            <xs:documentation>Possible status values for a todo item</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="PENDING">
                <xs:annotation>
                    <xs:documentation>Todo item is created but not yet started</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="IN_PROGRESS">
                <xs:annotation>
                    <xs:documentation>Todo item is currently being worked on</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="COMPLETED">
                <xs:annotation>
                    <xs:documentation>Todo item has been completed</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CANCELLED">
                <xs:annotation>
                    <xs:documentation>Todo item has been cancelled</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ON_HOLD">
                <xs:annotation>
                    <xs:documentation>Todo item is temporarily on hold</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <!-- Priority enumeration -->
    <xs:simpleType name="PriorityType">
        <xs:annotation>
            <xs:documentation>Priority levels for todo items</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="LOW"/>
            <xs:enumeration value="MEDIUM"/>
            <xs:enumeration value="HIGH"/>
            <xs:enumeration value="URGENT"/>
        </xs:restriction>
    </xs:simpleType>

    <!-- User data type -->
    <xs:complexType name="UserType">
        <xs:annotation>
            <xs:documentation>Represents a user who can be assigned to todo items</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:long" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Unique user identifier</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="username" type="tns:UsernameType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Unique username</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="email" type="tns:EmailType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>User's email address</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="fullName" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>User's full display name</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- Username validation -->
    <xs:simpleType name="UsernameType">
        <xs:annotation>
            <xs:documentation>Valid username format (3-30 characters, alphanumeric and underscore)</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z0-9_]{3,30}"/>
            <xs:minLength value="3"/>
            <xs:maxLength value="30"/>
        </xs:restriction>
    </xs:simpleType>

    <!-- Email validation -->
    <xs:simpleType name="EmailType">
        <xs:annotation>
            <xs:documentation>Valid email address format</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"/>
            <xs:maxLength value="254"/>
        </xs:restriction>
    </xs:simpleType>

    <!-- Tag list type -->
    <xs:complexType name="TagListType">
        <xs:annotation>
            <xs:documentation>List of tags for categorizing todo items</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="tag" type="tns:TagType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Individual tag</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- Tag validation -->
    <xs:simpleType name="TagType">
        <xs:annotation>
            <xs:documentation>Valid tag format (1-50 characters, no special characters)</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z0-9\-_\s]{1,50}"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="50"/>
        </xs:restriction>
    </xs:simpleType>

    <!-- Todo list type -->
    <xs:complexType name="TodoListType">
        <xs:annotation>
            <xs:documentation>Collection of todo items</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="todo" type="tns:TodoType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Individual todo item in the list</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="totalCount" type="xs:int" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Total number of todo items (for pagination)</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- Search criteria type -->
    <xs:complexType name="SearchCriteriaType">
        <xs:annotation>
            <xs:documentation>Criteria for searching and filtering todo items</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="status" type="tns:TodoStatusType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Filter by status (multiple values allowed)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="priority" type="tns:PriorityType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Filter by priority (multiple values allowed)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="assigneeId" type="xs:long" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Filter by assigned user ID</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dueBefore" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Filter items due before this date</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dueAfter" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Filter items due after this date</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="textSearch" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Search text in title and description</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <!-- Root element declarations -->
    <xs:element name="Todo" type="tns:TodoType">
        <xs:annotation>
            <xs:documentation>Root element for a single todo item</xs:documentation>
        </xs:annotation>
    </xs:element>

    <xs:element name="TodoList" type="tns:TodoListType">
        <xs:annotation>
            <xs:documentation>Root element for a collection of todo items</xs:documentation>
        </xs:annotation>
    </xs:element>

    <xs:element name="User" type="tns:UserType">
        <xs:annotation>
            <xs:documentation>Root element for user information</xs:documentation>
        </xs:annotation>
    </xs:element>

    <xs:element name="SearchCriteria" type="tns:SearchCriteriaType">
        <xs:annotation>
            <xs:documentation>Root element for search criteria</xs:documentation>
        </xs:annotation>
    </xs:element>

</xs:schema>
