<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xs:schema [
<!ENTITY L_P_ACKTYP "2">
<!ENTITY L_P_ADRTYP "1">
<!ENTITY L_P_AGRSSTEHNR "2">
<!ENTITY L_P_AGRSSTNR "4">
<!ENTITY L_P_APPLNMN "30">
<!ENTITY L_P_ARDTYP "3">
<!ENTITY L_P_ARDNMN "20">
<!ENTITY L_P_ATGARD "2">
<!ENTITY L_P_ATKTYP "1">
<!ENTITY L_P_ALTANV "30">
<!ENTITY L_P_ANTLEDKOD "1">
<!ENTITY L_P_ANTRBTFOSTD "3">
<!ENTITY L_P_ANSVSJ "6">
<!ENTITY L_P_ANVDID "6">
<!ENTITY L_P_ANVDNMN "30">
<!ENTITY L_P_ATKRD "50">
<!ENTITY L_P_ATKPKOD "2">
<!ENTITY L_P_ATKTXT "30">
<!ENTITY L_P_ATTRKOD "4">
<!ENTITY L_P_AVBANVDID "6">
<!ENTITY L_P_AVBORSAK "2">
<!ENTITY L_P_AVTNREXT "19">
<!ENTITY L_P_AVTTYP "1">
<!ENTITY L_P_BARNALDRAR "12">
<!ENTITY L_P_BESKADR "30">
<!ENTITY L_P_BETANVDID "6">
<!ENTITY L_P_BETKKORTSYS "2">
<!ENTITY L_P_BETKOD "2">
<!ENTITY L_P_BETKOD2 "2">
<!ENTITY L_P_BETSTAKOD "1">
<!ENTITY L_P_BETSTNR "4">
<!ENTITY L_P_BETVILLK "2">
<!ENTITY L_P_BJTSTRECKKOD "25">
<!ENTITY L_P_BJTTXTRD "50">
<!ENTITY L_P_BLANDBAR "1">
<!ENTITY L_P_BYTESBLANDBAR "1">
<!ENTITY L_P_BOKTK "2">
<!ENTITY L_P_BORTTAGKOD "2">
<!ENTITY L_P_BSHANDELSE "3">
<!ENTITY L_P_PRELBETKOD "2">
<!ENTITY L_P_BETBELNTO "8">
<!ENTITY L_P_BETKKORTREF "20">
<!ENTITY L_P_BETMDLKOD "3">
<!ENTITY L_P_BETMOD "2">
<!ENTITY L_P_BOKNKLASS "6">
<!ENTITY L_P_BSFLAGGOR "16">
<!ENTITY L_P_BSNR "8">
<!ENTITY L_P_BSRDNR "3">
<!ENTITY L_P_BSTTXT "16">
<!ENTITY L_P_BJTKOD "13">
<!ENTITY L_P_BJTNR "12">
<!ENTITY L_P_BJATYP "5">
<!ENTITY L_P_CLASSID "3">
<!ENTITY L_P_DEFGRP "6">
<!ENTITY L_P_DETANTLEDKOD "1">
<!ENTITY L_P_DBKRV "1">
<!ENTITY L_P_DBDOKKOD "1">
<!ENTITY L_P_DBURL "255">
<!ENTITY L_P_DLGTXT "30">
<!ENTITY L_P_DISTKOD "2">
<!ENTITY L_P_DISTTYP "3">
<!ENTITY L_P_DNR "20">
<!ENTITY L_P_DOKNMNL "40">
<!ENTITY L_P_DOKRGL "1">
<!ENTITY L_P_DOKUTSKOD "2">
<!ENTITY L_P_DOR "3">
<!ENTITY L_P_EMDELSYSTEM "8">
<!ENTITY L_P_EPSTADR "80">
<!ENTITY L_P_EPSTTITLE "254">
<!ENTITY L_P_ERBJKOD "25">
<!ENTITY L_P_ETABLERING1 "30">
<!ENTITY L_P_ETABLERING2 "30">
<!ENTITY L_P_EUTSKOD "2">
<!ENTITY L_P_EVFKOD "4">
<!ENTITY L_P_EVFKOD "4">
<!ENTITY L_P_EXTBETUSER "10">
<!ENTITY L_P_EXTAVRID "50">
<!ENTITY L_P_EXTBJTTXT "30">
<!ENTITY L_P_EXTFINNS "1">
<!ENTITY L_P_EXTKNDREF "6">
<!ENTITY L_P_EXTRAUTRUSTN "150">
<!ENTITY L_P_EXTREFID "36">
<!ENTITY L_P_FANMN "48">
<!ENTITY L_P_FELTXTLNG "255">
<!ENTITY L_P_FKTREF "50">
<!ENTITY L_P_FLEX "6">
<!ENTITY L_P_FMTK "2">
<!ENTITY L_P_FMNMN "22">
<!ENTITY L_P_FMNR "5">
<!ENTITY L_P_FODDATKRV "1">
<!ENTITY L_P_FORKOPMAX "4">
<!ENTITY L_P_FORKOPMIN "4">
<!ENTITY L_P_FTGAVDNMN "20">
<!ENTITY L_P_FTGFORM "2">
<!ENTITY L_P_GLTTOM "4">
<!ENTITY L_P_GLTTXT1 "35">
<!ENTITY L_P_GRPNMN "20">
<!ENTITY L_P_GTULNGADR "70">
<!ENTITY L_P_GTUNMN1 "50">
<!ENTITY L_P_GTUNMN2 "50">
<!ENTITY L_P_GTUNR1 "10">
<!ENTITY L_P_GTUNR2 "10">
<!ENTITY L_P_GTUNR2 "10">
<!ENTITY L_P_HANDELSETYP "8">
<!ENTITY L_P_HEMLND "35">
<!ENTITY L_P_HEMLNDSKRV "1">
<!ENTITY L_P_HEMTELNR "16">
<!ENTITY L_P_HOTELLID "12">
<!ENTITY L_P_HOTELLKEDJA "55">
<!ENTITY L_P_HLPTXT "400">
<!ENTITY L_P_HLPTXTLNG "2000">
<!ENTITY L_P_IATAKOD "3">
<!ENTITY L_P_INTERVMARK "1">
<!ENTITY L_P_ITTLGTK "1">
<!ENTITY L_P_KAMPANJKOD "20">
<!ENTITY L_P_KAOBETKOD "2">
<!ENTITY L_P_KCOADR "35">
<!ENTITY L_P_KDTANVDID "6">
<!ENTITY L_P_KDTKKORTSYS "2">
<!ENTITY L_P_KDTBETKOD "2">
<!ENTITY L_P_KDTBETMDLKOD "3">
<!ENTITY L_P_KGTUADR "35">
<!ENTITY L_P_KKKTRNR "8">
<!ENTITY L_P_KKORTDATA "37">
<!ENTITY L_P_KKORTNR "19">
<!ENTITY L_P_KKORTSYS "2">
<!ENTITY L_P_KDTKKORTREF "20">
<!ENTITY L_P_KKORTSOEKNR "12">
<!ENTITY L_P_KNDPRFKAT "10">
<!ENTITY L_P_KOMMKOD "3">
<!ENTITY L_P_KOMPGLTT "2">
<!ENTITY L_P_KORGNR "11">
<!ENTITY L_P_KORTKOD "2">
<!ENTITY L_P_KORTNR "24">
<!ENTITY L_P_KORTTYP "2">
<!ENTITY L_P_KDTORSAK "2">
<!ENTITY L_P_KDTRGL "3">
<!ENTITY L_P_KDTTK "3">
<!ENTITY L_P_KMFT "2">
<!ENTITY L_P_KLNDKOD "4">
<!ENTITY L_P_KNDABNR "8">
<!ENTITY L_P_KNDENMN "35">
<!ENTITY L_P_KNDFNMN "15">
<!ENTITY L_P_KNDGTAADR "30">
<!ENTITY L_P_KNDLNDNR "4">
<!ENTITY L_P_KNDNR "10">
<!ENTITY L_P_KNDPRIO "3">
<!ENTITY L_P_KNDPSTADR "20">
<!ENTITY L_P_KNDPSTNR "6">
<!ENTITY L_P_KNDRNR "4">
<!ENTITY L_P_KOMMSATT "1">
<!ENTITY L_P_KOMPLSTA "2">
<!ENTITY L_P_KOMPLSYS "6">
<!ENTITY L_P_KOMPNMN "20">
<!ENTITY L_P_KOMPREDATG "2">
<!ENTITY L_P_KOMPTYP "6">
<!ENTITY L_P_KORTKRV "4">
<!ENTITY L_P_KPSTLND "35">
<!ENTITY L_P_KPSTNR "9">
<!ENTITY L_P_KPSTORT "27">
<!ENTITY L_P_KPSTREG "35">
<!ENTITY L_P_KRYPTKORTNR "48">
<!ENTITY L_P_KST "7">
<!ENTITY L_P_KSTKTO "50">
<!ENTITY L_P_KSTKTOOBL "1">
<!ENTITY L_P_KTLGADR "35">
<!ENTITY L_P_KTOID "10">
<!ENTITY L_P_KTONIV "3">
<!ENTITY L_P_KTOSTAT "2">
<!ENTITY L_P_LITTERA "8">
<!ENTITY L_P_LOKALKAT "2">
<!ENTITY L_P_LOKALLNGNMN "65">
<!ENTITY L_P_LOGGA "1">
<!ENTITY L_P_LSID "11">
<!ENTITY L_P_LSTOTID "26">
<!ENTITY L_P_MALLTYP "1">
<!ENTITY L_P_MEDKAT "2">
<!ENTITY L_P_MOBILTELNR "16">
<!ENTITY L_P_MSGID "24">
<!ENTITY L_P_NMN "48">
<!ENTITY L_P_NKLID "5">
<!ENTITY L_P_OBL "1">
<!ENTITY L_P_ONSKETXT "60">
<!ENTITY L_P_ORGNR "10">
<!ENTITY L_P_ORTLNGNMN "50">
<!ENTITY L_P_PASSNO "20">
<!ENTITY L_P_PASSWRD "32">
<!ENTITY L_P_PINKOD "4">
<!ENTITY L_P_PLSEGS "2">
<!ENTITY L_P_PLSNR "5">
<!ENTITY L_P_PLSNRTYP "1">
<!ENTITY L_P_PLSPLAC "2">
<!ENTITY L_P_PLSRIKTN "1">
<!ENTITY L_P_PRDKOD "3">
<!ENTITY L_P_PRSNV "2">
<!ENTITY L_P_PSNNR "12">
<!ENTITY L_P_PSTNR1 "9">
<!ENTITY L_P_PSTNR2 "9">
<!ENTITY L_P_PSTORT1 "27">
<!ENTITY L_P_PSTORT2 "27">
<!ENTITY L_P_RBRKNDNR "14">
<!ENTITY L_P_RBTFODST "3">
<!ENTITY L_P_RBTFOSTD "3">
<!ENTITY L_P_RBTFOSTD2 "3">
<!ENTITY L_P_RBTFOKOD "3">
<!ENTITY L_P_RBTFOKND "3">
<!ENTITY L_P_RBTFOKSA "3">
<!ENTITY L_P_RBTFOTYP "2">
<!ENTITY L_P_REDNMN "97">
<!ENTITY L_P_REDKDTBETKOD "2">
<!ENTITY L_P_REFNR2 "20">
<!ENTITY L_P_RDVPRCNMN "30">
<!ENTITY L_P_REKVKRV "1">
<!ENTITY L_P_RENIDKRV "2">
<!ENTITY L_P_RENTK "2">
<!ENTITY L_P_RENTXT "35">
<!ENTITY L_P_RENXRKOD "1">
<!ENTITY L_P_REQUESTKEY "20">
<!ENTITY L_P_RESTRT "3">
<!ENTITY L_P_RPOMST "3">
<!ENTITY L_P_RPOSATCODE "3">
<!ENTITY L_P_RSTK "1">
<!ENTITY L_P_RSTXT "20">
<!ENTITY L_P_RUMSTYPTXT "40">
<!ENTITY L_P_RVGKOD "2">
<!ENTITY L_P_SALJSTOP "1">
<!ENTITY L_P_SEKRETESS "1">
<!ENTITY L_P_SKLTVGNNR "15">
<!ENTITY L_P_SLJNMN "8">
<!ENTITY L_P_SMSAVSADR "80">
<!ENTITY L_P_SMSSTYLESHEET "255">
<!ENTITY L_P_STNNMN "30">
<!ENTITY L_P_STNNMNFKN "15">
<!ENTITY L_P_STNNMNREL "17">
<!ENTITY L_P_STNSGN "4">
<!ENTITY L_P_SSTEHNMN "30">
<!ENTITY L_P_SSTKAT "2">
<!ENTITY L_P_SSTNR "4">
<!ENTITY L_P_SSTNMN "30">
<!ENTITY L_P_SKKFINNS "1">
<!ENTITY L_P_XMLSPECIFIKATION "64">
<!ENTITY L_P_SPKODKRED "3">
<!ENTITY L_P_SSTEHNR "2">
<!ENTITY L_P_STATUSINFO "20">
<!ENTITY L_P_SUBCLASSID "3">
<!ENTITY L_P_TAPTXT "30">
<!ENTITY L_P_TAVTXT "40">
<!ENTITY L_P_TELNR "30">
<!ENTITY L_P_TILLKRDFKT "1">
<!ENTITY L_P_TILLVALSTYP "6">
<!ENTITY L_P_TITEL "20">
<!ENTITY L_P_TIDSENHET "2">
<!ENTITY L_P_TMLID "8">
<!ENTITY L_P_TMPPASSWRD "32">
<!ENTITY L_P_TRMKODTXT "30">
<!ENTITY L_P_TRPKOD "4">
<!ENTITY L_P_TRPKODER "36">
<!ENTITY L_P_UPDSIGN "6">
<!ENTITY L_P_UPPDRAG "7">
<!ENTITY L_P_URSPFUNK "6">
<!ENTITY L_P_UTRKON "2">
<!ENTITY L_P_UTRNR "5">
<!ENTITY L_P_UTREGS "2">
<!ENTITY L_P_UTRROK "2">
<!ENTITY L_P_UTRSTL "2">
<!ENTITY L_P_UTSANVDID "6">
<!ENTITY L_P_UTSKOD "2">
<!ENTITY L_P_UVISERADANVDID "6">
<!ENTITY L_P_UVISERADSTNR "4">
<!ENTITY L_P_VARLNMN "50">
<!ENTITY L_P_VALSLAKOD "3">
<!ENTITY L_P_VARUMRKFKN "3">
<!ENTITY L_P_VARUMRKKOD "4">
<!ENTITY L_P_VARUMRKTXT "33">
<!ENTITY L_P_VIAREDKOD1 "1">
<!ENTITY L_P_VIAREDKOD2 "1">
<!ENTITY L_P_VISERADANVDID "6">
<!ENTITY L_P_VISERADSTNR "4">
<!ENTITY L_P_VRANAT "3">
<!ENTITY L_P_VRANMN "20">
<!ENTITY L_P_VRALNGNMN "72">
<!ENTITY L_P_VRANMNFKN "10">
<!ENTITY L_P_COOKIEID "36">]>

<xs:schema
  xmlns:tns="http://petra.linkon.se/commonelements/data/v4_7"
  xmlns:xs="http://www.w3.org/2001/XMLSchema"
  xmlns:jaxb="http://java.sun.com/xml/ns/jaxb"
  xmlns:xjc="http://java.sun.com/xml/ns/jaxb/xjc"
  targetNamespace="http://petra.linkon.se/commonelements/data/v4_7"
  elementFormDefault="qualified"
  version="4.7.202270.0"
  jaxb:extensionBindingPrefixes="xjc"
  jaxb:version="2.0">

  <xs:annotation>
    <xs:appinfo>
      <jaxb:globalBindings generateIsSetMethod="true">
        <xjc:serializable uid="1234" />
      </jaxb:globalBindings>
    </xs:appinfo>
  </xs:annotation>

  <!-- =============================== Attribute groups ============================ -->

	<xs:attributeGroup name="PlaintextAttribute">
		<xs:annotation>
			<xs:documentation>
				Code/clear-text attribute type.
				A value property provides the code (of any specific type).
				The plaintext attribute is the clear-text (of string type).
			</xs:documentation>
		</xs:annotation>
  		<xs:attribute name="plaintext" type="xs:string" />
	</xs:attributeGroup>

  <!-- =============================== A ============================ -->

  <xs:element name="AccessLevel" type="tns:AccessLevelType"/>
  <xs:simpleType name="AccessLevelType">
        <xs:annotation>
            <xs:documentation>
                Access level.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:nonNegativeInteger">
            <xs:maxInclusive value="**********" />
        </xs:restriction>
    </xs:simpleType>
  
     <xs:element name="AccountingNumber" type="tns:AccountingNumberType">
        <xs:annotation>
            <xs:documentation>
            	Accountingnumber 
            </xs:documentation>
        </xs:annotation>
     </xs:element>
     <xs:simpleType name="AccountingNumberType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="9" />
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="AccountProducer" type="tns:AccountProducerType" />
	<xs:simpleType name="AccountProducerType">
		<xs:annotation>
			<xs:documentation>
				Identification of a accountproducer.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>
    
    <xs:element name="AccountProducerName" type="tns:AccountProducerNameType" />
    <xs:simpleType name="AccountProducerNameType">
        <xs:annotation>
            <xs:documentation>
                Identification of a accountproducer name.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
			<xs:maxLength value="30" />
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="AccountStatus" type="tns:AccountStatusType" />
	<xs:simpleType name="AccountStatusType">
		<xs:annotation>
			<xs:documentation>
				Account status.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="KL">
				<xs:annotation>
					<xs:documentation>
						Ready
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
        	<xs:enumeration value="SP">
        		<xs:annotation>
        			<xs:documentation>
        				Locked
        			</xs:documentation>
        		</xs:annotation>
        	</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="AdditionalEquipment" type="tns:AdditionalEquipmentType" />
	<xs:simpleType name="AdditionalEquipmentType">
		<xs:annotation>
			<xs:documentation>
				Additional equipment, etc. Free text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="150" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="AdditionalPaymentCode" type="tns:AdditionalPaymentCode2Type" />
	<xs:complexType name="AdditionalPaymentCode2Type">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
				Additional payment code.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="AddressType" type="tns:AddressTypeCodeType" />
	<xs:simpleType name="AddressTypeType">
		<xs:annotation>
			<xs:documentation>
				Type of address.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"/>
			<xs:enumeration value="O">
				<xs:annotation>
					<xs:documentation>
						Official address.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="L">
				<xs:annotation>
					<xs:documentation>
						Delivery address.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="C">
				<xs:annotation>
					<xs:documentation>
						Communication address .
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="T">
				<xs:annotation>
					<xs:documentation>
						Temporary address.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="AddressTypeCodeType">
		<xs:annotation>
			<xs:documentation>
				Type of address.

				Code/clear-text type. The value is the code (of enum type)
				and the plaintext attribute is the clear-text (of string type).
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="tns:AddressTypeType">
				<xs:attributeGroup ref="tns:PlaintextAttribute" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="AllowedCredit" type="tns:AllowedCreditType" />
	<xs:simpleType name="AllowedCreditType">
		<xs:annotation>
			<xs:documentation>
				Allowed Credit/Invoice.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"></xs:maxLength>
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>Invoice not allowed, but other sale options are
						allowed.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="E">
				<xs:annotation>
					<xs:documentation>External invoice (allowed periodically)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="I">
				<xs:annotation>
					<xs:documentation>Internal invoice</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
    
    <xs:element name="AlternativeCreditCode" type="tns:AlternativeCreditCodeType" />
    <xs:complexType name="AlternativeCreditCodeType">
        <xs:simpleContent>
            <xs:annotation>
                <xs:documentation>
                    Payment code when crediting with "other reason". Credit other reason can be used when the order is known.
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="tns:StringCodeType">
                <xs:maxLength value="2" />
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    
    <xs:element name="ApprovalCode" type="tns:ApprovalCodeType" />
    <xs:simpleType name="ApprovalCodeType">
        <xs:annotation>
            <xs:documentation>
            	ApprovalCode
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="8" />
        </xs:restriction>
    </xs:simpleType>

  	<xs:element name="ArrivalDate" type="tns:ArrivalDateType" />
  	<xs:simpleType name="ArrivalDateType">
    	<xs:annotation>
      		<xs:documentation>
        		Specifies the arrival date, for a component of a service,
        		i.e. the last date the customer is last
        		entitled to exploit the
        		resource (component).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date" />
	</xs:simpleType>

	<xs:element name="ArrivalDateTime" type="tns:ArrivalDateTimeType"/>
	<xs:simpleType name="ArrivalDateTimeType">
		<xs:annotation>
			<xs:documentation>
				Specifies the arrival date/time, for a component of	a service,
				i.e. the last date/time the customer is last entitled to exploit the
				resource (component).

				Corresponding: ArrivalDate + ArrivalTime
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime" />
	</xs:simpleType>

	<xs:element name="ArrivalTime" type="tns:ArrivalTimeType"/>
	<xs:simpleType name="ArrivalTimeType">
		<xs:annotation>
			<xs:documentation>
				Specifies the arrival time, for a component of	a service,
				i.e. the last time the customer is last entitled to exploit the
				resource (component).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:time" />
	</xs:simpleType>

	<!-- =============================== B ============================ -->

	<xs:element name="BatchId" type="tns:BatchIdType" />
	<xs:simpleType name="BatchIdType">
		<xs:annotation>
			<xs:documentation>
				Unique identitiy, between 1-999999, for a batch run.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
 			<xs:maxInclusive value="999999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="BirthDate" type="tns:BirthDateType"/>
	<xs:simpleType name="BirthDateType">
		<xs:annotation>
			<xs:documentation>
				Date of birth, (YYYY-MM-DD).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date" />
	</xs:simpleType>

	<xs:element name="BirthDateMandatory" type="tns:BirthDateMandatoryType" />
	<xs:simpleType name="BirthDateMandatoryType">
		<xs:annotation>
			<xs:documentation>
				Birth date is mandatory.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						Undefined.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>
						No.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="J">
				<xs:annotation>
					<xs:documentation>
						Yes.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="BookingClass" type="tns:BookingClassType" />
	<xs:complexType name="BookingClassType">
		<xs:annotation>
			<xs:documentation>
				Level of comfortability.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="BookingDeviation" type="tns:BookingDeviationType" />
	<xs:simpleType name="BookingDeviationType">
		<xs:annotation>
			<xs:appinfo>
			<jaxb:typesafeEnumClass name="BookingDeviationType">
					<jaxb:typesafeEnumMember name="AVVIKELSE_PLEGS" value="P" />
					<jaxb:typesafeEnumMember name="AVVIKELSE_ROK" value="R" />
					<jaxb:typesafeEnumMember name="AVVIKELSE_TAG" value="T" />
				</jaxb:typesafeEnumClass>
			</xs:appinfo>
			<xs:documentation>
				Booking deviation property.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="P">
				<xs:annotation>
					<xs:documentation>
						Placement property deviation in booking.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="R">
				<xs:annotation>
					<xs:documentation>
						Smoking compartment deviation in booking.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="T">
				<xs:annotation>
					<xs:documentation>
						Train deviation in booking.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="BookingDialogNumber" type="tns:BookingDialogNumberType" />
	<xs:simpleType name="BookingDialogNumberType">
		<xs:annotation>
			<xs:documentation>
				Dialogue number when booking abroad.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="99999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="BookingRule" type="tns:BookingRuleType" />
	<xs:simpleType name="BookingRuleType">
		<xs:annotation>
			<xs:documentation>
				Rule for seat booking such as no seat reservation, seat
				reservation mandatory etc.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="EJBO">
				<xs:annotation>
					<xs:documentation>No seat reservation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="OBLI">
				<xs:annotation>
					<xs:documentation>Seat reservation mandatory</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ODEF">
				<xs:annotation>
					<xs:documentation>Seat reservation undefined</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="REKO">
				<xs:annotation>
					<xs:documentation>Seat reservation recommended</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="BookingService" type="tns:BookingServiceType" />
	<xs:simpleType name="BookingServiceType">
		<xs:annotation>
			<xs:documentation>
				The booking system responsible for the original order.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="BusinessArea" type="tns:BusinessAreaType" />
	<xs:simpleType name="BusinessAreaType">
		<xs:annotation>
			<xs:documentation>
				Naming of organizational levels within a company. The two higher
				levels are responsibility or business.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Buyer" type="tns:BuyerType" />
	<xs:simpleType name="BuyerType">
		<xs:annotation>
			<xs:documentation>
				Free text eg: travel buyers, telephone number, type of delivery.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== C ============================ -->

	<xs:element name="CancelDialogNumber" type="tns:CancelDialogNumberType" />
	<xs:simpleType name="CancelDialogNumberType">
		<xs:annotation>
			<xs:documentation>
				Dialogue number when cancelling an abroad booking.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="99999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CancellationDate" type="tns:CancellationDateType" />
	<xs:simpleType name="CancellationDateType">
		<xs:annotation>
			<xs:documentation>
				Indicates the date a service was cancelled.
				Used for international cancellations.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date">
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CancellationDateTime" type="tns:CancellationDateTimeType" />
	<xs:simpleType name="CancellationDateTimeType">
		<xs:annotation>
			<xs:documentation>
				Indicates the date/time a service was cancelled.
				Used for international cancellations.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime">
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CancellationProducerCode" type="tns:CancellationProducerCodeType" />
	<xs:simpleType name="CancellationProducerCodeType">
		<xs:annotation>
			<xs:documentation>
				Identification code of the producer cancelling a service.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CancellationTerminalId" type="tns:TerminalIdType">
		<xs:annotation>
			<xs:documentation>
				Id of the terminal from where the order was cancelled.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="CancellationUserId" type="tns:CancellationUserIdType" />
	<xs:simpleType name="CancellationUserIdType">
		<xs:annotation>
			<xs:documentation>
				Identifies the user that issued the cancellation.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>
    
    <xs:element name="CancelReasonCode" type="tns:CancelReasonCodeType" />
    <xs:simpleType name="CancelReasonCodeType">
        <xs:annotation>
            <xs:documentation>
                Code associated with a cancel action.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="2" />
        </xs:restriction>
    </xs:simpleType>
    
	<xs:element name="CardDataTransferenceDate" type="tns:CardDataTransferenceDateType" />
	<xs:simpleType name="CardDataTransferenceDateType">
		<xs:annotation>
			<xs:documentation>
				Date of card date transfer.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date">
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CardMandatory" type="tns:CardMandatoryType" />
	<xs:simpleType name="CardMandatoryType">
		<xs:annotation>
			<xs:documentation>
				Credit card is required when paying.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CardNumber" type="tns:CardNumberType" />
	<xs:simpleType name="CardNumberType">
		<xs:annotation>
			<xs:documentation>
				Card number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="19" />
			<xs:pattern value="[A-Za-z0-9\*]*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CardPin" type="tns:PinCodeType">
		<xs:annotation>
			<xs:documentation>
				Card pin code.

				Corresponding Element: See PinCodeType
			</xs:documentation>
		</xs:annotation>
	</xs:element>
	
    <xs:element name="CardPresentSequenceNumber" type="tns:CardPresentSequenceNumberType" />
        <xs:simpleType name="CardPresentSequenceNumberType">
            <xs:annotation>
                <xs:documentation>
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="xs:string" />
        </xs:simpleType>
    
    <xs:element name="CardSystem" type="tns:CardSystemType" />
    <xs:complexType name="CardSystemType">
        <xs:annotation>
            <xs:documentation>
                A card system is used to allow different payment methods within a particular salespoint category.
                
                Possible values:
                Blank: The salesunit has normal payment methods and rules for the particular salespoint category.
                **   : The salespoint selection code is not important. 
                XX   : The salespoint selection code.
                
                Example of XX:
                ES,AR,AW,EL,ES,RF,ES,EX,EB,EF,ES,ES,ES,EX
            </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:restriction base="tns:StringCodeType">
                <xs:maxLength value="2" />
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>
    
	<xs:element name="CardType" type="tns:CardTypeType" />
	<xs:complexType name="CardTypeType">
		<xs:annotation>
			<xs:documentation>
				Credit card type.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="CareOf" type="tns:CareOfType"/>
	<xs:simpleType name="CareOfType">
		<xs:annotation>
			<xs:documentation>
				Customer c/o address.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CarriageGroup" type="tns:CarriageGroupType"/>
  	<xs:simpleType name="CarriageGroupType">
  		<xs:annotation>
  			<xs:documentation>
  				Carriage group.
  			</xs:documentation>
  		</xs:annotation>
  		<xs:restriction base="xs:integer">
  			<xs:minInclusive value="0"/>
  			<xs:maxInclusive value="*********"/>
  		</xs:restriction>
  	</xs:simpleType>

  	<xs:element name="CarriageGroupStartDate" type="tns:CarriageGroupStartDateType"/>
  	<xs:simpleType name="CarriageGroupStartDateType">
  		<xs:annotation>
  			<xs:documentation>
  				Carriage group departure date.
  			</xs:documentation>
  		</xs:annotation>
  		<xs:restriction base="xs:date"/>
  	</xs:simpleType>

	<xs:element name="CarriageId" type="tns:CarriageIdType"/>
  	<xs:simpleType name="CarriageIdType">
  		<xs:annotation>
  			<xs:documentation>
  				Carriage id, i.e. the number of the carriage.
  			</xs:documentation>
  		</xs:annotation>
  		<xs:restriction base="xs:string">
  			<xs:maxLength value="15"/>
  		</xs:restriction>
  	</xs:simpleType>

  	<xs:element name="CarriageType" type="tns:CarriageTypeType"/>
  	<xs:simpleType name="CarriageTypeType">
  		<xs:annotation>
  			<xs:documentation>
  				Carriage type.
  			</xs:documentation>
  		</xs:annotation>
  		<xs:restriction base="xs:string">
  			<xs:maxLength value="8"/>
  		</xs:restriction>
  	</xs:simpleType>

  	<xs:element name="CarriageTypeNumber" type="tns:CarriageTypeNumberType"/>
  	<xs:simpleType name="CarriageTypeNumberType">
  		<xs:annotation>
  			<xs:documentation>
  				Carriage type number.
  			</xs:documentation>
  		</xs:annotation>
  		<xs:restriction base="xs:integer">
  			<xs:minInclusive value="0"/>
  			<xs:maxInclusive value="9999"/>
  		</xs:restriction>
  	</xs:simpleType>

	<xs:element name="CashDiscountCode" type="tns:CashDiscountCodeType" />
	<xs:simpleType name="CashDiscountCodeType">
		<xs:annotation>
			<xs:documentation>
				Identifies a particular cash discount.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CashJournalId" type="tns:CashJournalIdType" />
	<xs:simpleType name="CashJournalIdType">
		<xs:annotation>
			<xs:documentation>
				Uniquely identifies a bill per selling point.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="999999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CatalogueListId" type="tns:CatalogueListIdType"/>
	<xs:simpleType name="CatalogueListIdType">
	 <xs:annotation>
	  <xs:documentation>
		  Service Type
	  </xs:documentation>
	 </xs:annotation>
	 <xs:restriction base="xs:string">
	  <xs:maxLength value="3" />
	 </xs:restriction>
	</xs:simpleType>

	<xs:element name="CatalogueListName" type="tns:CatalogueListNameType"/>
	<xs:simpleType name="CatalogueListNameType">
	 <xs:annotation>
	  <xs:documentation>
		  Service Name
	  </xs:documentation>
	 </xs:annotation>
	 <xs:restriction base="xs:string">
	  <xs:maxLength value="20" />
	 </xs:restriction>
	</xs:simpleType>

	<xs:element name="CheckInStartTime" type="tns:CheckInStartTimeType" />
	<xs:simpleType name="CheckInStartTimeType">
		<xs:annotation>
			<xs:documentation>
				The time check-in of a ticket, such as a mobile ticket, can begin,
				normally about one day before departure.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="ChildrenAges" type="tns:ChildrenAgesType" />
	<xs:simpleType name="ChildrenAgesType">
		<xs:annotation>
			<xs:documentation>
				Children Ages. Max 4 ages separated by a white space.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CityLongName" type="tns:CityLongNameType"/>
	<xs:simpleType name="CityLongNameType">
		<xs:annotation>
			<xs:documentation>
				City long name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="50" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CityName" type="tns:CityNameType"/>
	<xs:simpleType name="CityNameType">
		<xs:annotation>
			<xs:documentation>
				City name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="27" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="ClassId" type="tns:ClassIdType"/>
	<xs:simpleType name="ClassIdType">
		<xs:annotation>
			<xs:documentation>
				RPO class id.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
			<xs:pattern value="[A-Z0-9][A-Z0-9]*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ClearCustomer" type="tns:ClearCustomerType"/>
	<xs:simpleType name="ClearCustomerType">
		<xs:annotation>
			<xs:appinfo>
				<jaxb:typesafeEnumClass name="ClearCustomerType">
					<jaxb:typesafeEnumMember name="BLANK" value="" />
				</jaxb:typesafeEnumClass>
			</xs:appinfo>
			<xs:documentation>
				Clear marker.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"></xs:maxLength>
			<xs:enumeration value="J"></xs:enumeration>
			<xs:enumeration value="N"></xs:enumeration>
			<xs:enumeration value=""></xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CombinationDiscountNumber" type="tns:CombinationDiscountNumberType" />
	<xs:simpleType name="CombinationDiscountNumberType">
	  <xs:annotation>
	    <xs:documentation>
	      Combination discount number.
	    </xs:documentation>
	  </xs:annotation>
	  <xs:restriction base="xs:int">
	    <xs:minInclusive value="0"/>
	    <xs:maxInclusive value="99999"/>
	  </xs:restriction>
	</xs:simpleType>

	<xs:element name="Comments" type="tns:CommentsType"/>
	<xs:simpleType name="CommentsType">
		<xs:annotation>
			<xs:documentation>
				Comments text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="60"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Commission" type="tns:CommissionType"/>
	<xs:simpleType name="CommissionType">
		<xs:annotation>
			<xs:documentation>
				(Debit) Commission.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="7"/>
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="CommunicationCode" type="tns:CommunicationCodeType"/>
	<xs:simpleType name="CommunicationCodeType">
		<xs:annotation>
			<xs:documentation>
				Communication code, indicates how a customer wants to be contacted or if he/she can't be or doesn't want to be contacted at all.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CompanyCustomerId" type="tns:CustomerIdType">
		<xs:annotation>
			<xs:documentation>
				Company/legal person customer number.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="CompanyForm" type="tns:CompanyFormType"/>
	<xs:simpleType name="CompanyFormType">
		<xs:annotation>
			<xs:documentation>
				Company form, AB/EF/..
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2"/>
		</xs:restriction>
	</xs:simpleType>

    <xs:element name="ReceiptCompanyName" type="tns:CompanyNameType" />
	<xs:element name="CompanyName" type="tns:CompanyNameType" />
	<xs:simpleType name="CompanyNameType">
		<xs:annotation>
			<xs:documentation>
				Name of legal entity or individual.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="48"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CompartmentCode" type="tns:CompartmentCodeType" />
	<xs:complexType name="CompartmentCodeType">
		<xs:annotation>
			<xs:documentation>
				Indicates whether an area must be defined as only men or women,
				or if it may be a mixture.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="CompartmentNumber" type="tns:CompartmentNumberType" />
	<xs:simpleType name="CompartmentNumberType">
		<xs:annotation>
			<xs:documentation>
				Compartment number.
				Used for example for bench rows in an event.
				Normally not used used for trains.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="5" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CompartmentSize" type="tns:CompartmentSizeType" />
	<xs:complexType name="CompartmentSizeType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					Compartment size.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="CompleteTelephoneNumber" type="tns:CompleteTelephoneNumberType"/>
	<xs:simpleType name="CompleteTelephoneNumberType">
		<xs:annotation>
			<xs:documentation>
				Complete telephone number.
			</xs:documentation>
      	</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CompletionStatus" type="tns:CompletionStatusType" />
	<xs:simpleType name="CompletionStatusType">
		<xs:annotation>
			<xs:documentation>
				Status flag used in the sales system to indicate the completion status,
				such as incompleted, pre-completed, completed, of a component.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CompletionSystem" type="tns:CompletionSystemType" />
	<xs:simpleType name="CompletionSystemType">
		<xs:annotation>
			<xs:documentation>
				Completion system.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ComponentName" type="tns:ComponentNameType" />
	<xs:simpleType name="ComponentNameType">
		<xs:annotation>
			<xs:documentation>
				Component name.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ComponentNumber" type="tns:ComponentNumberType" />
	<xs:simpleType name="ComponentNumberType">
		<xs:annotation>
			<xs:documentation>
				Identification of a component
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="32767" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ComponentResult" type="tns:ComponentResultType" />
	<xs:simpleType name="ComponentResultType">
		<xs:annotation>
			<xs:documentation>
				Code description the result of a component completion.
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ComponentSequenceNumber" type="tns:ComponentSequenceNumberType" />
	<xs:simpleType name="ComponentSequenceNumberType">
		<xs:annotation>
			<xs:documentation>
				Componenent sequence number.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="9999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ComponentType" type="tns:ComponentTypeType" />
	<xs:simpleType name="ComponentTypeType">
		<xs:annotation>
			<xs:documentation>
				Component type.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ComponentEditAction" type="tns:ComponentEditActionCodeType" />
        <xs:simpleType name="ComponentEditActionType">
          <xs:annotation>
            <xs:documentation>
              Edit action for a component.
            </xs:documentation>
          </xs:annotation>
          <xs:restriction base="xs:string">
            <xs:enumeration value="A">
              <xs:annotation>
                <xs:documentation>
                  Add
                </xs:documentation>
              </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CF">
              <xs:annotation>
                <xs:documentation>
                  Change from
                </xs:documentation>
              </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CT">
              <xs:annotation>
                <xs:documentation>
                  Change to
                </xs:documentation>
              </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="D">
              <xs:annotation>
                <xs:documentation>
                  Delete
                </xs:documentation>
              </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BF">
              <xs:annotation>
                <xs:documentation>
                  Change departure from
                </xs:documentation>
              </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BT">
              <xs:annotation>
                <xs:documentation>
                  Change departure to
                </xs:documentation>
              </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BA">
              <xs:annotation>
                <xs:documentation>
                  Change departure add
                </xs:documentation>
              </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BD">
              <xs:annotation>
                <xs:documentation>
                  Change departure delete
                </xs:documentation>
              </xs:annotation>
            </xs:enumeration>
          </xs:restriction>
        </xs:simpleType>

	<xs:complexType name="ComponentEditActionCodeType">
		<xs:annotation>
			<xs:documentation>
        	Edit action for a component.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="tns:ComponentEditActionType">
				<xs:attributeGroup ref="tns:PlaintextAttribute" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
    
    <xs:element name="CompensationRule" type="tns:CompensationRuleType" />
    <xs:simpleType name="CompensationRuleType">
        <xs:annotation>
            <xs:documentation>
                Crediting rule.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="3" />
        </xs:restriction>
    </xs:simpleType>

  	<xs:element name="ConditionalPassengerCategory" type="tns:PassengerCategoryType">
  	  <xs:annotation>
  	    <xs:documentation>
  	      Conditional passenger category for combination discount.
  	    </xs:documentation>
  	  </xs:annotation>
  	</xs:element>

	<xs:element name="ContractCategory" type="tns:ContractCategoryType" />
	<xs:simpleType name="ContractCategoryType">
		<xs:annotation>
			<xs:documentation>
				Contract category, 'S' = Standard 'T' = Temporary.
	      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="S"></xs:enumeration>
			<xs:enumeration value="T"></xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ContractNumber" type="tns:ContractNumberType" />
	<xs:simpleType name="ContractNumberType">
		<xs:annotation>
			<xs:documentation>
				Contract number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="*********" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ContractPeriod" type="tns:ContractPeriodType" />
	<xs:simpleType name="ContractPeriodType">
		<xs:annotation>
			<xs:documentation>
				Contract period.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ContractPeriodText" type="tns:ContractPeriodTextType" />
	<xs:simpleType name="ContractPeriodTextType">
		<xs:annotation>
			<xs:documentation>
				Contract period free-text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ContractualCarrierCode" type="tns:ContractualCarrierCodeType"/>
	<xs:simpleType name="ContractualCarrierCodeType">
		<xs:annotation>
			<xs:documentation>
				Carrier code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ContractualCarrierCodes" type="tns:ContractualCarrierCodesType"/>
	<xs:simpleType name="ContractualCarrierCodesType">
		<xs:annotation>
			<xs:documentation>
				Carrier codes (several).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="36"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ContractType" type="tns:ContractTypeType"/>
	<xs:simpleType name="ContractTypeType">
		<xs:annotation>
			<xs:documentation>
				Contract type.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ContractVersion" type="tns:ContractVersionType" />
	<xs:simpleType name="ContractVersionType">
		<xs:annotation>
			<xs:documentation>
				Contract version number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="9999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ContractVersionText" type="tns:ContractVersionTextType" />
	<xs:simpleType name="ContractVersionTextType">
		<xs:annotation>
			<xs:documentation>
				Contract version number text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CookieId" type="tns:CookieIdType" />
    <xs:simpleType name="CookieIdType">
        <xs:annotation>
            <xs:documentation>
                The id of a cookie for user authentication
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:length value="36" />
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="CostCenter" type="tns:CostCenterType"/>
	<xs:simpleType name="CostCenterType">
		<xs:annotation>
			<xs:documentation>
				Cost center.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="7"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CountedFare" type="tns:CountedFareType"/>
	<xs:simpleType name="CountedFareType">
		<xs:annotation>
			<xs:documentation>
				Indicates whether or not fare count is valid.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						Implicit No, fare count is not valid.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>
						No, fare count is not valid.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="J">
				<xs:annotation>
					<xs:documentation>
						Yes, fare count is valid.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CountryCode" type="tns:CountryCodeType"/>
	<xs:complexType name="CountryCodeType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					Country code in address.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:minLength value="2"/>
				<xs:maxLength value="4" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="CountryName" type="tns:CountryNameType"/>
	<xs:simpleType name="CountryNameType">
		<xs:annotation>
			<xs:documentation>
				Customer country.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CountryOfResidenceName" type="tns:CountryOfResidenceNameType"/>
	<xs:simpleType name="CountryOfResidenceNameType">
		<xs:annotation>
			<xs:documentation>
				Customer country of residence name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CountryOfResidenceNameMandatory" type="tns:CountryOfResidenceNameMandatoryType"/>
	<xs:simpleType name="CountryOfResidenceNameMandatoryType">
		<xs:annotation>
			<xs:documentation>
				Customer country of residence name mandatory.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CountryOfOrigin" type="tns:CountryOfOriginType" />
	<xs:simpleType name="CountryOfOriginType">
		<xs:annotation>
			<xs:documentation>
				Country of origin.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CountryOfOriginMandatory" type="tns:CountryOfOriginMandatoryType" />
	<xs:simpleType name="CountryOfOriginMandatoryType">
		<xs:annotation>
			<xs:documentation>
				Country of origin is mandatory.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditAmount" type="tns:CreditAmountType" />
	<xs:simpleType name="CreditAmountType">
		<xs:annotation>
			<xs:documentation>
				Credit amount. The amount which has been refunded to the customer
				when the order was credited.
				As long as the order is not credited, this field is missing.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int" />
	</xs:simpleType>
	
	<xs:element name="CreditCardTerminalId" type="tns:JournalTerminalIdType">
        <xs:annotation>
            <xs:documentation>
            	CreditCard Terminal Id
            </xs:documentation>
        </xs:annotation>
    </xs:element>
    <xs:simpleType name="JournalTerminalIdType">
        <xs:restriction base="xs:string">
            <xs:length value="16" />
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="ExtraInfo" type="tns:ExtraInfoType" />
	<xs:simpleType name="ExtraInfoType">
		<xs:annotation>
			<xs:documentation>
				Extra information.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string" />
	</xs:simpleType>

	<xs:element name="ExtendedInfo" type="tns:ExtendedInfoType" />
	<xs:simpleType name="ExtendedInfoType">
		<xs:annotation>
			<xs:documentation>
				Extended information max 990 characters
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string" >
			<xs:maxLength value="990" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditCashJournalId" type="tns:CashJournalIdType">
		<xs:annotation>
			<xs:documentation>
				Uniquely identifies a crediting bill per selling point.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="CreditCashierNoteNumber" type="tns:CashJournalIdType">
		<xs:annotation>
			<xs:documentation>
				Uniquely identifies a crediting bill per selling point.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="CardData" type="tns:CreditCardDataType" />
	<xs:element name="CreditCardData" type="tns:CreditCardDataType" />
	<xs:simpleType name="CreditCardDataType">
		<xs:annotation>
			<xs:documentation>
				Credit card data.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="37" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditCardNumber" type="tns:CreditCardNumberType" />
	<xs:simpleType name="CreditCardNumberType">
		<xs:annotation>
			<xs:documentation>
				Credit card number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="24" />
			<xs:pattern value="([0-9])*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditCardReference" type="tns:CreditCardReferenceType" />
	<xs:simpleType name="CreditCardReferenceType">
		<xs:annotation>
			<xs:documentation>
				Credit card reference/retrieval code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="12" />
		</xs:restriction>
	</xs:simpleType>
    
	<xs:element name="CreditDate" type="tns:CreditDateType" />
	<xs:simpleType name="CreditDateType">
		<xs:annotation>
			<xs:documentation>
				The date when a product/service was credited.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="CreditDateTime" type="tns:CreditDateTimeType" />
	<xs:simpleType name="CreditDateTimeType">
		<xs:annotation>
			<xs:documentation>
				The date/time when a product/service was credited.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="CreditingCreditCardSystem" type="tns:CreditingCreditCardSystemType" />
	<xs:simpleType name="CreditingCreditCardSystemType">
		<xs:annotation>
			<xs:documentation>
				Payment credit card system that issued the credit.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditingPaymentCode" type="tns:CreditingPaymentCodeType" />
	<xs:simpleType name="CreditingPaymentCodeType">
		<xs:annotation>
			<xs:documentation>
				Payment code to be used when crediting.
                This payment code should be stored in the orderbook if a refund action is made.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>
    
     <xs:element name="CreditingPaymentModelCode" type="tns:CreditingPaymentModelCodeType"/>
     <xs:simpleType name="CreditingPaymentModelCodeType">
                <xs:annotation>
                    <xs:documentation>
                        A value exists only when purchase and refund should be separate. (gross value) 
                        A missing value indicates net management (nettohantering) or not used at the moment.  
                    </xs:documentation>
                </xs:annotation>
                <xs:restriction base="xs:string">
                    <xs:maxLength value="3" />
                </xs:restriction>
     </xs:simpleType>         

	<xs:element name="CreditingCreditCardReference" type="tns:CreditingCreditCardReferenceType" />
	<xs:simpleType name="CreditingCreditCardReferenceType">
		<xs:annotation>
			<xs:documentation>
				Credit card reference when crediting.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditPoints" type="tns:CreditPointsType" />
	<xs:simpleType name="CreditPointsType">
		<xs:annotation>
			<xs:documentation>
				Number of creditable (KPS) points for a service/component.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="********" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditProducerCode" type="tns:CreditProducerCodeType" />
	<xs:simpleType name="CreditProducerCodeType">
		<xs:annotation>
			<xs:documentation>
				Identification code of the producer that credited ths product/service.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditReason" type="tns:CreditReasonType" />
	<xs:complexType name="CreditReasonType">
		<xs:annotation>
			<xs:documentation>
				Crediting reason code.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>
    
    <xs:element name="CreditRuleCode" type="tns:CreditRuleCodeType"/>
    <xs:complexType name="CreditRuleCodeType">
                <xs:annotation>
                    <xs:documentation>
                        Indicates possible limitations for refunds.
                        
                        Possible values 
                        blankt  Not possible
                        EJ  Not allowed
                        TI  Allowed 
                        IK  Allowed within the same firm group 
                        DB  Allowed, particular control is mandatory. 
                        TK  Allowed, credit card coupling is mandatory. 
                        TS  Allowed if the customer is not blocked
                        TN  Refund allowed if net value is >=  0
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleContent>
                    <xs:restriction base="tns:StringCodeType">
                        <xs:maxLength value="2" />
                    </xs:restriction>
                </xs:simpleContent>
      </xs:complexType> 

	<xs:element name="CreditTypeCode" type="tns:CreditTypeCodeType" />
	<xs:simpleType name="CreditTypeCodeType">
		<xs:annotation>
			<xs:documentation>
				Crediting reason code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditTerminalId" type="tns:TerminalIdType">
		<xs:annotation>
			<xs:documentation>
				Id of the terminal from where the order was credited.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="CreditUserId" type="tns:CreditUserIdType" />
	<xs:simpleType name="CreditUserIdType">
		<xs:annotation>
			<xs:documentation>
				Identifies the user that issued the credit.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerAuthenticationLevel">
		<xs:annotation>
			<xs:documentation>
				Customer account authentication/validation level.
	      </xs:documentation>
		</xs:annotation>
		<xs:simpleType>
			<xs:restriction base="xs:string">
				<xs:length value="3"></xs:length>
				<xs:enumeration value="STA">
					<xs:annotation>
						<xs:documentation>
							Strong
						</xs:documentation>
					</xs:annotation>
				</xs:enumeration>
				<xs:enumeration value="SVA">
					<xs:annotation>
						<xs:documentation>
							Weak
						</xs:documentation>
					</xs:annotation>
				</xs:enumeration>
			</xs:restriction>
		</xs:simpleType>
	</xs:element>

	<xs:element name="CustomerCity" type="tns:CustomerCityType"/>
	<xs:simpleType name="CustomerCityType">
		<xs:annotation>
			<xs:documentation>
				Customer city.
				Don't use for new interfaces. Use CustomerCityName instead.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerCityName" type="tns:CustomerCityNameType"/>
	<xs:simpleType name="CustomerCityNameType">
		<xs:annotation>
			<xs:documentation>
				Customer city.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="27" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerDiscountCode" type="tns:CustomerDiscountCodeType" />
	<xs:simpleType name="CustomerDiscountCodeType">
		<xs:annotation>
			<xs:documentation>
				Identifies a particular customer discount.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerId" type="tns:CustomerIdType" />
	<xs:simpleType name="CustomerIdType">
		<xs:annotation>
			<xs:documentation>
				Unique identification of a private customer in PETRA.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="10" />
			<xs:pattern value="([0-9])*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerName" type="tns:CustomerNameType" />
	<xs:simpleType name="CustomerNameType">
		<xs:annotation>
			<xs:documentation>
				Company/Department name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerNote" type="tns:NoteType" />

	<xs:element name="CustomerOrganisationNumber" type="tns:CustomerOrganisationNumberType"/>
	<xs:simpleType name="CustomerOrganisationNumberType">
		<xs:annotation>
			<xs:documentation>
				Customer Organisation (BASUN).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
	        <xs:maxLength value="11"/>
	        <xs:pattern value="([0-9])*"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerPin" type="tns:PinCodeType">
		<xs:annotation>
			<xs:documentation>
				Customer pin code.

				Corresponding Element: See PinCodeType
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="CustomerPriority" type="tns:CustomerPriorityType" />
	<xs:simpleType name="CustomerPriorityType">
		<xs:annotation>
			<xs:documentation>
				Customer Priority (Gold / Silver / VIP /...)
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerRoleCode" type="tns:CustomerRoleCodeType" />
	<xs:simpleType name="CustomerRoleCodeType">
		<xs:annotation>
			<xs:documentation>
				Role in company.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="SKR">
		        <xs:annotation>
			        <xs:documentation>Printer</xs:documentation>
			    </xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BES">
                <xs:annotation>
                    <xs:documentation>Orderer</xs:documentation>
                </xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="">
                <xs:annotation>
                    <xs:documentation>Traveller</xs:documentation>
                </xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="INB">
                <xs:annotation>
                    <xs:documentation>Internet booker</xs:documentation>
                </xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerStreet" type="tns:CustomerStreetType"/>
	<xs:simpleType name="CustomerStreetType">
		<xs:annotation>
			<xs:documentation>
				Customer street.
				Don't use for new interfaces. Use CustomerStreetName instead.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerStreetName" type="tns:CustomerStreetNameType"/>
	<xs:simpleType name="CustomerStreetNameType">
		<xs:annotation>
			<xs:documentation>
				Customer street name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerStreetNameExtra" type="tns:CustomerStreetNameExtraType"/>
	<xs:simpleType name="CustomerStreetNameExtraType">
		<xs:annotation>
			<xs:documentation>
				Customer street address extra information.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerZip" type="tns:CustomerZipType"/>
	<xs:simpleType name="CustomerZipType">
		<xs:annotation>
			<xs:documentation>
				Customer zip code.
				Don't use for new interfaces. Use CustomerZipCode instead.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerZipCode" type="tns:CustomerZipCodeType"/>
	<xs:simpleType name="CustomerZipCodeType">
		<xs:annotation>
			<xs:documentation>
				Customer zip code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="9" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="DateOfPrinting" type="tns:DateOfPrintingType"></xs:element>
	<xs:simpleType name="DateOfPrintingType">
		<xs:annotation>
			<xs:documentation>
				Print out/distribution date.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<!-- =============================== D ============================ -->
	
	<xs:element name="DateTime" type="tns:JournalDateTimeType">
        <xs:annotation>
            <xs:documentation>
            	Timestamp for the initial payment transaction.
            </xs:documentation>
        </xs:annotation>
    </xs:element>
    <xs:simpleType name="JournalDateTimeType">
        <xs:restriction base="xs:dateTime">
        </xs:restriction>
    </xs:simpleType>
    
    <xs:element name="DebitCredit" type="tns:DebitCreditType">
        <xs:annotation>
            <xs:documentation>
                Debit-/CreditCode
                D = Debit
                K = Credit
                Others = something wrong
            </xs:documentation>
        </xs:annotation>
    </xs:element>
    <xs:simpleType name="DebitCreditType">
        <xs:restriction base="xs:string" />
    </xs:simpleType>

    <xs:element name="DelayCompensationCode" type="tns:DelayCompensationCodeType" />
    <xs:complexType name="DelayCompensationCodeType">
        <xs:annotation>
            <xs:documentation>
                Indicates how a delay is compensated.
            </xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:restriction base="tns:StringCodeType">
                <xs:maxLength value="4" />
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>

	<xs:element name="DepartureDate" type="tns:DepartureDateType"/>
	<xs:simpleType name="DepartureDateType">
		<xs:annotation>
			<xs:documentation>
				Specifies the departure date, for a component of a service,
				i.e. the first date the customer is first entitled to exploit the
				resource (component).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="DepartureDateTime" type="tns:DepartureDateTimeType"/>
	<xs:simpleType name="DepartureDateTimeType">
		<xs:annotation>
			<xs:documentation>
				Specifies the departure date/time, for a component of a service,
				i.e. the first date/time the customer is first entitled to exploit the
				resource (component).

				Corresponding elements: DepartureDate + DepartureTime
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="DepartureTime" type="tns:DepartureTimeType"/>
	<xs:simpleType name="DepartureTimeType">
		<xs:annotation>
			<xs:documentation>
				Specifies the departure time, for a component of a service,
				i.e. the first time the customer is first entitled to exploit the
				resource (component).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:time"/>
	</xs:simpleType>

	<xs:element name="LongDescription" type="tns:LongDescriptionType" />
	<xs:simpleType name="LongDescriptionType">
		<xs:annotation>
			<xs:documentation>
				A long description text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string" />
	</xs:simpleType>
	
	<xs:element name="Description" type="tns:DescriptionType"/>
	<xs:simpleType name="DescriptionType">
		<xs:annotation>
			<xs:documentation>
				A description text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="VariantDescription" type="tns:VariantDescriptionType"/>
	<xs:simpleType name="VariantDescriptionType">
		<xs:annotation>
			<xs:documentation>
				A variant description text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="50" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="DialogNumber" type="tns:DialogNumberType" />
	<xs:simpleType name="DialogNumberType">
		<xs:annotation>
			<xs:documentation>
				Dialog number when we communicate with systems abroad.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="99999"/>
		</xs:restriction>
	</xs:simpleType>
    
    <xs:element name="DialogText" type="tns:DialogTextType"></xs:element>
    <xs:simpleType name="DialogTextType">
        <xs:annotation>
            <xs:documentation>
                A descriptive text. 
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="30"></xs:maxLength>
        </xs:restriction>
    </xs:simpleType>
    
	<xs:element name="DiscountCode" type="tns:DiscountCodeType" />
	<xs:complexType name="DiscountCodeType">
		<xs:annotation>
			<xs:documentation>
				Identifies a particular discount.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="3" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="DiscountType" type="tns:DiscountTypeType" />
	<xs:simpleType name="DiscountTypeType">
		<xs:annotation>
			<xs:documentation>
				Identifies a particular type/kind of discount.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="DistributorUrl" type="tns:DistributorUrlType" />
	<xs:simpleType name="DistributorUrlType">
		<xs:annotation>
			<xs:documentation>
				Distributor website URL
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="TravelAgencyCustomerId" type="tns:TravelAgentCustomerIdType"/>
 		<xs:simpleType name="TravelAgentCustomerIdType">
 			<xs:annotation>
 				<xs:documentation>
 					Traveller customerId with travel agency.
 				</xs:documentation>
 			</xs:annotation>
 			<xs:restriction base="xs:string">
 				<xs:minLength value="1"></xs:minLength>
 				<xs:maxLength value="14"></xs:maxLength>
 			</xs:restriction>
 		</xs:simpleType>

	<xs:element name="DistanceDiscountCode" type="tns:DistanceDiscountCodeType" />
	<xs:simpleType name="DistanceDiscountCodeType">
		<xs:annotation>
			<xs:documentation>
				Distance discount code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="DistributionAddressSequenceNumber" type="tns:DistributionAddressSequenceNumberType" />
	<xs:simpleType name="DistributionAddressSequenceNumberType">
		<xs:annotation>
			<xs:documentation>
				Address serial number in order database.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="9999"/>
		</xs:restriction>
	</xs:simpleType>
    
     <xs:element name="DBDocCode" type="tns:DBDocCodeType"/>
     <xs:simpleType name="DBDocCodeType">
                <xs:annotation>
                    <xs:documentation>
                        Used to determine which direct letter document to be used. (invoice, delivery note etc.).
                        Possible values
                        E   Post invoice
                        F   Invoice 
                        K   Receipt (credit card purchase)
                        S   Delivery note 
                        V   Voucher
                        blank  'Direktbrev' is not possible
                    </xs:documentation>
                </xs:annotation>
                <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                </xs:restriction>
     </xs:simpleType>

     <xs:element name="DBRestrictionCode" type="tns:DBRestrictionCodeType"/>
     <xs:simpleType name="DBRestrictionCodeType">
                <xs:annotation>
                    <xs:documentation>
                        Direct letter is required as delivery method.
                        J =  Direct letter is mandatory.
                        N =  Direct letter letterbrev is not allowed. 
                        Blank = Direktbrev is allowed but not required.
                    </xs:documentation>
                </xs:annotation>
                <xs:restriction base="xs:string">
                    <xs:maxLength value="1" />
                </xs:restriction>
    </xs:simpleType>
    
	<xs:element name="DistributionCode" type="tns:DistributionCodeType" />
	<xs:complexType name="DistributionCodeType">
		<xs:annotation>
			<xs:documentation>
				Distribution code.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
			</xs:simpleContent>
	</xs:complexType>

	<xs:element name="DistributionType" type="tns:DistributionTypeType" />
	<xs:simpleType name="DistributionTypeType">
		<xs:annotation>
			<xs:documentation>
				Distribution type.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="DistributionUserId" type="tns:DistributionUserIdType" />
	<xs:simpleType name="DistributionUserIdType">
		<xs:annotation>
			<xs:documentation>
				Identifies the user that issued the print out/distribution.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="DocumentName" type="tns:DocumentNameType" />
	<xs:simpleType name="DocumentNameType">
		<xs:annotation>
			<xs:documentation>
				Document (long) name written on the on the ticket or receipt.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="40" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="DocumentPrintOutCode" type="tns:DocumentPrintOutCodeType" />
	<xs:simpleType name="DocumentPrintOutCodeType">
		<xs:annotation>
			<xs:documentation>
				Document print out code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== E ============================ -->

	<xs:element name="EconomyTransactionSystem" type="tns:EconomyTransactionSystemType" />
	<xs:simpleType name="EconomyTransactionSystemType">
		<xs:annotation>
			<xs:documentation>
				MQ-info to send any economy messages.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="8"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="EditActionStatusCode" type="tns:EditActionStatusCodeType" />
	<xs:simpleType name="EditActionStatusCodeType">
	  <xs:annotation>
	    <xs:documentation>
	      Status of component
	    </xs:documentation>
	  </xs:annotation>
	    <xs:restriction base="xs:string">
	    <xs:enumeration value="AN">
	      <xs:annotation>
	        <xs:documentation>
	          Cancelled (system-initiated, order not paid)
	        </xs:documentation>
	      </xs:annotation>
	    </xs:enumeration>
	    <xs:enumeration value="FO">
	      <xs:annotation>
	        <xs:documentation>
	          Born
	        </xs:documentation>
	      </xs:annotation>
	    </xs:enumeration>
	    <xs:enumeration value="KL">
	      <xs:annotation>
	        <xs:documentation>
	          Ready
	        </xs:documentation>
	      </xs:annotation>
	    </xs:enumeration>
	  </xs:restriction>
	</xs:simpleType>

	<xs:element name="EditCode1" type="tns:EditCode1Type" />
	<xs:simpleType name="EditCode1Type">
		<xs:annotation>
			<xs:documentation>
				Code (type 1) for editing via location.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="EditCode2" type="tns:EditCode2Type" />
	<xs:simpleType name="EditCode2Type">
		<xs:annotation>
			<xs:documentation>
				Code (type 2) for editing via location.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>
    
    <xs:element name="EditedCustomerName" type="tns:EditedCustomerNameType"/>
    <xs:simpleType name="EditedCustomerNameType">
        <xs:annotation>
            <xs:documentation>
                Edited customer name. If a customer name is edited.           
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="97"></xs:maxLength>
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="ElectronicDistributionDateTime" type="tns:ElectronicDistributionDateTimeType" />
	<xs:simpleType name="ElectronicDistributionDateTimeType">
		<xs:annotation>
			<xs:documentation>
				Electronic print out/distribution date/time.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="ElectronicDistributionTime" type="tns:ElectronicDistributionTimeType" />
	<xs:simpleType name="ElectronicDistributionTimeType">
		<xs:annotation>
			<xs:documentation>
				Electronic print out/distribution time (of day).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:time"/>
	</xs:simpleType>

	<xs:element name="ElectronicPrintOutCode" type="tns:ElectronicPrintOutCodeType" />
	<xs:simpleType name="ElectronicPrintOutCodeType">
		<xs:annotation>
			<xs:documentation>
				Electronic print out code, eg. email, SMS.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="EmailAddress" type="tns:EmailAddressType"/>
	<xs:simpleType name="EmailAddressType">
		<xs:annotation>
			<xs:documentation>
				E-mail address.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="80" />
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="EmptyType">
		<xs:annotation>
			<xs:documentation>
				Empty type that may be used for Boolean types of information.
			</xs:documentation>
		</xs:annotation>
	</xs:complexType>

	<xs:element name="EncryptedCardNumber" type="tns:EncryptedCardNumberType" />
	<xs:simpleType name="EncryptedCardNumberType">
		<xs:annotation>
			<xs:documentation>
				Encrypted credit card number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="48" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="EncryptionKeyId" type="tns:EncryptionKeyIdType" />
	<xs:simpleType name="EncryptionKeyIdType">
		<xs:annotation>
			<xs:documentation>
				Id that uniquely identifies the key used for encryption.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="5" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ErrorCode" type="tns:ErrorCodeType" />
	<xs:simpleType name="ErrorCodeType">
		<xs:annotation>
			<xs:documentation>
				Error text number, declared by constants.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1"/>
			<xs:maxInclusive value="99999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ErrorText" type="xs:string" />
	<xs:simpleType name="ErrorTextType">
		<xs:annotation>
			<xs:documentation>
				Error text corresponding to constants.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="255" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Establishment" type="tns:EstablishmentType" />
	<xs:simpleType name="EstablishmentType">
		<xs:annotation>
			<xs:documentation>
				Establishment can be associated with a company/office name related to a purchase.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="EventType" type="tns:EventTypeType" />
	<xs:simpleType name="EventTypeType">
		<xs:annotation>
			<xs:documentation>
				Type of an event. (CREATED/RETURNED/SENT)
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="8" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ExceptionSeverity" type="tns:ExceptionSeverityType"/>
	<xs:simpleType name="ExceptionSeverityType">
		<xs:annotation>
			<xs:documentation>
				Indicates severety of the error. No update will generate a rollback.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="INFORMATION" />
			<xs:enumeration value="INFORMATION_NO_UPDATE" />
			<xs:enumeration value="WARNING" />
			<xs:enumeration value="WARNING_NO_UPDATE" />
			<xs:enumeration value="SEVERE_ERROR" />
			<xs:enumeration value="SEVERE_ERROR_NO_UPDATE" />
			<xs:enumeration value="FATAL_ERROR" />
			<xs:enumeration value="FATAL_ERROR_NO_UPDATE" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ExternalContractNumber" type="tns:ExternalContractNumberType" />
	<xs:simpleType name="ExternalContractNumberType">
		<xs:annotation>
			<xs:documentation>
				External contract number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="19" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ExternalCustomerReference" type="tns:ExternalCustomerReferenceType" />
	<xs:simpleType name="ExternalCustomerReferenceType">
		<xs:annotation>
			<xs:documentation>
				Customer reference in an external booking system
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ExternalOfferCode" type="tns:ExternalOfferCodeType" />
	<xs:simpleType name="ExternalOfferCodeType">
		<xs:annotation>
			<xs:documentation>
				External offer code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="25" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ExternalReferenceId" type="tns:ExternalReferenceIdType" />
	<xs:simpleType name="ExternalReferenceIdType">
		<xs:annotation>
			<xs:documentation>
				External Reference Id.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="36" />
		</xs:restriction>
	</xs:simpleType>
	
    <xs:element name="ExternalSettlementId" type="tns:ExternalSettlementIdType" />
    <xs:simpleType name="ExternalSettlementIdType">
        <xs:annotation>
            <xs:documentation>
                External settlement id.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="50" />
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="ExternalTicketText" type="tns:ExternalTicketTextType" />
	<xs:simpleType name="ExternalTicketTextType">
		<xs:annotation>
			<xs:documentation>
				External offer code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ExpirationDate" type="tns:ToDateType">
		<xs:annotation>
			<xs:documentation>
				Expiration date (variant of ToDate often used for validity ranges).

				Corresponding element: See ToDateType
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="ExpireDate" type="tns:ExpireDateType" />
	<xs:simpleType name="ExpireDateType">
		<xs:annotation>
			<xs:documentation>
				Valid until YYMM.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4"></xs:maxLength>
			<xs:pattern value="([0-9])*"></xs:pattern>
		</xs:restriction>
	</xs:simpleType>
    
     <xs:element name="ExchangeableMixable" type="tns:ExchangableMixableType"/>
     <xs:simpleType name="ExchangableMixableType">
        <xs:annotation>
            <xs:documentation>
                    The payment method is mixable during rebooking/change of the ticket.

                    N = If there is a difference in price, then the difference must be
                    paid with the same payment method.
                    J = Possible to mix with other payment methods.
                    Blank = Exchange is not possible for the payment method.     
            </xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:string">
        <xs:maxLength value="1"></xs:maxLength>
      </xs:restriction>
     </xs:simpleType>

	<!-- =============================== F ============================ -->

	<xs:element name="FareAvailability" type="tns:FareAvailabilityType" />
	<xs:simpleType name="FareAvailabilityType">
		<xs:annotation>
			<xs:documentation>
				Fare availability, i.e. number of available tickets.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt"/>
	</xs:simpleType>

	<xs:element name="FareCategory" type="tns:FareCategoryType" />
	<xs:simpleType name="FareCategoryType">
		<xs:annotation>
			<xs:documentation>
				Fare counter category.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						Regular counters.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="S">
				<xs:annotation>
					<xs:documentation>
						Special counters.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="FareType" type="tns:FareTypeType"/>
	<xs:complexType name="FareTypeType">
		<xs:annotation>
			<xs:documentation>
				Fare counter type.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
			<xs:maxLength value="2"/>
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="FirmName" type="tns:FirmNameType" />
	<xs:simpleType name="FirmNameType">
		<xs:annotation>
			<xs:documentation>
				Firm name for sole trader.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="48"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="FirstName" type="tns:FirstNameType"/>
	<xs:simpleType name="FirstNameType">
		<xs:annotation>
			<xs:documentation>
				Customer's first name in Petra customer records.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:token">
			<xs:maxLength value="15" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="FreeText" type="tns:FreeTextType" />
	<xs:simpleType name="FreeTextType">
		<xs:annotation>
			<xs:documentation>
				Free text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="30" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="FreeTextRow" type="tns:FreeTextRowType" />
	<xs:simpleType name="FreeTextRowType">
		<xs:annotation>
			<xs:documentation>
				Ticket free text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="50" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="FreeTextRowNumber" type="tns:FreeTextRowNumberType" />
	<xs:simpleType name="FreeTextRowNumberType">
		<xs:annotation>
			<xs:documentation>
				Ticket free text row number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="FromDate" type="tns:FromDateType"/>
	<xs:simpleType name="FromDateType">
		<xs:annotation>
			<xs:documentation>
				From date.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>
	
	<xs:element name="FromDateTime" type="tns:FromDateTimeType"/>
	<xs:simpleType name="FromDateTimeType">
		<xs:annotation>
			<xs:documentation>
				From date time.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="FromFareZone" type="tns:FromFareZoneType"/>
	<xs:simpleType name="FromFareZoneType">
		<xs:annotation>
			<xs:documentation>
				Specifies the from zone a relational concerns.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="99999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="FromOrderItemId" type="tns:OrderItemIdType" />

	<xs:element name="FromTicketStockNumber" type="tns:FromTicketStockNumberType"/>
	<xs:simpleType name="FromTicketStockNumberType">
		<xs:annotation>
			<xs:documentation>
				From ticket stock number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="********"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== G ============================ -->

	<xs:element name="GLAccount" type="tns:GLAccountType"/>
	<xs:simpleType name="GLAccountType">
		<xs:annotation>
			<xs:documentation>
				G / L Account.
   			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="99999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="GroupReference" type="tns:GroupReferenceType" />
	<xs:simpleType name="GroupReferenceType">
		<xs:annotation>
			<xs:documentation>
				Group reference, stored as a secondary reference number for group travel transport components.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="GuaranteeCode" type="tns:GuaranteeCodeType" />
	<xs:simpleType name="GuaranteeCodeType">
		<xs:annotation>
			<xs:documentation>
				Flag for guaranteed booking, J / C / blank.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						Implicit no guarantee.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>
						No guarantee.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="J">
				<xs:annotation>
					<xs:documentation>
						Guaranteed in a, to PETRA, unknown way.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="C">
				<xs:annotation>
					<xs:documentation>
						Credit card guarantee.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== H ============================ -->

	<xs:element name="HaveEXTRows" type="tns:HaveEXTRowsType" />
	<xs:simpleType name="HaveEXTRowsType">
		<xs:annotation>
			<xs:documentation>
				Related rows have extendend information stored, J/blank.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="HaveNMNRows" type="tns:HaveNMNRowsType" />
	<xs:simpleType name="HaveNMNRowsType">
		<xs:annotation>
			<xs:documentation>
				Extendend information of customer contact information is available.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="J">
				<xs:annotation>
					<xs:documentation>
						Yes.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>
						No.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="HaveSKKRow" type="tns:HaveSKKRowType" />
	<xs:simpleType name="HaveSKKRowType">
		<xs:annotation>
			<xs:documentation>
				Extendend information of SKK-components exists is available.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="HelpText" type="tns:HelpTextType"/>
	<xs:simpleType name="HelpTextType">
		<xs:annotation>
			<xs:documentation>
				Helptext indented for service descriptions or menu options. The field may contain HTML code if necessary.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="400" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="HelpTextLong" type="tns:HelpTextLongType"/>
	<xs:simpleType name="HelpTextLongType">
		<xs:annotation>
			<xs:documentation>
				Long help text indented for use either standalone or in combination with the reguar help text. The field may contain HTML code if necessary.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2000" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="HotelChain" type="tns:HotelChainType" />
	<xs:simpleType name="HotelChainType">
		<xs:annotation>
			<xs:documentation>
				The hotel chain name/identity.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="55" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="HotelId" type="tns:HotelIdType" />
	<xs:simpleType name="HotelIdType">
		<xs:annotation>
			<xs:documentation>
				System identity of a hotel.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="12" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="HotelCustomerId" type="tns:HotelCustomerIdType" />
	<xs:simpleType name="HotelCustomerIdType">
		<xs:annotation>
			<xs:documentation>
				Organization number in external hotel booking system.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt">
 			<xs:maxInclusive value="**********" />
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== I ============================ -->

	<xs:element name="IATACode" type="tns:IATACodeType" />
	<xs:simpleType name="IATACodeType">
		<xs:annotation>
			<xs:documentation>
				City/Airport-code established by IATA (International Air Transport
				Association)
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="IdRequirement" type="tns:IdRequirementType" />
	<xs:simpleType name="IdRequirementType">
		<xs:annotation>
			<xs:documentation>
				Traveler's identification requirements (Only registered travelers)
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="InOutIndicator" type="tns:InOutIndicatorType"/>
	<xs:simpleType name="InOutIndicatorType">
		<xs:annotation>
			<xs:documentation>
				RPO in out indicator
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[IO]" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="InternationalSupplementCode" type="tns:InternationalSupplementCodeType" />
	<xs:simpleType name="InternationalSupplementCodeType">
		<xs:annotation>
			<xs:documentation>
				International supplement code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="InvoiceReference" type="tns:InvoiceReferenceType" />
	<xs:simpleType name="InvoiceReferenceType">
		<xs:annotation>
			<xs:documentation>
				Invoice reference.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="50"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>
    
    <xs:element name="IsMixable" type="tns:IsMixableType"/> 
    <xs:simpleType name="IsMixableType">
                <xs:annotation>
                    <xs:documentation>
                        The payment method is mixable with vouchers.
                        J = Indicates if the payment method is mixable.
                        N = The payment method can not be mixable with other payment types.
                        Blank = The payment method has no rule. It is not decided if it is mixable or not.   
                    </xs:documentation>
                </xs:annotation>
                <xs:restriction base="xs:string">
            <xs:maxLength value="1"></xs:maxLength>
        </xs:restriction>
     </xs:simpleType>

	<xs:element name="ItineraryReference" type="tns:ItineraryReferenceType" />
	<xs:simpleType name="ItineraryReferenceType">
		<xs:annotation>
			<xs:documentation>
				Itinerary reference uniquely identifies an itinerary among
				a set of itineraries within a journey-advice.
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="99" />
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== J ============================ -->

	<xs:element name="JourneyConnectionReference" type="tns:JourneyConnectionReferenceType" />
	<xs:simpleType name="JourneyConnectionReferenceType">
		<xs:annotation>
			<xs:documentation>
				Travel proposal reference, uniquely identifies a journey-advice
				(from timetable).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== K ============================ -->

	<xs:element name="KpsBurnCode" type="tns:KpsBurnCodeType"/>
	<xs:simpleType name="KpsBurnCodeType">
		<xs:annotation>
			<xs:documentation>
				Indicates if KPS points should be used.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value=" " >
                <xs:annotation>
                    <xs:documentation>Undefined</xs:documentation>
                </xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N" >
                <xs:annotation>
                    <xs:documentation>Don't use KPS points</xs:documentation>
                </xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="J" >
                <xs:annotation>
                    <xs:documentation>Use KPS Points</xs:documentation>
                </xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== L ============================ -->

	<xs:element name="LanguageCode" type="tns:LanguageCodeType"/>
	<xs:simpleType name="LanguageCodeType">
		<xs:annotation>
			<xs:documentation>
				Specifies which language is used for terms and dialogues.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="da" />
			<xs:enumeration value="de" />
			<xs:enumeration value="en" />
			<xs:enumeration value="fr" />
			<xs:enumeration value="no" />
			<xs:enumeration value="sv" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="LastActivityDate" type="tns:LastActivityDateType"/>
	<xs:simpleType name="LastActivityDateType">
		<xs:annotation>
			<xs:documentation>
				Time of last use.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date">
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="LastName" type="tns:LastNameType"/>
	<xs:simpleType name="LastNameType">
		<xs:annotation>
			<xs:documentation>
				Customer's last name in Petra customer records.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:token">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="LastRetrievalDate" type="tns:LastRetrievalDateType" />
	<xs:simpleType name="LastRetrievalDateType">
		<xs:annotation>
			<xs:documentation>
				The last date the order can be printed/retrieved.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="LatestCancellationDateTime" type="tns:LatestCancellationDateTimeType" />
	<xs:simpleType name="LatestCancellationDateTimeType">
		<xs:annotation>
			<xs:documentation>
				The latest date/time for cancellation.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="LocationCategory" type="tns:LocationCategoryType" />
	<xs:simpleType name="LocationCategoryType">
		<xs:annotation>
			<xs:documentation>
				Station catagory.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="LocationMapReference" type="tns:LocationMapReferenceType" />
	<xs:simpleType name="LocationMapReferenceType">
		<xs:annotation>
			<xs:documentation>
				Country part, in semicolon separated string.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="LocationProducerCode" type="tns:LocationProducerCodeType" />
	<xs:simpleType name="LocationProducerCodeType">
		<xs:annotation>
			<xs:documentation>
				Unique producer id for a specific location.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Logotype" type="tns:LogotypeType"/>
	<xs:simpleType name="LogotypeType">
		<xs:annotation>
			<xs:documentation>
				Logotype on a coupon.
			</xs:documentation>
      	</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>

    <xs:element name="LogotypeUrl" type="tns:LogotypeUrlType"/>
    <xs:simpleType name="LogotypeUrlType">
        <xs:annotation>
            <xs:documentation>
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string" />
    </xs:simpleType>

	<xs:element name="LocationId" type="tns:LocationIdType" />
	<xs:simpleType name="LocationIdType">
		<xs:annotation>
			<xs:documentation>
				Unique id number for a specific location.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt">
 			<xs:maxInclusive value="99999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="LocationCode" type="tns:LocationCodeType" />
	<xs:simpleType name="LocationCodeType">
		<xs:annotation>
			<xs:documentation>
				Trafikverket's (Banverket's) signature for a specific location.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="LocationNameShort" type="tns:LocationNameShortType" />
	<xs:simpleType name="LocationNameShortType">
		<xs:annotation>
			<xs:documentation>
				Short name of a specific location.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="17" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="LocationName" type="tns:LocationNameType" />
	<xs:simpleType name="LocationNameType">
		<xs:annotation>
			<xs:documentation>
				Name of a specific location.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="LocationLongName" type="tns:LocationLongNameType" />
	<xs:simpleType name="LocationLongNameType">
		<xs:annotation>
			<xs:documentation>
				Long name of a specific location.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="65" />
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== M ============================ -->

	<xs:element name="Mandatory" type="tns:MandatoryType"/>
	<xs:simpleType name="MandatoryType">
		<xs:annotation>
			<xs:documentation>
				Mandatory.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="MarketSegmentTypeCode" type="tns:MarketSegmentTypeCodeType"/>
	<xs:simpleType name="MarketSegmentTypeCodeType">
		<xs:annotation>
			<xs:documentation>
				RPO market segment type.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="MaskedCreditCardNumber" type="tns:MaskedCreditCardNumberType" />
	<xs:simpleType name="MaskedCreditCardNumberType">
		<xs:annotation>
			<xs:documentation>
				Credit card number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="24" />
			<xs:pattern value="([0-9\*])*" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="MaxPriceRecalculateFactor" type="tns:PriceRecalculateFactorType">
		<xs:annotation>
			<xs:documentation>
				Maximum price recalculate factor.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="MembershipCardNumber" type="tns:MembershipCardNumberType" />
	<xs:simpleType name="MembershipCardNumberType">
		<xs:annotation>
			<xs:documentation>
				Membership Card number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="24" />
			<xs:pattern value="\s*|[A-Z0-9]*" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="CardMeasure" type="tns:CardMeasureType" />
	<xs:complexType name="CardMeasureType">
		<xs:annotation>
			<xs:documentation>
				Measure type.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="MerchantNumber" type="tns:MerchantNumberType" /> 
	<xs:simpleType name="MerchantNumberType">
        <xs:annotation>
	        <xs:documentation>
	        	MerchantId 
	        </xs:documentation>
	    </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="10" />
        </xs:restriction>
	</xs:simpleType>

    <xs:simpleType name="MessageCategoryType">
        <xs:restriction base="xs:string">
            <xs:maxLength value="2" />
            <xs:enumeration value="BE">
                <xs:annotation>
                    <xs:documentation>Confirmation message</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DM">
                <xs:annotation>
                    <xs:documentation>Deviation message</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="EK">
                <xs:annotation>
                    <xs:documentation>Electronical receipt</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="LO">
                <xs:annotation>
                    <xs:documentation>Order service alarm message</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MB">
                <xs:annotation>
                    <xs:documentation>Mobile ticket</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MK">
                <xs:annotation>
                    <xs:documentation>Customer message</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OD">
                <xs:annotation>
                    <xs:documentation>Undefined</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PL">
                <xs:annotation>
                    <xs:documentation>Passenger list</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TF">
                <xs:annotation>
                    <xs:documentation>Notification when timetable is changed.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="MinPriceRecalculateFactor" type="tns:PriceRecalculateFactorType">
		<xs:annotation>
			<xs:documentation>
				Minimum price recalculate factor.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

    <xs:element name="MobileDeviceId" type="tns:MobileDeviceIdType" />
    <xs:simpleType name="MobileDeviceIdType">
        <xs:annotation>
            <xs:documentation>ID of a mobile device.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="32" />
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="MobilePhone" type="tns:MobilePhoneType"/>
	<xs:simpleType name="MobilePhoneType">
		<xs:annotation>
			<xs:documentation>
				Mobile phone number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="16" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ModifiedDate" type="tns:ModifiedDateType"/>
	<xs:simpleType name="ModifiedDateType">
		<xs:annotation>
			<xs:documentation>
				(Last) modified date.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date">
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="ModifiedReason" type="tns:ModifiedReasonType"/>
	<xs:simpleType name="ModifiedReasonType">
		<xs:annotation>
			<xs:documentation>
				(Last) modified reason.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="MovedFromDateTime" type="tns:MovedFromDateTimeType"/>
	<xs:simpleType name="MovedFromDateTimeType">
		<xs:annotation>
			<xs:documentation>
				The date/time the order item was moved from an order.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime">
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="MovedToDateTime" type="tns:MovedToDateTimeType"/>
	<xs:simpleType name="MovedToDateTimeType">
		<xs:annotation>
			<xs:documentation>
				The date/time the order item was moved to an order.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime">
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="MultiRideTicketNumberMandatory" type="tns:MultiRideTicketNumberMandatoryMandatoryType"/>
	<xs:simpleType name="MultiRideTicketNumberMandatoryMandatoryType">
		<xs:annotation>
			<xs:documentation>
				Multi ride ticket is mandatory.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						Undefined (implicit No).
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="J">
				<xs:annotation>
					<xs:documentation>
						Yes.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>
						No.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== N ============================ -->

	<xs:element name="NetPriceContract" type="tns:EmptyType">
		<xs:annotation>
			<xs:documentation>
				Net price is used in accordance to new contract.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:simpleType name="NonNegativeInt">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="NoteDateTime" type="tns:NoteDateTimeType" />
	<xs:simpleType name="NoteDateTimeType">
		<xs:annotation>
			<xs:documentation>
				Date/time when the note was created.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="NoteRowNumber" type="tns:NoteRowNumberType" />
	<xs:simpleType name="NoteRowNumberType">
		<xs:annotation>
			<xs:documentation>
				Note row number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="NoteRowText" type="tns:NoteType" />
	<xs:simpleType name="NoteType">
		<xs:annotation>
			<xs:documentation>
				Information text/note.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="50"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="NotPrintable" type="tns:EmptyType">
		<xs:annotation>
			<xs:documentation>
				Lock-debit.

				Indicates whether or not an order is locked for payment because
				the order has notes that will result in additional order rows.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="NumberOf" type="tns:NumberOfType" />
	<xs:simpleType name="NumberOfType">
		<xs:annotation>
			<xs:documentation>
				NumberOf Components
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt">
 			<xs:maxInclusive value="60" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="NumberOfCountries" type="tns:NumberOfCountriesType" />
	<xs:simpleType name="NumberOfCountriesType">
		<xs:annotation>
			<xs:documentation>
				NumberOf part, in semicolon separated string.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt">
 			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="NumberOfStandardDiscounts" type="tns:NumberOfStandardDiscountsType" />
	<xs:simpleType name="NumberOfStandardDiscountsType">
		<xs:annotation>
			<xs:documentation>
				Number of standard discounts.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt">
 			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="NonNegativeIntCodeType">
		<xs:simpleContent>
			<xs:extension base="tns:NonNegativeInt">
				<xs:attribute name="plaintext" type="xs:string" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="NumberOfFailedLogins" type="tns:NumberOfFailedLoginsType" />
	<xs:simpleType name="NumberOfFailedLoginsType">
		<xs:annotation>
			<xs:documentation>
				Number of failed login attempts.
    		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="99" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="NumberOfLockedOrderItems" type="tns:NumberOfLockedOrderItemsType" />
	<xs:simpleType name="NumberOfLockedOrderItemsType">
		<xs:annotation>
			<xs:documentation>
				Number of locked order items.
    		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="99" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="NumberOfPassengersCategoryType">
		<xs:annotation>
			<xs:documentation>
				Number of passengers of a given category.
    		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="NumberOfPassengersCategory1" type="tns:NumberOfPassengersCategoryType">
		<xs:annotation>
			<xs:documentation>
				Number of passengers of category 1.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="NumberOfPassengersCategory2" type="tns:NumberOfPassengersCategoryType">
		<xs:annotation>
			<xs:documentation>
				Number of passengers of category 2.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="NumberOfPassengersCategory3" type="tns:NumberOfPassengersCategoryType">
		<xs:annotation>
			<xs:documentation>
				Number of passengers of category 3.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="NumberOfPassengersCategory4" type="tns:NumberOfPassengersCategoryType">
		<xs:annotation>
			<xs:documentation>
				Number of passengers of category 4.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="NumberOfSeats" type="tns:NumberOfSeatsType" />
	<xs:simpleType name="NumberOfSeatsType">
		<xs:annotation>
			<xs:documentation>
				Number of seats.
    		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="99999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="NumberOfStandard2Discounts" type="tns:NumberOfStandard2DiscountsType" />
	<xs:simpleType name="NumberOfStandard2DiscountsType">
		<xs:annotation>
			<xs:documentation>
				Number of standard discounts 2.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt">
 			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== O ============================ -->

	<xs:element name="OptionCategory" type="tns:OptionCategoryType" />
	<xs:simpleType name="OptionCategoryType">
		<xs:annotation>
			<xs:documentation>
				Category of an option.
				Examples: FLEX, ANNAT, STNAVG, ...
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="OrderCreatorCustomerId" type="tns:CustomerIdType">
		<xs:annotation>
			<xs:documentation>
				Order creator customer id, i.e. the id of the customer that created
				the order.
			</xs:documentation>
		</xs:annotation>
	</xs:element>
     
	<xs:element name="OrderCreationDate" type="tns:OrderCreationDateType" />
	<xs:simpleType name="OrderCreationDateType">
		<xs:annotation>
			<xs:documentation>
				The date on which the order was entered in the order log.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>
    
    <xs:element name="OrderChainCreditingCode" type="tns:OrderChainCreditingCodeType"/>
        <xs:simpleType name="OrderChainCreditingCodeType">
                    <xs:annotation>
                        <xs:documentation>
                            Indicates which payment code that should be used when editing an order. A value exist for all payment codes where 
                            editing is possible.
                        </xs:documentation>
                    </xs:annotation>
                    <xs:restriction base="xs:string">
                         <xs:maxLength value="2" />
                    </xs:restriction>
        </xs:simpleType>

	<xs:element name="OrderDate" type="tns:OrderDateType" />
	<xs:simpleType name="OrderDateType">
		<xs:annotation>
			<xs:documentation>
				Date when the order was placed in Petra.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="OrderDateTime" type="tns:OrderDateTimeType" />
	<xs:simpleType name="OrderDateTimeType">
		<xs:annotation>
			<xs:documentation>
				Date/time when the order was placed in Petra.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="OrderId" type="tns:OrderIdType" />
    <xs:element name="SalesOrderId" type="tns:OrderIdType" />
	<xs:simpleType name="OrderIdType">
		<xs:annotation>
			<xs:documentation>
				Identifies a customer's order.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Z][A-Z][A-Z][0-9][0-9][0-9][0-9][A-Z]" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="OrderEvent" type="tns:OrderEventType"/>
	<xs:simpleType name="OrderEventType">
		<xs:annotation>
			<xs:documentation>
				Order event
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="OrderIdReference" type="tns:OrderIdType" >
		<xs:annotation>
			<xs:documentation>
				References an order.
      		</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="OrderItemId" type="tns:OrderItemIdType" />
	<xs:simpleType name="OrderItemIdType">
		<xs:annotation>
			<xs:documentation>
				Row number in an order.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="50" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="OrderItemIdReference" type="tns:OrderItemIdType">
		<xs:annotation>
			<xs:documentation>
				References an order item.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="OrderItemMultipleId" type="tns:OrderItemMultipleIdType"/>
	<xs:simpleType name="OrderItemMultipleIdType">
		<xs:annotation>
			<xs:documentation>
				Used to identifiy identical rows in the VT2 structure.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="9999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="OrderItemStatus" type="tns:OrderItemStatusCodeType" />
	<xs:simpleType name="OrderItemStatusType">
		<xs:annotation>
			<xs:documentation>
				Describes the status the order item is in.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="AN">
				<xs:annotation>
					<xs:documentation>
						Cancelled (system-initiated, order not paid)
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AS">
				<xs:annotation>
					<xs:documentation>
						Refunded (Special cases)
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AT">
				<xs:annotation>
					<xs:documentation>
						Refunded
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AV">
				<xs:annotation>
					<xs:documentation>
						Cancelled (customer-initiated, paid order)
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BK">
				<xs:annotation>
					<xs:documentation>
						Accounted
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="EK">
				<xs:annotation>
					<xs:documentation>
						Not completed
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FL">
				<xs:annotation>
					<xs:documentation>
						Moved
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FO">
				<xs:annotation>
					<xs:documentation>
						Born (New)
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="KL">
				<xs:annotation>
					<xs:documentation>
						Ready
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="OrderItemStatusCodeType">
		<xs:annotation>
			<xs:documentation>
				Describes the status the order item is in.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="tns:OrderItemStatusType">
				<xs:attributeGroup ref="tns:PlaintextAttribute" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="OrderLockId" type="tns:OrderLockIdType" />
	<xs:simpleType name="OrderLockIdType">
		<xs:annotation>
			<xs:documentation>
				Order lock id.
				An order lock id assigns an order to a certain client thus not
				allowing others to access the same order.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
			<xs:maxLength value="11" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="OrderLockTimeout" type="xs:dateTime">
		<xs:annotation>
			<xs:documentation>
				Order lock timeout time.
				An order lock timeout time defines how long any order lock
				is valid.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="OrderNoteType" type="tns:OrderNoteTypeType" />
	<xs:simpleType name="OrderNoteTypeType">
		<xs:annotation>
			<xs:documentation>
				Type of order note.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="OrderNotification" type="tns:OrderNotificationType" />
	<xs:simpleType name="OrderNotificationType">
		<xs:annotation>
			<xs:documentation>
				Order notification flags.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="16" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="OrderStatus" type="tns:OrderStatusCodeType" />
	<xs:simpleType name="OrderStatusType">
		<xs:annotation>
			<xs:documentation>
				Describes the status the order is in.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="B">
				<xs:annotation>
					<xs:documentation>
						Paid
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FO">
				<xs:annotation>
					<xs:documentation>
						Born
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FV">
				<xs:annotation>
					<xs:documentation>
						Forfeit
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="KL">
				<xs:annotation>
					<xs:documentation>
						Ready
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="UH">
				<xs:annotation>
					<xs:documentation>
						Collected (by customer)
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DE">
                <xs:annotation>
                    <xs:documentation>
                        Partially paid
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="OrderStatusCodeType">
		<xs:annotation>
			<xs:documentation>
				Describes the status the order is in.

				Code/clear-text type. The value is the code (of enum type)
				and the plaintext attribute is the clear-text (of string type).
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="tns:OrderStatusType">
				<xs:attributeGroup ref="tns:PlaintextAttribute" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="OrderTerminalId" type="tns:TerminalIdType">
		<xs:annotation>
			<xs:documentation>
				Id of the terminal from where the order was sent.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="OrganisationNumber" type="tns:OrganisationNumberType"/>
	<xs:simpleType name="OrganisationNumberType">
		<xs:annotation>
			<xs:documentation>
				Identification of an organization that distributes services contained
				in Petra.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
	        <xs:maxLength value="10"/>
	        <xs:pattern value="([0-9])*"/>
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== P ============================ -->

	<xs:element name="Partner" type="tns:PartnerType"/>
	<xs:simpleType name="PartnerType">
		<xs:annotation>
			<xs:documentation>
				Partner company.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="9999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PassengerAge" type="tns:PassengerAgeType" />
	<xs:simpleType name="PassengerAgeType">
		<xs:annotation>
			<xs:documentation>
				Travelers age in years.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt" />
	</xs:simpleType>

    <xs:element name="PassengerCategory" type="tns:PassengerCategoryType" />
	<xs:complexType name="PassengerCategoryType">
		<xs:annotation>
			<xs:documentation>
				Kind of traveler; adult, youth, children, animals etc.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
		<xs:restriction base="tns:StringCodeType">
		    <xs:maxLength value="2" />
		</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="PassengerCategory1" type="tns:PassengerCategoryType">
		<xs:annotation>
			<xs:documentation>
				Passenger of category 1.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="PassengerCategory2" type="tns:PassengerCategoryType">
		<xs:annotation>
			<xs:documentation>
				Passenger of category 2.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="PassengerCategory3" type="tns:PassengerCategoryType">
		<xs:annotation>
			<xs:documentation>
				Passenger of category 3.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="PassengerCategory4" type="tns:PassengerCategoryType">
		<xs:annotation>
			<xs:documentation>
				Passenger of category 4.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="PassportMandatory" type="tns:PassportMandatoryType"/>
	<xs:simpleType name="PassportMandatoryType">
		<xs:annotation>
			<xs:documentation>
				Customer passport number required.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						Undefined (implicit No).
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="J">
				<xs:annotation>
					<xs:documentation>
						Yes.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>
						No.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PassportNumber" type="tns:PassportNumberType"/>
	<xs:simpleType name="PassportNumberType">
		<xs:annotation>
			<xs:documentation>
				Customer passport number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PayerCustomerId" type="tns:CustomerIdType">
		<xs:annotation>
			<xs:documentation>
				Paying customer id.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="PaymentCardReference" type="tns:PaymentCardReferenceType" />
	<xs:simpleType name="PaymentCardReferenceType">
		<xs:annotation>
			<xs:documentation>
				Credit card payment reference.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PaymentCardSystem" type="tns:PaymentCardSystemType" />
	<xs:simpleType name="PaymentCardSystemType">
		<xs:annotation>
			<xs:documentation>
				Payment credit card system.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PaymentCashJournalId" type="tns:CashJournalIdType">
		<xs:annotation>
			<xs:documentation>
				Uniquely identifies a payment bill per selling point.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="PaymentCode" type="tns:PaymentCodeType" />
	<xs:complexType name="PaymentCodeType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
				Describes how the customer has paid.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="PaymentDateTime" type="tns:PaymentDateTimeType" />
	<xs:simpleType name="PaymentDateTimeType">
		<xs:annotation>
			<xs:documentation>
				Specifies the date/time the payment was made.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="PaymentDate" type="tns:PaymentDateType" />
	<xs:simpleType name="PaymentDateType">
		<xs:annotation>
			<xs:documentation>
				Specifies the date payment was made.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="PaymentFreeText" type="tns:PaymentFreeTextType" />
	<xs:simpleType name="PaymentFreeTextType">
		<xs:annotation>
			<xs:documentation>
				Payment free text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="16" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PaymentStatus" type="tns:PaymentStatusCodeType" />
	<xs:simpleType name="PaymentStatusType">
		<xs:annotation>
			<xs:appinfo>
				<jaxb:typesafeEnumClass name="PaymentStatusType">
					<jaxb:typesafeEnumMember name="BLANK" value="" />
				</jaxb:typesafeEnumClass>
			</xs:appinfo>
			<xs:documentation>
				Describes the payment status the order is in.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
			<xs:enumeration value="B">
				<xs:annotation>
					<xs:documentation>
						Paid
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="D">
				<xs:annotation>
					<xs:documentation>
						Partially paid
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="F">
				<xs:annotation>
					<xs:documentation>
						Forfeit
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="K">
				<xs:annotation>
					<xs:documentation>
						Credited
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="M">
				<xs:annotation>
					<xs:documentation>
						Cancelled
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="O">
				<xs:annotation>
					<xs:documentation>
						Unpaid
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="">
				<xs:annotation>
					<xs:documentation>
						Unpaid
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="X">
				<xs:annotation>
					<xs:documentation>
						Exchanged
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="V">
				<xs:annotation>
					<xs:documentation>
						Voucher
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="PaymentStatusCodeType">
		<xs:annotation>
			<xs:documentation>
				Describes the payment status the order is in.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="tns:PaymentStatusType">
				<xs:attributeGroup ref="tns:PlaintextAttribute" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="PaymentTerminalId" type="tns:TerminalIdType">
		<xs:annotation>
			<xs:documentation>
				Id of the terminal where payment has been registered.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="PaymentUserId" type="tns:PaymentUserIdType" />
	<xs:simpleType name="PaymentUserIdType">
		<xs:annotation>
			<xs:documentation>
				Identifies the user that issued the payment.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PayerAccount" type="tns:PayerAccountType" />
	<xs:simpleType name="PayerAccountType">
		<xs:annotation>
			<xs:documentation>
				Cost Center.
				Information for trains account customers. Not used by sales system.
    		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PayerAccountMandatory" type="tns:PayerAccountMandatoryType" />
	<xs:simpleType name="PayerAccountMandatoryType">
		<xs:annotation>
			<xs:documentation>
				Cost Center compulsory.
    		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PaymentConditions" type="tns:PaymentConditionsType"/>
	<xs:simpleType name="PaymentConditionsType">
		<xs:annotation>
			<xs:documentation>
				Payment conditions.
    		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PaymentNetAmount" type="tns:PaymentNetAmountType" />
	<xs:simpleType name="PaymentNetAmountType">
		<xs:annotation>
			<xs:documentation>
				The excess amount paid.
			</xs:documentation>
		</xs:annotation>
		 <xs:restriction base="xs:decimal">
		 	<xs:totalDigits value="14"/>
		 	<xs:fractionDigits value="2"/>
		 </xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerCountryNumber" type="tns:CustomerCountryNumberType" />
	<xs:simpleType name="CustomerCountryNumberType">
		<xs:annotation>
			<xs:documentation>
				Phone country number, such as +46 for Sweden.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerAreaNumber" type="tns:CustomerAreaNumberType" />
	<xs:simpleType name="CustomerAreaNumberType">
		<xs:annotation>
			<xs:documentation>
				Phone area number, such as 08 for Stockholm.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CustomerSubscriberNumber" type="tns:CustomerSubscriberNumberType" />
	<xs:simpleType name="CustomerSubscriberNumberType">
		<xs:annotation>
			<xs:documentation>
				Phone subscriber number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="8"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PaymentModel" type="tns:PaymentModelType" />
        <xs:simpleType name="PaymentModelType">
        <xs:annotation>
            <xs:documentation>
                Payment model.
                Indicates if certain conditions applies to debit/credit or distribution.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="2"/>
        </xs:restriction>
        </xs:simpleType>
        
    <xs:element name="PaymentModelCode" type="tns:PaymentModelCodeType" />
        <xs:simpleType name="PaymentModelCodeType">
		<xs:annotation>
			<xs:documentation>
				A payment model code is used during payment.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3"/>
		</xs:restriction>
        </xs:simpleType>

	<xs:element name="PinCode" type="tns:PinCodeType" />
	<xs:simpleType name="PinCodeType">
		<xs:annotation>
			<xs:documentation>
				Personal identification code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PlacementCharacteristics" type="tns:PlacementCharacteristicsType" />
	<xs:complexType name="PlacementCharacteristicsType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					PlacementCharacteristics indicates the placement type.
				</xs:documentation>
			</xs:annotation>
		<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="PlacementDeviation" type="tns:PlacementDeviationType" />
	<xs:simpleType name="PlacementDeviationType">
		<xs:annotation>
		<xs:appinfo>
				<jaxb:typesafeEnumClass name="PlacementDeviationType">
					<jaxb:typesafeEnumMember name="AVVIKELSE_1VAGN" value="1" />
					<jaxb:typesafeEnumMember name="AVVIKELSE_2VAGN" value="2" />
				</jaxb:typesafeEnumClass>
			</xs:appinfo>
			<xs:documentation>
				Placement deviation property.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="1">
				<xs:annotation>
					<xs:documentation>
						Deviation within carriage in booking.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2">
				<xs:annotation>
					<xs:documentation>
						Carriage deviation in booking.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PlacementDirection" type="tns:PlacementDirectionCodeType" />
	<xs:simpleType name="PlacementDirectionType">
		<xs:annotation>
			<xs:appinfo>
				<jaxb:typesafeEnumClass name="PlacementDirectionType">
					<jaxb:typesafeEnumMember name="BLANK" value=" " />
				</jaxb:typesafeEnumClass>
			</xs:appinfo>
			<xs:documentation>
				Indicates direction of the placement (undefined, forward, backward)
				N.B. Placementdirection is not used at the moment, it is not possible choose at booking.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						Undefined
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="B">
			<xs:annotation>
					<xs:documentation>
						Backward placement
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="F">
				<xs:annotation>
					<xs:documentation>
						Forward placement
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="PlacementDirectionCodeType">
		<xs:annotation>
			<xs:documentation>
				Indicates direction of the placement (undefined, forward, backward).
				N.B. Placementdirection is not used at the moment, it is not possible choose at booking.
				Code/clear-text type. The value is the code (of enum type) and the plaintext attribute is the clear-text (of string type).
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="tns:PlacementDirectionType">
				<xs:attributeGroup ref="tns:PlaintextAttribute" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="PlacementNumber" type="tns:PlacementNumberType"/>
	<xs:simpleType name="PlacementNumberType">
		<xs:annotation>
			<xs:documentation>
				Placement number, i.e. seat number in a carriage.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="5"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PlacementNumberInterval" type="tns:PlacementNumberIntervalType"/>
	<xs:simpleType name="PlacementNumberIntervalType">
		<xs:annotation>
			<xs:documentation>
				Marks that a placement number includes an interval.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PlacementNumberType" type="tns:PlacementNumberTypeType"/>
	<xs:simpleType name="PlacementNumberTypeType">
		<xs:annotation>
			<xs:documentation>
				Placement number type.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PlacementOrientation" type="tns:PlacementOrientationType" />
	<xs:complexType name="PlacementOrientationType">
		<xs:annotation>
			<xs:documentation>
				Specifies the relative orientation of the placement.
				Range of values include:
				OD: undefined (non-specific)
				F : windows
				G : aisle
				ML: between
				OV: above
				UN: under
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="PlacementSequenceNumber" type="tns:PlacementSequenceNumberType" />
	<xs:simpleType name="PlacementSequenceNumberType">
		<xs:annotation>
			<xs:documentation>
				Identifies a booking belonging to a certain order item.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PlacementType" type="tns:PlacementTypeType" />
	<xs:simpleType name="PlacementTypeType">
		<xs:annotation>
			<xs:documentation>
				Specifies the types of placements for which a particular car
				group/supply route is bookable
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PreliminaryPaymentCode" type="tns:PreliminaryPaymentCodeType" />
	<xs:simpleType name="PreliminaryPaymentCodeType">
		<xs:annotation>
			<xs:documentation>
				Preliminary payment code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="PrintOutCode" type="tns:PrintOutCodeType" />
	<xs:simpleType name="PrintOutCodeType">
		<xs:annotation>
			<xs:documentation>
				Print out code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Points" type="tns:PointsType" />
	<xs:simpleType name="PointsType">
		<xs:annotation>
			<xs:documentation>
				Number of points the service/component costs in KPS.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="********" />
		</xs:restriction>
	</xs:simpleType>

	<xs:simpleType name="PositiveInt">
		<xs:restriction base="xs:int">
			<xs:minInclusive value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PriceGroupCode" type="tns:PriceGroupCodeType" />
	<xs:complexType name="PriceGroupCodeType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					Price group.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:minLength value="1" />
				<xs:maxLength value="6" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="PriceGroupStatusCode" type="tns:PriceGroupStatusCodeType" />
	<xs:simpleType name="PriceGroupStatusCodeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OK">
				<xs:annotation>
					<xs:documentation>
						Okay
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
        	<xs:enumeration value="NOK">
        		<xs:annotation>
        			<xs:documentation>
        				Not okay
        			</xs:documentation>
        		</xs:annotation>
        	</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PriceLevelCode" type="tns:PriceLevelCodeType" />
	<xs:complexType name="PriceLevelCodeType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					Price level code.
					A price level describes different salable variants of a certain
					booking class. In a sense it describes not only the comfortability
					but also what you get out, in terms of quality, of a service.
					One example are the two price levels 1 class food onboard and 1 class
					are both of the same booking class; FK.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="PriceRecalculateFactor" type="tns:PriceRecalculateFactorType" />
	<xs:simpleType name="PriceRecalculateFactorType">
		<xs:annotation>
			<xs:documentation>
				Price recalculate factor.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:decimal">
			<xs:totalDigits value="7"/>
			<xs:fractionDigits value="4"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PriceRecalculateModel" type="tns:PriceRecalculateModelType" />
	<xs:simpleType name="PriceRecalculateModelType">
		<xs:annotation>
			<xs:documentation>
				Price recalculate model.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="-1"/>
 			<xs:maxInclusive value="9999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="PriorityOrder" type="tns:PriorityOrderType" />
	<xs:simpleType name="PriorityOrderType">
		<xs:annotation>
			<xs:documentation>
				Priority order.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt">
 			<xs:maxInclusive value="99999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="OrderProducerCode" type="tns:ProducerCodeType">
		<xs:annotation>
			<xs:documentation>
				Identification code of the producer that placed the order.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="ProducerCode" type="tns:ProducerCodeType" />
	<xs:simpleType name="ProducerCodeType">
		<xs:annotation>
			<xs:documentation>
				Identification code of a producer.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ProductCode" type="tns:ProductCodeType" />
	<xs:complexType name="ProductCodeType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					Product designation of a mean of conveyance that describes its look, comfort and service in a general way.
					ProductCode should be sent in as it is returned from the method getTimetable.  
					Please note that a blank value ('') is also a valid code and should be sent. The meaning of the blank code is "Other".
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="3" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="ProductionDate" type="tns:ProductionDateType"/>
	<xs:simpleType name="ProductionDateType">
		<xs:annotation>
			<xs:documentation>
				Production date. First date when a row is used in production in the system,
				usually the same as FromDate.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

    <xs:simpleType name="ProfileCategoryCodeType">
        <xs:annotation>
            <xs:documentation>
            	The Profile Category Code.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"></xs:minLength>
            <xs:maxLength value="10" />
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="PromotionCode" type="tns:PromotionCodeType" />
	<xs:simpleType name="PromotionCodeType">
		<xs:annotation>
			<xs:documentation>
				Promotion code identifying a promotion.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ProposedRetrievalDate" type="tns:ProposedRetrievalDateType" />
	<xs:simpleType name="ProposedRetrievalDateType">
		<xs:annotation>
			<xs:documentation>
				The proposed date of order printing/retrieval.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<!-- =============================== Q ============================ -->

	<!-- =============================== R ============================ -->

	<xs:element name="ReceiptNumber" type="tns:ReceiptNumberType" />
	<xs:simpleType name="ReceiptNumberType">
		<xs:annotation>
			<xs:documentation>
				Receipt report number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="999999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ReferenceNumber" type="tns:ReferenceNumberType" />
	<xs:simpleType name="ReferenceNumberType">
		<xs:annotation>
			<xs:documentation>
				Referencenumber for a reservation made in a reservationsystem or some
				other free-text information of a service, such as a groupname or a car
				license number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="RegionName" type="tns:RegionNameType"/>
	<xs:simpleType name="RegionNameType">
		<xs:annotation>
			<xs:documentation>
				Customer region name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="RegisterDate" type="tns:RegisterDateType"/>
	<xs:simpleType name="RegisterDateType">
		<xs:annotation>
			<xs:documentation>
				Posting date.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="RegistrationNumber" type="tns:RegistrationNumberType"/>
	<xs:simpleType name="RegistrationNumberType">
		<xs:annotation>
			<xs:documentation>
				Registration number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="RelationPriceContract" type="tns:EmptyType">
		<xs:annotation>
			<xs:documentation>
				Relational price is used in accordance to new contract.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="ReprintDateTime" type="tns:ReprintDateTimeType"/>
	<xs:simpleType name="ReprintDateTimeType">
		<xs:annotation>
			<xs:documentation>
				Print date/time.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="RequestId" type="tns:RequestIdType" />
	<xs:simpleType name="RequestIdType">
		<xs:annotation>
			<xs:documentation>
				Identifies a specific request from a sales terminal.
				Used as a middle-ware reference and between sales functions and
				suplementary systems. The exact reconstruction of this key shall
				be considered as known only to the function who creates it.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="RequisitionId" type="tns:RequisitionIdType" />
	<xs:simpleType name="RequisitionIdType">
		<xs:annotation>
			<xs:documentation>
				Requisition id/number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="999999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="RequisitionRequirement" type="tns:RequisitionRequirementType" />
	<xs:simpleType name="RequisitionRequirementType">
		<xs:annotation>
			<xs:documentation>
				Traveler Identification Requirements (Only registered travelers/Name Only/No requirement)
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="RestrictionCodeCredit" type="tns:RestrictionCodeCreditType" />
	<xs:simpleType name="RestrictionCodeCreditType">
		<xs:annotation>
			<xs:documentation>
				Credit freeze code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="RestrictionCodeCreditDate" type="tns:RestrictionCodeCreditDateType" />
	<xs:simpleType name="RestrictionCodeCreditDateType">
		<xs:annotation>
			<xs:documentation>
				Credit freeze date.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>
	
	<xs:element name="RestrictionType" type="tns:RestrictionTypeType" />
	<xs:simpleType name="RestrictionTypeType">
		<xs:annotation>
			<xs:documentation>
				Type of restriction.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="RetrievalDateTime" type="tns:RetrievalDateTimeType" />
	<xs:simpleType name="RetrievalDateTimeType">
		<xs:annotation>
			<xs:documentation>
				The date/time the order was printed.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="RetrievalDate" type="tns:RetrievalDateType" />
	<xs:simpleType name="RetrievalDateType">
		<xs:annotation>
			<xs:documentation>
				The date the order was printed.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="RoomTypeDescription" type="tns:RoomTypeDescriptionType" />
	<xs:simpleType name="RoomTypeDescriptionType">
		<xs:annotation>
			<xs:documentation>
				Room type description.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="RowMovedFromOrderId" type="tns:OrderIdType">
		<xs:annotation>
			<xs:documentation>
				The order from which this row has been moved.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="RowMovedToOrderId" type="tns:OrderIdType">
		<xs:annotation>
			<xs:documentation>
				The order to which this row has been moved.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<!-- =============================== S ============================ -->

	<xs:element name="SalesOrderSequenceNumber" type="tns:SalesOrderSequenceNumberType" />
	<xs:simpleType name="SalesOrderSequenceNumberType">
	  <xs:annotation>
	    <xs:documentation>
	    	Sequence number for the order in the order chain.
	    </xs:documentation>
	  </xs:annotation>
	  <xs:restriction base="xs:int">
	    <xs:minInclusive value="0" />
	     <xs:maxInclusive value="99999" />
	  </xs:restriction>
	</xs:simpleType>

	<xs:element name="SeatAvailability" type="tns:SeatAvailabilityType" />
	<xs:simpleType name="SeatAvailabilityType">
		<xs:annotation>
			<xs:documentation>
				Total available seats for a saleable product.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt">
 			<xs:maxInclusive value="99" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SeatAvailability1" type="tns:SeatAvailabilityType">
		<xs:annotation>
			<xs:documentation>
				Available seats in 1st class.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="SeatAvailability2" type="tns:SeatAvailabilityType">
		<xs:annotation>
			<xs:documentation>
				Not used at the moment.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="SeatAvailability3" type="tns:SeatAvailabilityType">
		<xs:annotation>
			<xs:documentation>
				Available seats in 2nd class.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="SeatAvailability4" type="tns:SeatAvailabilityType">
		<xs:annotation>
			<xs:documentation>
				Available beds in coupe.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="SeatAvailability5" type="tns:SeatAvailabilityType">
		<xs:annotation>
			<xs:documentation>
				Available beds on night train.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="SeatAvailability6" type="tns:SeatAvailabilityType">
		<xs:annotation>
			<xs:documentation>
				Available resting places on night train.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="SeatAvailabilityGroupDetail" type="tns:SeatAvailabilityGroupDetailType" />
	<xs:complexType name="SeatAvailabilityGroupDetailType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					Group code for detailed seat availability.

					Examples of detailed seat availability groups are:
					1 kl, 2 kl, Sov wc 1-2, etc.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="1" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="SeatAvailabilityGroupSummary" type="tns:SeatAvailabilityGroupSummaryType" />
	<xs:complexType name="SeatAvailabilityGroupSummaryType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					Group code for summarized seat availability.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="1" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="SalesCategoryBookingClassCode" type="tns:SalesCategoryBookingClassCodeType" />
	<xs:complexType name="SalesCategoryBookingClassCodeType">
		<xs:annotation>
			<xs:documentation>
				Booking class code part of a SalesCategory.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="6" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="SalesCategoryFlexibilityCode" type="tns:SalesCategoryFlexibilityCodeType" />
	<xs:complexType name="SalesCategoryFlexibilityCodeType">
		<xs:annotation>
			<xs:documentation>
				Flexibility code part of a SalesCategory.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="6" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="SalesProducer" type="tns:SalesProducerType" />
	<xs:simpleType name="SalesProducerType">
		<xs:annotation>
			<xs:documentation>
				Identification code of a distributing producer (ticketing).
				Is also serves the purpose to identify calling sales systems.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

    <xs:element name="SaleStatus" type="tns:SalesStatusType" />
    <xs:complexType name="SalesStatusType">
        <xs:simpleContent>
            <xs:annotation>
                <xs:documentation>
                	Status of the fare counter, if it is sellable or not.
                </xs:documentation>
            </xs:annotation>
            <xs:restriction base="tns:StringCodeType">
                <xs:maxLength value="1" />
            </xs:restriction>
        </xs:simpleContent>
    </xs:complexType>

    <xs:element name="SalesUnitKeyApplicationName" type="tns:SalesUnitKeyApplicationNameType" />
    <xs:simpleType name="SalesUnitKeyApplicationNameType">
        <xs:annotation>
            <xs:documentation>
                Application name for sales unit key.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="30" />
        </xs:restriction>
    </xs:simpleType>
 
	<xs:element name="SalesUnitOwner" type="tns:SalesUnitOwnerType" />
	<xs:simpleType name="SalesUnitOwnerType">
		<xs:annotation>
			<xs:documentation>
				Identifies the sales unit currently owning the order. Only the owner can read and update the order.
				Format: Any combination of characters in the ranges [A-Z] and [0-9].
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SalesUnitOwnerNumber" type="tns:SalesUnitOwnerNumberType"/>
	<xs:simpleType name="SalesUnitOwnerNumberType">
		<xs:annotation>
			<xs:documentation>
				Ownership of the sales point unit number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SalesUnitCategory" type="tns:SalesUnitCategoryType" />
	<xs:simpleType name="SalesUnitCategoryType">
		<xs:annotation>
			<xs:documentation>
				Categorizes outlets with regard to allowable cash, repurchase and
				accounting rules.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SalesUnitId" type="tns:SalesUnitIdType" />
	<xs:simpleType name="SalesUnitIdType">
		<xs:annotation>
			<xs:documentation>
				Identification of a sales unit.
				Format: Any combination of characters in the ranges [A-Z] and [0-9].
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SalesUnitName" type="tns:SalesUnitNameType" />
	<xs:simpleType name="SalesUnitNameType">
		<xs:annotation>
			<xs:documentation>
				Name of a sales unit.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SalesUnitSectionId" type="tns:SalesUnitSectionIdType" />
	<xs:simpleType name="SalesUnitSectionIdType">
		<xs:annotation>
			<xs:documentation>
				Identification of a section under a sales unit.
				Format: A value in the range 00-99.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SalesUnitSectionName" type="tns:SalesUnitSectionNameType" />
	<xs:simpleType name="SalesUnitSectionNameType">
		<xs:annotation>
			<xs:documentation>
				Name of a sales unit section.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Salutation" type="tns:SalutationType"/>
	<xs:simpleType name="SalutationType">
		<xs:annotation>
			<xs:documentation>
				Salutation, mr, mrs etc.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="SatCode" type="tns:SatCodeType"/>
	<xs:simpleType name="SatCodeType">
		<xs:annotation>
			<xs:documentation>
				RPO SAT Code.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SecrecyExist" type="tns:SecrecyExistType" />
	<xs:simpleType name="SecrecyExistType">
		<xs:annotation>
			<xs:documentation>
				Related lines exists within the Petra system.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"/>
			<xs:enumeration value="J"></xs:enumeration>
			<xs:enumeration value="N"></xs:enumeration>
			<xs:enumeration value=" "></xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SegmentProducerCode" type="tns:SegmentProducerCodeType" />
	<xs:complexType name="SegmentProducerCodeType">
		<xs:annotation>
			<xs:documentation>
				Identification of the owner of the transport segment.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:NonNegativeIntCodeType">
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="SegmentReference" type="tns:SegmentReferenceType" />
	<xs:simpleType name="SegmentReferenceType">
		<xs:annotation>
			<xs:documentation>
				The segment reference uniquely identifies a segment among
				a set of segment within the same itinerary.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="99" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SubsegmentSequenceNumber" type="tns:SubsegmentSequenceNumberType" />
	<xs:simpleType name="SubsegmentSequenceNumberType">
		<xs:annotation>
			<xs:documentation>
				Subsegment sequence number.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="99" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SellerName" type="tns:SellerNameType" />
	<xs:simpleType name="SellerNameType">
		<xs:annotation>
			<xs:documentation>
				A travel agency seller's name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="8" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="SenderAddress" type="tns:SenderAddressType" />
	<xs:simpleType name="SenderAddressType">
		<xs:annotation>
			<xs:documentation>
				SMS/Email sender address.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="80"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SerialNumber" type="tns:SerialNumberType" />
	<xs:simpleType name="SerialNumberType">
		<xs:annotation>
			<xs:documentation>
				The identity of the segment of an international travel.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="99999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Service" type="tns:ServiceType" />
	<xs:simpleType name="ServiceType">
		<xs:annotation>
			<xs:documentation>
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="*********" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceBrandAbbreviation" type="tns:ServiceBrandAbbreviationType" />
	<xs:simpleType name="ServiceBrandAbbreviationType">
		<xs:annotation>
			<xs:documentation>
				Abbreviation of Service brand according to UIC 918-1 (item 83).
				Abbreviation for the train's brand, which is used in the printing of RCT2-tickets.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceBrandCode" type="tns:ServiceBrandCodeType" />
	<xs:simpleType name="ServiceBrandCodeType">
		<xs:annotation>
			<xs:documentation>
				Service brand code according to UIC 918-1 (item 82). A code for the train brand.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceBrandName" type="tns:ServiceBrandNameType" />
	<xs:simpleType name="ServiceBrandNameType">
		<xs:annotation>
			<xs:documentation>
				UIC service brand name according to UIC 918-1 (item 84).
				Plain language of the train's brand, which is used in the printing of RCT2-tickets.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="33" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceId" type="tns:ServiceIdType" />
	<xs:simpleType name="ServiceIdType">
		<xs:annotation>
			<xs:documentation>
				Identifies a service available for sales in the Petra sales system.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:long">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="*********9" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceName" type="tns:ServiceNameType" />
	<xs:simpleType name="ServiceNameType">
		<xs:annotation>
			<xs:documentation>
				Identifies the name of the service available for sales in the Petra sales system. The service
				name is related to the ServiceId
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="20" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceLongName" type="tns:ServiceLongNameType" />
	<xs:simpleType name="ServiceLongNameType">
		<xs:annotation>
			<xs:documentation>
				Identifies the long name of the service available for sales in the Petra sales system. The service
				name is related to the ServiceId.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="72" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceShortName" type="tns:ServiceShortNameType" />
	<xs:simpleType name="ServiceShortNameType">
		<xs:annotation>
			<xs:documentation>
				Identifies the short name of the service available for sales in the Petra sales system.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="10" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceNationality" type="tns:ServiceNationalityType" />
	<xs:simpleType name="ServiceNationalityType">
		<xs:annotation>
			<xs:documentation>
				Specifies whether a service is Swedish or international.
				Can be used to determine whether the service is chargeable with VAT or not.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceSpecification" type="tns:ServiceSpecificationType"/>
	<xs:simpleType name="ServiceSpecificationType">
	 	<xs:annotation>
	  		<xs:documentation>
	  			Service Name
	  		</xs:documentation>
	 	</xs:annotation>
	 	<xs:restriction base="xs:string">
	  		<xs:maxLength value="64" />
	 	</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceVersion" type="tns:ServiceVersionType" />
	<xs:simpleType name="ServiceVersionType">
		<xs:annotation>
			<xs:documentation>
				Service version number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="9999" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="ServicePresaleMax" type="tns:ServicePresaleMaxType" />
	<xs:simpleType name="ServicePresaleMaxType">
		<xs:annotation>
			<xs:documentation>
				Maximum amount of time (TimeUnit) before departure the ticket has to be purchased. 
				If 0, advance purchase is not possible.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="ServicePresaleMin" type="tns:ServicePresaleMinType" />
	<xs:simpleType name="ServicePresaleMinType">
		<xs:annotation>
			<xs:documentation>
				Minimum amount of time before departure the ticket has to be purchased. 
				If 0, advance purchase is not required.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ServiceProducerCode" type="tns:ServiceProducerCodeType" />
	<xs:complexType name="ServiceProducerCodeType">
		<xs:simpleContent>
		<xs:annotation>
			<xs:documentation>
				Service producer code.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeIntCodeType">
		</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="Sex" type="tns:SexType"/>
	<xs:simpleType name="SexType">
		<xs:annotation>
			<xs:appinfo>
				<jaxb:typesafeEnumClass name="SexType">
					<jaxb:typesafeEnumMember name="BLANK" value=" " />
				</jaxb:typesafeEnumClass>
			</xs:appinfo>
			<xs:documentation>
				A person's sex/gender.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
			<xs:enumeration value="M">
				<xs:annotation>
					<xs:documentation>
						Male.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="F">
				<xs:annotation>
					<xs:documentation>
						Female.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="U">
				<xs:annotation>
					<xs:documentation>
						Unspecified/Unknown.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						Unspecified/Unknown.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Destination" type="tns:ShortCardNameType"/>
	<xs:element name="ShortCardName" type="tns:ShortCardNameType"/>
	<xs:simpleType name="ShortCardNameType">
		<xs:annotation>
			<xs:documentation>
				Credit card issuer.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Signature" type="tns:SignatureType"/>
	<xs:simpleType name="SignatureType">
		<xs:annotation>
			<xs:documentation>
				User/Seller signature.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SmokingDepartment" type="tns:SmokingDepartmentCodeType" />
	<xs:simpleType name="SmokingDepartmentType">
		<xs:annotation>
			<xs:appinfo>
				<jaxb:typesafeEnumClass name="SmokingDepartmentType">
					<jaxb:typesafeEnumMember name="BLANK" value="" />
				</jaxb:typesafeEnumClass>
			</xs:appinfo>
			<xs:documentation>
				Smoking department property.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="">
				<xs:annotation>
					<xs:documentation>
						Implicit no smoking allowed.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="IR">
				<xs:annotation>
					<xs:documentation>
						No smoking allowed.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="R">
				<xs:annotation>
					<xs:documentation>
						Smoking allowed.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="SmokingDepartmentCodeType">
		<xs:annotation>
			<xs:documentation>
				Smoking department property.

				Code/clear-text type. The value is the code (of enum type)
				and the plaintext attribute is the clear-text (of string type).
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="tns:SmokingDepartmentType">
				<xs:attributeGroup ref="tns:PlaintextAttribute" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	
	<xs:element name="SocialSecurityNumber" type="tns:SocialSecurityNumberType" />
	<xs:simpleType name="SocialSecurityNumberType">
		<xs:annotation>
			<xs:documentation>
				Swedish Personal identity number in format YYYYMMDDNNNN
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="12"></xs:maxLength>
			<xs:pattern value="([0-9])*"></xs:pattern>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SpaceProperty" type="tns:SpacePropertyType" />
	<xs:simpleType name="SpacePropertyType">
		<xs:annotation>
			<xs:documentation>
				Code describing a non-defined characteristic of a space.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SpacePropertyCode" type="tns:SpacePropertyCodeType" />
	<xs:complexType name="SpacePropertyCodeType">
		<xs:simpleContent>
		<xs:annotation>
			<xs:documentation>
				Code describing a non-defined characteristic of a space.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:StringCodeType">
			<xs:maxLength value="2"/>
		</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="StandardDiscountCode" type="tns:StandardDiscountCodeType" />
	<xs:simpleType name="StandardDiscountCodeType">
		<xs:annotation>
			<xs:documentation>
				Discount code of discount form Standard.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="Standard2DiscountCode" type="tns:Standard2DiscountCodeType" />
	<xs:simpleType name="Standard2DiscountCodeType">
		<xs:annotation>
			<xs:documentation>
				Discount code of discount form Standard 2.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="StartDate" type="tns:FromDateType">
		<xs:annotation>
			<xs:documentation>
				Start date (variant of FromDate often used for validity ranges).
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="StatisticalKilometer" type="tns:StatisticalKilometerType"/>
	<xs:simpleType name="StatisticalKilometerType">
		<xs:annotation>
			<xs:documentation>
				Distance of a relation in kilometers for statistical purposes.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="9999"/>
		</xs:restriction>
	</xs:simpleType>

    <xs:element name="SaleStatusInfo" type="tns:SaleStatusInfoType" />
    <xs:simpleType name="SaleStatusInfoType">
        <xs:annotation>
            <xs:documentation>
            	Information about the fare counter status. It could be a maintenence reference number
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:maxLength value="20" />
        </xs:restriction>
    </xs:simpleType>

	<xs:element name="StreetLongName" type="tns:StreetLongNameType"/>
	<xs:simpleType name="StreetLongNameType">
		<xs:annotation>
			<xs:documentation>
				Street name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="70" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="StreetName" type="tns:StreetNameType"/>
	<xs:simpleType name="StreetNameType">
		<xs:annotation>
			<xs:documentation>
				Street name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="50" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="StreetNumber" type="tns:StreetNumberType"/>
	<xs:simpleType name="StreetNumberType">
		<xs:annotation>
			<xs:documentation>
				Street number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="10" />
		</xs:restriction>
	</xs:simpleType>

	<xs:complexType name="StringCodeType">
		<xs:annotation>
			<xs:documentation>
				Code/clear-text base type. The value is the code (of string type)
				and the plaintext attribute is the clear-text (of string type).
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="plaintext" type="xs:string" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	
	<xs:element name="Stylesheet" type="tns:StylesheetType" />
	<xs:simpleType name="StylesheetType">
		<xs:annotation>
			<xs:documentation>
				Template used in SMS/Email dispatches.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="255"/>
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="SubClassId" type="tns:SubClassIdType"/>
	<xs:simpleType name="SubClassIdType">
		<xs:annotation>
			<xs:documentation>
				RPO sub class id.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
			<xs:pattern value="[A-Z0-9][A-Z0-9]*" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="SuborderDate" type="tns:SuborderDateType" />
	<xs:simpleType name="SuborderDateType">
		<xs:annotation>
			<xs:documentation>
				Suborder date.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="SuborderOrderId" type="tns:OrderIdType"/>

	<xs:element name="SuborderOrderItemId" type="tns:OrderItemIdType"/>

	<!-- =============================== T ============================ -->

	<xs:element name="Telephone" type="tns:TelephoneType"/>
	<xs:simpleType name="TelephoneType">
		<xs:annotation>
			<xs:documentation>
				Telephone number.
			</xs:documentation>
      	</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="16"></xs:maxLength>
			<xs:pattern value="\+?[0-9\*\-\s\\/]*"></xs:pattern>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TemplateType" type="tns:TemplateTypeType"/>
	<xs:simpleType name="TemplateTypeType">
		<xs:annotation>
			<xs:documentation>
				Template.
			</xs:documentation>
      	</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>

    <xs:element name="TariffUnit" type="tns:TariffUnitType" />
    <xs:simpleType name="TariffUnitType">
        <xs:annotation>
            <xs:documentation>
                Tariff unit.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
             <xs:maxInclusive value="*********"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="SalesUnitKey" type="tns:TerminalIdType" />
	<xs:element name="TerminalId" type="tns:TerminalIdType" />
	<xs:simpleType name="TerminalIdType">
		<xs:annotation>
			<xs:documentation>
				Id of a terminal.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="8" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TicketBarCode" type="tns:TicketBarCodeType" />
	<xs:simpleType name="TicketBarCodeType">
		<xs:annotation>
			<xs:documentation>
				Ticket barcode.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TicketCode" type="tns:TicketCodeType" />
	<xs:simpleType name="TicketCodeType">
		<xs:annotation>
			<xs:documentation>
				Ticket code in readable form for ticketless travel.
				Allowed characters: ABCDEFGHJKLMNPQRTUVXYZAAAEOE1234567.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="13" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TicketNumber" type="tns:TicketNumberType" />
	<xs:simpleType name="TicketNumberType">
		<xs:annotation>
			<xs:documentation>
				Ticket number.

				Format of national tickets: Pos 1-8=order number, pos 9-12=order item number.
				Example: ACF8477G0001

				Format of international tickets: Pos 1-2=producer number, pos 3-12 sequence number.
				Example: 800000000050 
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="12" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TicketTeared" type="tns:TicketTearedType" />
	<xs:simpleType name="TicketTearedType">
		<xs:annotation>
			<xs:documentation>
				The order item is electronically teared (authenticated).
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						The order item is not electronically teared.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>
						The order item is not electronically teared.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="J">
				<xs:annotation>
					<xs:documentation>
						The order item is electronically teared.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TicketTearedDateTime" type="tns:TicketTearedDateTimeType" />
	<xs:simpleType name="TicketTearedDateTimeType">
		<xs:annotation>
			<xs:documentation>
				The date/time the ticket was teared.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="TicketTearedUserId" type="tns:TicketTearedUserIdType" />
	<xs:simpleType name="TicketTearedUserIdType">
		<xs:annotation>
			<xs:documentation>
				The user id that authenticated the ticket.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TicketTearedLocationId" type="tns:TicketTearedLocationIdType" />
	<xs:simpleType name="TicketTearedLocationIdType">
		<xs:annotation>
			<xs:documentation>
				The the location where the ticket was autenticated.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TicketType" type="tns:TicketTypeType" />
	<xs:complexType name="TicketTypeType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
				   Identifies a ticket type.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="5" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="TicketUntearedDateTime" type="tns:TicketUntearedDateTimeType" />
	<xs:simpleType name="TicketUntearedDateTimeType">
		<xs:annotation>
			<xs:documentation>
				The date/time the ticket was unteared.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="TicketUntearedUserId" type="tns:TicketUntearedUserIdType" />
	<xs:simpleType name="TicketUntearedUserIdType">
		<xs:annotation>
			<xs:documentation>
				The user id that unteared the ticket.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="TimeUnit" type="tns:TimeUnitType" />
	<xs:simpleType name="TimeUnitType">
		<xs:annotation>
			<xs:documentation>
				Timeunit for presale period or price (hourly/monthly/dayly)
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TicketUntearedLocationId" type="tns:TicketUntearedLocationIdType" />
	<xs:simpleType name="TicketUntearedLocationIdType">
		<xs:annotation>
			<xs:documentation>
				The the location where the ticket was unteared.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="4" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="Title" type="tns:TitleType" />
	<xs:simpleType name="TitleType">
		<xs:annotation>
			<xs:documentation>
				Name, title, or heading to an electronic message, 
				such as subject in an e-mail or file name of an XML message.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="254" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TotalNumberOfTickets" type="tns:TotalNumberOfTicketsType" />
	<xs:simpleType name="TotalNumberOfTicketsType">
		<xs:annotation>
			<xs:documentation>
				Multi ride ticket amount, i.e. number of available tickets.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt"/>
	</xs:simpleType>

	<xs:element name="ToDate" type="tns:ToDateType"/>
	<xs:simpleType name="ToDateType">
		<xs:annotation>
			<xs:documentation>
				To date.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>
	
	<xs:element name="ToDateTime" type="tns:ToDateTimeType"/>
	<xs:simpleType name="ToDateTimeType">
		<xs:annotation>
			<xs:documentation>
				To date/time.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime"/>
	</xs:simpleType>

	<xs:element name="ToFareZone" type="tns:ToFareZoneType"/>
	<xs:simpleType name="ToFareZoneType">
		<xs:annotation>
			<xs:documentation>
				Specifies the to zone a relational concerns.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="99999"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ToTicketStockNumber" type="tns:ToTicketStockNumberType"/>
	<xs:simpleType name="ToTicketStockNumberType">
		<xs:annotation>
			<xs:documentation>
				To ticket stock number.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
 			<xs:maxInclusive value="********"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TransportId" type="tns:TransportIdType" />
	<xs:simpleType name="TransportIdType">
		<xs:annotation>
			<xs:documentation>
				Transportation service identification, e.g. train number or bus line.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="5"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TransportInformation" type="tns:TransportInformationType" />
	<xs:complexType name="TransportInformationType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					Transport attribute code from timetable (JP).
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="4" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="TransportName" type="tns:TransportNameType" />
	<xs:simpleType name="TransportNameType">
		<xs:annotation>
			<xs:documentation>
				Transportation name.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="22"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TransportSegmentsOrigin" type="tns:TransportSegmentsOriginType"/>
	<xs:simpleType name="TransportSegmentsOriginType">
		<xs:annotation>
			<xs:documentation>
				Indicates origin of segments.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="T">
				<xs:annotation>
					<xs:documentation>
						Segment originates from timetable search.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="V">
				<xs:annotation>
					<xs:documentation>
						Segment originates from timetable search with via search.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="X">
				<xs:annotation>
					<xs:documentation>
						Segment does not originate from timetable search.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TravelDocumentCode" type="tns:TravelDocumentCodeType"/>
	<xs:simpleType name="TravelDocumentCodeType">
		<xs:annotation>
			<xs:documentation>
				Document rule which governes if and how fares and reservations are ordered
				and stored in the order database. 

				It is normally only necessary to specify TravelDocumentCode when booking itineraries outside Sweden.
				TravelDocumentCode values;

				G - Journey and a seat reservation. Common documents. This is default.
				S - Journey and a seat reservation. Separate documents.
				B - Journey and a seat reservation. System decides whether common or separate documents.
				F - Only journey.
				P - Only seat reservation.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="B">
				<xs:annotation>
					<xs:documentation>
						Fare + reservation, the system decides the partitioning.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="F">
				<xs:annotation>
					<xs:documentation>
						Fare only.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="G">
				<xs:annotation>
					<xs:documentation>
						Joint Document (fare + reservation).
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="P">
				<xs:annotation>
					<xs:documentation>
						Reservation only.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="S">
				<xs:annotation>
					<xs:documentation>
						Separate document (fare + reservation).
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TravelLinkOrder" type="tns:TravelLinkOrderType" />
	<xs:simpleType name="TravelLinkOrderType">
		<xs:annotation>
			<xs:documentation>
				TravelLinkOrder/Lock-debit.

				Indicates whether or not an order is locked for payment because
				the order has notes that will result in additional order rows.
				Also used for identifying Aergo-orders when printed.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt"/>
	</xs:simpleType>

	<xs:element name="TravellerCustomerId" type="tns:CustomerIdType">
		<xs:annotation>
			<xs:documentation>
				Traveller customer number.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

	<xs:element name="TravellerFreeText" type="tns:TravellerFreeTextType" />
	<xs:simpleType name="TravellerFreeTextType">
		<xs:annotation>
			<xs:documentation>
				Traveller free text.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="35" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TravellerMandatory" type="tns:TravellerMandatoryType" />
	<xs:simpleType name="TravellerMandatoryType">
		<xs:annotation>
			<xs:documentation>
				Type of requirement for Traveler coupling.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value=" ">
				<xs:annotation>
					<xs:documentation>
						Undefined.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="J">
				<xs:annotation>
					<xs:documentation>
						Frequent or temporary traveller is mandatory
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="F">
				<xs:annotation>
					<xs:documentation>
						Frequent traveller is mandatory
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="P">
				<xs:annotation>
					<xs:documentation>
						Frequent or temporary traveller is mandatory
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>
						Frequent or temporary traveller is not mandatory
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="E">
				<xs:annotation>
					<xs:documentation>
						E-mail or phone number is mandatory
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="TravellerNumber" type="tns:TravellerNumberType" />
	<xs:simpleType name="TravellerNumberType">
		<xs:annotation>
			<xs:documentation>
				Traveller number, a per sales order id unique number identifying a 
				particular traveller.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="1" />
			<xs:maxInclusive value="9999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TravellerRestrictionCode" type="tns:TravellerRestrictionCodeType" />
	<xs:simpleType name="TravellerRestrictionCodeType">
		<xs:annotation>
			<xs:documentation>
				Traveller restriction code, indicating that specific traveller (often customer) restrictions may apply.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="CompanyRestrictionCode" type="tns:CompanyRestrictionCodeType" />
	<xs:simpleType name="CompanyRestrictionCodeType">
		<xs:annotation>
			<xs:documentation>
				Company restriction code, indicating that specific company (often customer) restrictions may apply.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="1" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TravellerSequenceNumber" type="tns:TravellerSequenceNumberType" />
	<xs:simpleType name="TravellerSequenceNumberType">
		<xs:annotation>
			<xs:documentation>
				Traveller sequence number.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TravelDate" type="tns:TravelDateType"/>
	<xs:simpleType name="TravelDateType">
		<xs:annotation>
			<xs:documentation>
				Requested travel date for a service.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="TravelMethodCode" type="tns:TravelMethodCodeType" />
	<xs:complexType name="TravelMethodCodeType">
		<xs:simpleContent>
			<xs:annotation>
				<xs:documentation>
					Brief description of a certain mean of conveyance.
				</xs:documentation>
			</xs:annotation>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="2" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="TravelRoute" type="tns:TravelRouteType" />
	<xs:simpleType name="TravelRouteType">
		<xs:annotation>
			<xs:documentation>
				The identification of a specified path between the from and to station
				ordered by priority.
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TravelType" type="tns:TravelTypeType" />
	<xs:complexType name="TravelTypeType">
		<xs:annotation>
			<xs:documentation>
				Specifies if a particular transport service is simple or return.
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
			    <xs:maxLength value="1" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="TypeOfCommunication" type="tns:TypeOfCommunicationType" />
    <xs:simpleType name="TypeOfCommunicationType">
    	<xs:annotation>
    		<xs:documentation>
    			Preferred methods of communication (T = Tel, E = Email, P = Post)
    		</xs:documentation>
    	</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="1"></xs:length>
			<xs:enumeration value="T"></xs:enumeration>
			<xs:enumeration value="E"></xs:enumeration>
			<xs:enumeration value="P"></xs:enumeration>
			<xs:enumeration value=" "></xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="TypeOfSpecification" type="tns:TypeOfSpecificationType" />
    <xs:simpleType name="TypeOfSpecificationType">
    	<xs:annotation>
    		<xs:documentation>
    			Type of specification
    		</xs:documentation>
    	</xs:annotation>
		<xs:restriction base="xs:string">
		</xs:restriction>
	</xs:simpleType>

	<!-- =============================== U ============================ -->

	<xs:element name="UnlockUserAccountPassword" type="tns:UnlockUserAccountPasswordType"/>
	<xs:simpleType name="UnlockUserAccountPasswordType">
		<xs:annotation>
			<xs:documentation>
				Temporary password for unlocking a user account.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="UpdateUserId" type="tns:UpdateUserIdType" />
	<xs:simpleType name="UpdateUserIdType">
		<xs:annotation>
			<xs:documentation>
				User that updated an entry in the PETRA sales systems.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="UsedForVoucherCreationDate" type="tns:UsedForVoucherCreationDateType"/>
	<xs:simpleType name="UsedForVoucherCreationDateType">
		<xs:annotation>
			<xs:documentation>
				Travel guarantee creation date.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="UsedNumberOfTickets" type="tns:UsedNumberOfTicketsType" />
	<xs:simpleType name="UsedNumberOfTicketsType">
		<xs:annotation>
			<xs:documentation>
				Used multi ride ticket amount, i.e. number of tickets used.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="tns:NonNegativeInt"/>
	</xs:simpleType>

	<xs:element name="UserAccountAlias" type="tns:UserAccountAliasType" />
	<xs:simpleType name="UserAccountAliasType">
		<xs:annotation>
			<xs:documentation>
				Alternative user identity.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="30"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="UserAccountId" type="tns:UserAccountIdType" />
	<xs:simpleType name="UserAccountIdType">
		<xs:annotation>
			<xs:documentation>
				Key to user account.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="10"></xs:maxLength>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="UserAccountPassword" type="tns:UserAccountPasswordType"/>
	<xs:simpleType name="UserAccountPasswordType">
		<xs:annotation>
			<xs:documentation>
				User account password.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="UserId" type="tns:UserIdType" />
	<xs:simpleType name="UserIdType">
		<xs:annotation>
			<xs:documentation>
				Unique identification of users of PETRA sales systems.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
		</xs:restriction>
	</xs:simpleType>

  <xs:element name="UserName" type="tns:UserNameType" />
  <xs:simpleType name="UserNameType">
    <xs:annotation>
      <xs:documentation>
              	Full user name of PETRA sales systems user.
      </xs:documentation>
    </xs:annotation>
    <xs:restriction base="xs:string">
      <xs:maxLength value="30"></xs:maxLength>
    </xs:restriction>
  </xs:simpleType>

	<!-- =============================== V ============================ -->

	<xs:element name="ValidFrom" type="tns:ValidFromType"/>
	<xs:simpleType name="ValidFromType">
		<xs:annotation>
			<xs:documentation>
				The service is valid from this date.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="ValidTo" type="tns:ValidToType"/>
	<xs:simpleType name="ValidToType">
		<xs:annotation>
			<xs:documentation>
				The service is valid to this date.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:date"/>
	</xs:simpleType>

	<xs:element name="ValidityCode" type="tns:ValidityCodeType" />
	<xs:simpleType name="ValidityCodeType">
		<xs:annotation>
			<xs:documentation>
				Validity code.
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="9999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ValidityDescription" type="tns:ValidityDescriptionType" />
	<xs:simpleType name="ValidityDescriptionType">
		<xs:annotation>
			<xs:documentation>
				Validity description.
      		</xs:documentation>
		</xs:annotation>
			<xs:restriction base="xs:string">
		      	<xs:maxLength value="35"></xs:maxLength>
			</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="ValidityTimeType" type="tns:ValidityTimeType" />
	<xs:simpleType name="ValidityTimeType">
		<xs:annotation>
			<xs:documentation>
				Code specifying how a validity time should be determined.
      		</xs:documentation>
		</xs:annotation>
			<xs:restriction base="xs:string">
		      	<xs:maxLength value="2"></xs:maxLength>
			</xs:restriction>
	</xs:simpleType>

	<xs:element name="VariantNumber" type="tns:VariantNumberType" />
	<xs:simpleType name="VariantNumberType">
		<xs:annotation>
			<xs:documentation>
				Variant number.
      </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="9999" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="VatMandatoryCode" type="tns:VatMandatoryCodeType" />
	<xs:simpleType name="VatMandatoryCodeType">
		<xs:annotation>
			<xs:documentation>
				Specifies whether the given product/service is VAT charged and with
				what tax rate.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="EM">
				<xs:annotation>
					<xs:documentation>
						Not subject to VAT.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="L1">
				<xs:annotation>
					<xs:documentation>
						Add tax rate 1.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="L2">
				<xs:annotation>
					<xs:documentation>
						Add tax rate 2.
					</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="ViaLocationSequenceNumber" type="tns:ViaLocationSequenceNumberType" />
	<xs:simpleType name="ViaLocationSequenceNumberType">
		<xs:annotation>
			<xs:documentation>
				Sequence number for via location (used in foreign bookings).
      		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:int">
			<xs:minInclusive value="0" />
 			<xs:maxInclusive value="99" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="VisitorsAddress" type="tns:VisitorsAddressType" />
	<xs:simpleType name="VisitorsAddressType">
	   <xs:annotation>
	       <xs:documentation>
	       </xs:documentation>
	   </xs:annotation>
	   <xs:restriction base="xs:string">
            <xs:maxLength value="30" />
        </xs:restriction>
	</xs:simpleType>

	<!-- =============================== W ============================ -->

	<!-- =============================== X ============================ -->

	<!-- =============================== Y ============================ -->

	<!-- =============================== Z ============================ -->

	<xs:element name="ZipCode" type="tns:ZipCodeType"/>
	<xs:simpleType name="ZipCodeType">
		<xs:annotation>
			<xs:documentation>
				Zip code
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="9" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CurrencyCode" type="tns:CurrencyCodeType"/>
	<xs:complexType name="CurrencyCodeType">
		<xs:annotation>
			<xs:documentation>
			</xs:documentation>
		</xs:annotation>
		<xs:simpleContent>
			<xs:restriction base="tns:StringCodeType">
				<xs:maxLength value="3" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>

	<xs:element name="MessageId" type="tns:MessageIdType" />
	<xs:simpleType name="MessageIdType">
		<xs:annotation>
			<xs:documentation>
				An id that is unique for every information message
				in the following
				format:

				YYbbbbbbbbSSiiC

				YY Year from PaymentDateTime
				bbbbbbbb SalesOrderId
				SS SalesOrderSequenceNr
				ii OrderItemId of the
				first order item
				C DebitCreditCode

				Exampel: 12ABC0001Q0201K
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="24" />
		</xs:restriction>
	</xs:simpleType>

	<xs:element name="CreditReasonCode" type="tns:CreditReasonCodeType"/>
	<xs:simpleType name="CreditReasonCodeType">
		<xs:annotation>
			<xs:documentation>
				Credit Reason Code
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="EventDateTime" type="tns:EventDateTimeType"/>
	<xs:simpleType name="EventDateTimeType">
		<xs:annotation>
			<xs:documentation>
				Current event(debit,credit,canel..) datetime, when a informationmessage is triggerd.
			</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:dateTime" />
	</xs:simpleType>
	
	<xs:element name="NumberOfDiscounts" type="tns:NumberOfDiscountsType"/>
	<xs:simpleType name="NumberOfDiscountsType">
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:element name="Version" type="xs:string">
		<xs:annotation>
			<xs:documentation>
				The version of the information message.
			</xs:documentation>
		</xs:annotation>
	</xs:element>

</xs:schema>
