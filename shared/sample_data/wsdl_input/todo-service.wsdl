<?xml version="1.0" encoding="UTF-8"?>
<definitions name="TodoService"
   targetNamespace="http://api.todoservice.com/wsdl"
   xmlns="http://schemas.xmlsoap.org/wsdl/"
   xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
   xmlns:tns="http://api.todoservice.com/wsdl"
   xmlns:types="http://api.todoservice.com/types"
   xmlns:xsd="http://www.w3.org/2001/XMLSchema">

   <documentation>
      Todo Service WSDL - Comprehensive todo item management service
      Provides CRUD operations for todo items, user management, and search functionality
   </documentation>

   <types>
      <xsd:schema targetNamespace="http://api.todoservice.com/wsdl"
                  xmlns:types="http://api.todoservice.com/types">
         
         <!-- Import external types schema -->
         <xsd:import namespace="http://api.todoservice.com/types" 
                     schemaLocation="todo-types.xsd"/>
         
         <!-- Request/Response wrapper types for SOAP operations -->
         
         <!-- Create Todo Request -->
         <xsd:complexType name="CreateTodoRequestType">
            <xsd:annotation>
               <xsd:documentation>Request to create a new todo item</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="todo" type="types:TodoType">
                  <xsd:annotation>
                     <xsd:documentation>Todo item to create (ID will be auto-generated)</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Create Todo Response -->
         <xsd:complexType name="CreateTodoResponseType">
            <xsd:annotation>
               <xsd:documentation>Response containing the created todo item with generated ID</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="todo" type="types:TodoType">
                  <xsd:annotation>
                     <xsd:documentation>The created todo item with assigned ID</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
               <xsd:element name="success" type="xsd:boolean">
                  <xsd:annotation>
                     <xsd:documentation>Indicates if the operation was successful</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Get Todo Request -->
         <xsd:complexType name="GetTodoRequestType">
            <xsd:annotation>
               <xsd:documentation>Request to retrieve a specific todo item by ID</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="todoId" type="xsd:long">
                  <xsd:annotation>
                     <xsd:documentation>Unique identifier of the todo item to retrieve</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Get Todo Response -->
         <xsd:complexType name="GetTodoResponseType">
            <xsd:annotation>
               <xsd:documentation>Response containing the requested todo item</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="todo" type="types:TodoType" minOccurs="0">
                  <xsd:annotation>
                     <xsd:documentation>The requested todo item (null if not found)</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
               <xsd:element name="found" type="xsd:boolean">
                  <xsd:annotation>
                     <xsd:documentation>Indicates if the todo item was found</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Update Todo Request -->
         <xsd:complexType name="UpdateTodoRequestType">
            <xsd:annotation>
               <xsd:documentation>Request to update an existing todo item</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="todo" type="types:TodoType">
                  <xsd:annotation>
                     <xsd:documentation>Todo item with updated information (ID must be provided)</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Update Todo Response -->
         <xsd:complexType name="UpdateTodoResponseType">
            <xsd:annotation>
               <xsd:documentation>Response containing the updated todo item</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="todo" type="types:TodoType" minOccurs="0">
                  <xsd:annotation>
                     <xsd:documentation>The updated todo item</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
               <xsd:element name="success" type="xsd:boolean">
                  <xsd:annotation>
                     <xsd:documentation>Indicates if the update was successful</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Delete Todo Request -->
         <xsd:complexType name="DeleteTodoRequestType">
            <xsd:annotation>
               <xsd:documentation>Request to delete a todo item by ID</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="todoId" type="xsd:long">
                  <xsd:annotation>
                     <xsd:documentation>Unique identifier of the todo item to delete</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Delete Todo Response -->
         <xsd:complexType name="DeleteTodoResponseType">
            <xsd:annotation>
               <xsd:documentation>Response indicating the result of the delete operation</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="success" type="xsd:boolean">
                  <xsd:annotation>
                     <xsd:documentation>Indicates if the deletion was successful</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
               <xsd:element name="message" type="xsd:string" minOccurs="0">
                  <xsd:annotation>
                     <xsd:documentation>Optional message about the operation result</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Search Todos Request -->
         <xsd:complexType name="SearchTodosRequestType">
            <xsd:annotation>
               <xsd:documentation>Request to search for todo items based on criteria</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="criteria" type="types:SearchCriteriaType" minOccurs="0">
                  <xsd:annotation>
                     <xsd:documentation>Search criteria (if empty, returns all todos)</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
               <xsd:element name="limit" type="xsd:int" minOccurs="0">
                  <xsd:annotation>
                     <xsd:documentation>Maximum number of results to return</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
               <xsd:element name="offset" type="xsd:int" minOccurs="0">
                  <xsd:annotation>
                     <xsd:documentation>Number of results to skip (for pagination)</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Search Todos Response -->
         <xsd:complexType name="SearchTodosResponseType">
            <xsd:annotation>
               <xsd:documentation>Response containing search results</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="todos" type="types:TodoListType">
                  <xsd:annotation>
                     <xsd:documentation>List of todo items matching the search criteria</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
               <xsd:element name="hasMore" type="xsd:boolean">
                  <xsd:annotation>
                     <xsd:documentation>Indicates if there are more results available</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Error Response Type -->
         <xsd:complexType name="ErrorResponseType">
            <xsd:annotation>
               <xsd:documentation>Standard error response for all operations</xsd:documentation>
            </xsd:annotation>
            <xsd:sequence>
               <xsd:element name="errorCode" type="xsd:string">
                  <xsd:annotation>
                     <xsd:documentation>Error code identifying the type of error</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
               <xsd:element name="errorMessage" type="xsd:string">
                  <xsd:annotation>
                     <xsd:documentation>Human-readable error message</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
               <xsd:element name="timestamp" type="xsd:dateTime">
                  <xsd:annotation>
                     <xsd:documentation>When the error occurred</xsd:documentation>
                  </xsd:annotation>
               </xsd:element>
            </xsd:sequence>
         </xsd:complexType>

         <!-- Element declarations -->
         <xsd:element name="CreateTodoRequest" type="tns:CreateTodoRequestType"/>
         <xsd:element name="CreateTodoResponse" type="tns:CreateTodoResponseType"/>
         <xsd:element name="GetTodoRequest" type="tns:GetTodoRequestType"/>
         <xsd:element name="GetTodoResponse" type="tns:GetTodoResponseType"/>
         <xsd:element name="UpdateTodoRequest" type="tns:UpdateTodoRequestType"/>
         <xsd:element name="UpdateTodoResponse" type="tns:UpdateTodoResponseType"/>
         <xsd:element name="DeleteTodoRequest" type="tns:DeleteTodoRequestType"/>
         <xsd:element name="DeleteTodoResponse" type="tns:DeleteTodoResponseType"/>
         <xsd:element name="SearchTodosRequest" type="tns:SearchTodosRequestType"/>
         <xsd:element name="SearchTodosResponse" type="tns:SearchTodosResponseType"/>
         <xsd:element name="ErrorResponse" type="tns:ErrorResponseType"/>
         
      </xsd:schema>
   </types>

   <!-- Message definitions -->
   <message name="CreateTodoRequestMessage">
      <documentation>Message for creating a new todo item</documentation>
      <part name="request" element="tns:CreateTodoRequest"/>
   </message>
   <message name="CreateTodoResponseMessage">
      <documentation>Message containing the created todo item</documentation>
      <part name="response" element="tns:CreateTodoResponse"/>
   </message>

   <message name="GetTodoRequestMessage">
      <documentation>Message for retrieving a todo item by ID</documentation>
      <part name="request" element="tns:GetTodoRequest"/>
   </message>
   <message name="GetTodoResponseMessage">
      <documentation>Message containing the requested todo item</documentation>
      <part name="response" element="tns:GetTodoResponse"/>
   </message>

   <message name="UpdateTodoRequestMessage">
      <documentation>Message for updating an existing todo item</documentation>
      <part name="request" element="tns:UpdateTodoRequest"/>
   </message>
   <message name="UpdateTodoResponseMessage">
      <documentation>Message containing the updated todo item</documentation>
      <part name="response" element="tns:UpdateTodoResponse"/>
   </message>

   <message name="DeleteTodoRequestMessage">
      <documentation>Message for deleting a todo item</documentation>
      <part name="request" element="tns:DeleteTodoRequest"/>
   </message>
   <message name="DeleteTodoResponseMessage">
      <documentation>Message confirming todo item deletion</documentation>
      <part name="response" element="tns:DeleteTodoResponse"/>
   </message>

   <message name="SearchTodosRequestMessage">
      <documentation>Message for searching todo items</documentation>
      <part name="request" element="tns:SearchTodosRequest"/>
   </message>
   <message name="SearchTodosResponseMessage">
      <documentation>Message containing search results</documentation>
      <part name="response" element="tns:SearchTodosResponse"/>
   </message>

   <message name="ErrorResponseMessage">
      <documentation>Message for error responses</documentation>
      <part name="error" element="tns:ErrorResponse"/>
   </message>

   <!-- Port Type definition -->
   <portType name="TodoServicePortType">
      <documentation>Todo Service Port Type - Defines all available operations</documentation>

      <operation name="createTodo">
         <documentation>Creates a new todo item and returns it with assigned ID</documentation>
         <input message="tns:CreateTodoRequestMessage"/>
         <output message="tns:CreateTodoResponseMessage"/>
         <fault name="CreateTodoFault" message="tns:ErrorResponseMessage"/>
      </operation>

      <operation name="getTodo">
         <documentation>Retrieves a specific todo item by its ID</documentation>
         <input message="tns:GetTodoRequestMessage"/>
         <output message="tns:GetTodoResponseMessage"/>
         <fault name="GetTodoFault" message="tns:ErrorResponseMessage"/>
      </operation>

      <operation name="updateTodo">
         <documentation>Updates an existing todo item</documentation>
         <input message="tns:UpdateTodoRequestMessage"/>
         <output message="tns:UpdateTodoResponseMessage"/>
         <fault name="UpdateTodoFault" message="tns:ErrorResponseMessage"/>
      </operation>

      <operation name="deleteTodo">
         <documentation>Deletes a todo item by its ID</documentation>
         <input message="tns:DeleteTodoRequestMessage"/>
         <output message="tns:DeleteTodoResponseMessage"/>
         <fault name="DeleteTodoFault" message="tns:ErrorResponseMessage"/>
      </operation>

      <operation name="searchTodos">
         <documentation>Searches for todo items based on specified criteria</documentation>
         <input message="tns:SearchTodosRequestMessage"/>
         <output message="tns:SearchTodosResponseMessage"/>
         <fault name="SearchTodosFault" message="tns:ErrorResponseMessage"/>
      </operation>
   </portType>

   <!-- SOAP Binding -->
   <binding name="TodoServiceSoapBinding" type="tns:TodoServicePortType">
      <documentation>SOAP binding for Todo Service</documentation>
      <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <operation name="createTodo">
         <soap:operation soapAction="http://api.todoservice.com/createTodo"/>
         <input>
            <soap:body use="literal"/>
         </input>
         <output>
            <soap:body use="literal"/>
         </output>
         <fault name="CreateTodoFault">
            <soap:fault name="CreateTodoFault" use="literal"/>
         </fault>
      </operation>

      <operation name="getTodo">
         <soap:operation soapAction="http://api.todoservice.com/getTodo"/>
         <input>
            <soap:body use="literal"/>
         </input>
         <output>
            <soap:body use="literal"/>
         </output>
         <fault name="GetTodoFault">
            <soap:fault name="GetTodoFault" use="literal"/>
         </fault>
      </operation>

      <operation name="updateTodo">
         <soap:operation soapAction="http://api.todoservice.com/updateTodo"/>
         <input>
            <soap:body use="literal"/>
         </input>
         <output>
            <soap:body use="literal"/>
         </output>
         <fault name="UpdateTodoFault">
            <soap:fault name="UpdateTodoFault" use="literal"/>
         </fault>
      </operation>

      <operation name="deleteTodo">
         <soap:operation soapAction="http://api.todoservice.com/deleteTodo"/>
         <input>
            <soap:body use="literal"/>
         </input>
         <output>
            <soap:body use="literal"/>
         </output>
         <fault name="DeleteTodoFault">
            <soap:fault name="DeleteTodoFault" use="literal"/>
         </fault>
      </operation>

      <operation name="searchTodos">
         <soap:operation soapAction="http://api.todoservice.com/searchTodos"/>
         <input>
            <soap:body use="literal"/>
         </input>
         <output>
            <soap:body use="literal"/>
         </output>
         <fault name="SearchTodosFault">
            <soap:fault name="SearchTodosFault" use="literal"/>
         </fault>
      </operation>
   </binding>

   <!-- Service definition -->
   <service name="TodoService">
      <documentation>
         Todo Service - A comprehensive todo item management service
         Provides full CRUD operations, search functionality, and user management
         for todo items with support for priorities, due dates, and assignments.
      </documentation>
      <port name="TodoServiceSoapPort" binding="tns:TodoServiceSoapBinding">
         <soap:address location="http://api.todoservice.com/soap/TodoService"/>
      </port>
   </service>

</definitions>
