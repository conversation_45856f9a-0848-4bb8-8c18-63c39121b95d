<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns='http://schemas.xmlsoap.org/wsdl/'
    xmlns:soap='http://schemas.xmlsoap.org/wsdl/soap/'
    xmlns:wsp='http://www.w3.org/ns/ws-policy'
    xmlns:sp='http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702'
    xmlns:wsu='http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd'
    xmlns:tns='http://service.linkon.se/gtssales/v1_27'
    targetNamespace='http://service.linkon.se/gtssales/v1_27'
    xmlns:xs='http://www.w3.org/2001/XMLSchema'>
    <wsp:Policy wsu:Id='UsernameToken'>
      <sp:SupportingTokens>
        <wsp:Policy>
          <sp:UsernameToken sp:IncludeToken='http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702/IncludeToken/AlwaysToRecipient'>
            <wsp:Policy>
              <sp:WssUsernameToken10 />
            </wsp:Policy>
          </sp:UsernameToken>
        </wsp:Policy>
      </sp:SupportingTokens>
    </wsp:Policy>

    <import
        namespace='http://service.linkon.se/gtssales/v1_27'
        location='WSGtsSalesServiceInterface.wsdl' />
    <binding name='WSGtsSales-binding' type='tns:GtsSales-service'>

        <wsp:PolicyReference URI="#UsernameToken" />
        <soap:binding style='document'
            transport='http://schemas.xmlsoap.org/soap/http' />

        <!-- ==================== GetJourneyAdvicePriceQuotes ==================== -->
 
                
        <operation name='getJourneyAdviceOverviewPriceQuotes'>
            <documentation>
                The operation returns an overview over price quotes on a per journey
                advice using sales
                categories.
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header
                    message='tns:WSGtsSales_getJourneyAdviceOverviewPriceQuotesSpecificationRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='getJourneyAdviceDetailedPriceQuotes'>
            <documentation>
                The operation returns a detailed journey advice price
                quote using sales categories.
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header
                    message='tns:WSGtsSales_getJourneyAdviceDetailedPriceQuotesSpecificationRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <!-- ==================== GetValidPriceGroups ==================== -->

        <operation name='getValidPriceGroups'>
            <documentation>
                The operation returns valid pricegroups, optionally
                appending
                information
                concerning accumulators (antalsraknare), seat
                availability and prices, for
                the given itineraries.
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header
                    message='tns:WSGtsSales_getValidPriceGroupsSpecificationRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <!-- ==================== GetPriceGroups ==================== -->

        <operation name='getPriceGroups'>
            <documentation>
                The operation returns pricegroups.
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_getPriceGroupsRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='getValidCodes'>
            <documentation>
                The operation returns the valid (relevant) code lists
                for this service.
            </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_getValidCodeRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='bookItinerary'>
            <documentation>
                The operation books an itinerary service according to
                the request.
            </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_bookItineraryRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='bookSegment'>
            <documentation>
                The operation books a segment according to the
                request.
        </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_bookSegmentRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='detailedItineraryPriceQuote'>
            <documentation>
                The operation gets a detailed price quote for an
                itinerary according to
                the request.
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_detailedItineraryPriceQuoteRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='detailedSegmentPriceQuote'>
            <documentation>
                The operation gets a detailed price quote for a
                segment according to
                the request.
        </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_detailedSegmentPriceQuoteRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='editTransport'>
            <documentation>
                The operation edits an existing transport booking
                according to the
                request.
                Bookings can be altered in various ways, for
                instance by removing or adding
                a
                new service (component) or by
                modifying an existing service
                (component).
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_editTransportRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>
        
        <operation name='changeDeparture'>
            <documentation>
                The operation changes departure, if possible, according to the request.
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_changeDepartureRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='getItineraryCharacteristics'>
            <documentation>
                The operation returns the itinerary characteristics
                for the itinerary
                in the
                request.
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header
                    message='tns:WSGtsSales_getItineraryCharacteristicsSpecificationRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='getTransportActions'>
            <documentation>
                The operation returns the available edit actions for
                an existing
                transport booking.
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header
                    message='tns:WSGtsSales_getTransportActionsSpecificationRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <operation name='getMultiRideAlternativeRelations'>
            <documentation>
                The operation finds multiple ride alternative
                relations according to
                the request.
      </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header
                    message='tns:WSGtsSales_getMultiRideAlternativeRelationsRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>

        <!-- ==================== GetSeatAvailabilityDetail ==================== -->

        <operation name='getSeatAvailabilityDetail'>
            <documentation>
                The operation is responsible for retrieving detailed
                results regarding availabilities on ground travel services.
                (trains,
                buses, etc.)
            </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_getSeatAvailabilityDetailRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>
        
        <!-- ======================== GetOptionAvailability ======================== -->
        
        <operation name='getOptionAvailability'>
            <documentation>
                The operation is responsible for retrieving information about 'optionals'
                availabilities. Optionals in this case are for example breakfast, sandwiches or cold dishes.
            </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_getOptionAvailabilityRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>
        
       <!-- ==================== GetPlacementOptions ==================== -->

        <operation name='getPlacementOptions'>
            <documentation>
            Returns valid, but not necessarily available, placement options for a requested journey. 
            The placement options are returned for the different levels of the journey;
            The valid options for each segment, aggregated on itinerary level, and 
            aggregated for the journey as a whole.
            The aggregations are done according to a intersection kind of strategy, meaning for example that
            only placement options valid for all segment of an itinerary will be present on the itinerary level.
            However, segments that doesn't support booking, for example if BookingRule is EJBO, will NOT be regarded
            in this intersection strategy and will not have any impact on the Placement Options on the itinerary level.
            Only Placement Options available for all the journey's consumers will be returned. 
            This means that a journey with two consumers, containing a segment were there's only one placement left
            for each of the segment's placement types, will return empty lists of placement options. 
            </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_getPlacementOptionsRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>


        <!-- ======================== getPassengerCategories ======================== -->
        
        <operation name='getPassengerCategories'>
            <documentation>
                The operation returns passenger categories (and some information about them) to be used for pricequotes and booking.
            </documentation>
            <soap:operation soapAction='' />
            <input>
                <!-- The following header must be included in the actual message -->
                <!-- if security is in effect. It may still be left commented out -->
                <!-- here, as per section 3.7 of the WSDL spec -->
                <!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
                <!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
                    use='literal'/> -->
                <soap:header message='tns:WSGtsSales_getPassengerCategoriesRequest'
                    part='clientInformationHeader' use='literal' />
                <soap:body parts='parameters' use='literal' />
            </input>
            <output>
                <soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
                <soap:body use='literal' />
            </output>
            <fault name='GtsSalesException'>
                <soap:fault name='GtsSalesException' use='literal' />
            </fault>
        </operation>
		
		
		<!-- ======================== validatePromotionCode ======================== -->
		
		<operation name='validatePromotionCode'>
			<documentation>
				The operation validates if a promotion code exists or not.
			</documentation>
			<soap:operation soapAction='' />
			<input>
				<!-- The following header must be included in the actual message -->
				<!-- if security is in effect. It may still be left commented out -->
				<!-- here, as per section 3.7 of the WSDL spec -->
				<!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
				<!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
					use='literal'/> -->
				<soap:header message='tns:WSGtsSales_validatePromotionCodeRequest'
					part='clientInformationHeader' use='literal' />
				<soap:body parts='parameters' use='literal' />
			</input>
			<output>
				<soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
				<soap:body use='literal' />
			</output>
			<fault name='GtsSalesException'>
				<soap:fault name='GtsSalesException' use='literal' />
			</fault>
		</operation>


		<!-- ======================== validateTravelPass ======================== -->
		
		<operation name='validateTravelPass'>
			<documentation>
            	The operation validates if a travel pass is valid for travel.
			</documentation>
			<soap:operation soapAction='' />
			<input>
				<!-- The following header must be included in the actual message -->
				<!-- if security is in effect. It may still be left commented out -->
				<!-- here, as per section 3.7 of the WSDL spec -->
				<!-- (http://www.w3.org/TR/2001/NOTE-wsdl-20010315) -->
				<!-- <soap:header message='tns:SecurityHeader' part='securityHeader' 
					use='literal'/> -->
				<soap:header message='tns:WSGtsSales_validateTravelPassRequest'
					part='clientInformationHeader' use='literal' />
				<soap:body parts='parameters' use='literal' />
			</input>
			<output>
				<soap:header message='tns:ServerInformation' part='serverInformationHeader' use='literal' />
				<soap:body use='literal' />
			</output>
			<fault name='GtsSalesException'>
				<soap:fault name='GtsSalesException' use='literal' />
			</fault>
		</operation>
    </binding>

    <service name='WSGtsSalesService'>
        <port binding='tns:WSGtsSales-binding' name='WSGtsSales-port'>
            <soap:address location='http://dummyurl.linkon.se:8280/wsgtssales' />
        </port>
    </service>
</definitions>
