package com.todoservice.api;

import javax.jws.WebService;
import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;
import java.util.List;

/**
 * Todo Service Interface
 * 
 * Comprehensive web service interface for managing todo items.
 * Provides CRUD operations, search functionality, and user management
 * for todo items with support for priorities, due dates, and assignments.
 * 
 * <AUTHOR> Service Team
 * @version 1.0.0
 * @since 2024-01-01
 */
@WebService(
    name = "TodoService",
    targetNamespace = "http://api.todoservice.com/wsdl",
    serviceName = "TodoService",
    portName = "TodoServiceSoapPort"
)
public interface TodoService {

    /**
     * Creates a new todo item and returns it with assigned ID
     * 
     * @param todo The todo item to create (ID will be auto-generated)
     * @return The created todo item with assigned ID and timestamps
     * @throws TodoServiceException if creation fails
     */
    @WebMethod(operationName = "createTodo")
    @WebResult(name = "todo", targetNamespace = "http://api.todoservice.com/types")
    @RequestWrapper(
        localName = "CreateTodoRequest",
        targetNamespace = "http://api.todoservice.com/wsdl",
        className = "com.todoservice.api.CreateTodoRequest"
    )
    @ResponseWrapper(
        localName = "CreateTodoResponse", 
        targetNamespace = "http://api.todoservice.com/wsdl",
        className = "com.todoservice.api.CreateTodoResponse"
    )
    Todo createTodo(
        @WebParam(name = "todo", targetNamespace = "http://api.todoservice.com/types") 
        Todo todo
    ) throws TodoServiceException;

    /**
     * Retrieves a specific todo item by its ID
     * 
     * @param todoId Unique identifier of the todo item to retrieve
     * @return The requested todo item, or null if not found
     * @throws TodoServiceException if retrieval fails
     */
    @WebMethod(operationName = "getTodo")
    @WebResult(name = "todo", targetNamespace = "http://api.todoservice.com/types")
    @RequestWrapper(
        localName = "GetTodoRequest",
        targetNamespace = "http://api.todoservice.com/wsdl",
        className = "com.todoservice.api.GetTodoRequest"
    )
    @ResponseWrapper(
        localName = "GetTodoResponse",
        targetNamespace = "http://api.todoservice.com/wsdl", 
        className = "com.todoservice.api.GetTodoResponse"
    )
    Todo getTodo(
        @WebParam(name = "todoId", targetNamespace = "http://api.todoservice.com/wsdl") 
        Long todoId
    ) throws TodoServiceException;

    /**
     * Updates an existing todo item
     * 
     * @param todo Todo item with updated information (ID must be provided)
     * @return The updated todo item with new timestamp
     * @throws TodoServiceException if update fails or todo not found
     */
    @WebMethod(operationName = "updateTodo")
    @WebResult(name = "todo", targetNamespace = "http://api.todoservice.com/types")
    @RequestWrapper(
        localName = "UpdateTodoRequest",
        targetNamespace = "http://api.todoservice.com/wsdl",
        className = "com.todoservice.api.UpdateTodoRequest"
    )
    @ResponseWrapper(
        localName = "UpdateTodoResponse",
        targetNamespace = "http://api.todoservice.com/wsdl",
        className = "com.todoservice.api.UpdateTodoResponse"
    )
    Todo updateTodo(
        @WebParam(name = "todo", targetNamespace = "http://api.todoservice.com/types") 
        Todo todo
    ) throws TodoServiceException;

    /**
     * Deletes a todo item by its ID
     * 
     * @param todoId Unique identifier of the todo item to delete
     * @return true if deletion was successful, false if todo not found
     * @throws TodoServiceException if deletion fails
     */
    @WebMethod(operationName = "deleteTodo")
    @WebResult(name = "success", targetNamespace = "http://api.todoservice.com/wsdl")
    @RequestWrapper(
        localName = "DeleteTodoRequest",
        targetNamespace = "http://api.todoservice.com/wsdl",
        className = "com.todoservice.api.DeleteTodoRequest"
    )
    @ResponseWrapper(
        localName = "DeleteTodoResponse",
        targetNamespace = "http://api.todoservice.com/wsdl",
        className = "com.todoservice.api.DeleteTodoResponse"
    )
    boolean deleteTodo(
        @WebParam(name = "todoId", targetNamespace = "http://api.todoservice.com/wsdl") 
        Long todoId
    ) throws TodoServiceException;

    /**
     * Searches for todo items based on specified criteria
     * 
     * @param criteria Search criteria (status, priority, assignee, dates, text)
     * @param limit Maximum number of results to return (default: 20)
     * @param offset Number of results to skip for pagination (default: 0)
     * @return List of todo items matching the search criteria
     * @throws TodoServiceException if search fails
     */
    @WebMethod(operationName = "searchTodos")
    @WebResult(name = "todos", targetNamespace = "http://api.todoservice.com/types")
    @RequestWrapper(
        localName = "SearchTodosRequest",
        targetNamespace = "http://api.todoservice.com/wsdl",
        className = "com.todoservice.api.SearchTodosRequest"
    )
    @ResponseWrapper(
        localName = "SearchTodosResponse",
        targetNamespace = "http://api.todoservice.com/wsdl",
        className = "com.todoservice.api.SearchTodosResponse"
    )
    TodoList searchTodos(
        @WebParam(name = "criteria", targetNamespace = "http://api.todoservice.com/types") 
        SearchCriteria criteria,
        @WebParam(name = "limit", targetNamespace = "http://api.todoservice.com/wsdl") 
        Integer limit,
        @WebParam(name = "offset", targetNamespace = "http://api.todoservice.com/wsdl") 
        Integer offset
    ) throws TodoServiceException;

    /**
     * Retrieves all users who can be assigned to todo items
     * 
     * @param limit Maximum number of users to return
     * @param offset Number of users to skip for pagination
     * @return List of available users
     * @throws TodoServiceException if retrieval fails
     */
    @WebMethod(operationName = "getUsers")
    @WebResult(name = "users", targetNamespace = "http://api.todoservice.com/types")
    List<User> getUsers(
        @WebParam(name = "limit", targetNamespace = "http://api.todoservice.com/wsdl") 
        Integer limit,
        @WebParam(name = "offset", targetNamespace = "http://api.todoservice.com/wsdl") 
        Integer offset
    ) throws TodoServiceException;

    /**
     * Retrieves details of a specific user
     * 
     * @param userId Unique identifier of the user
     * @return User details, or null if not found
     * @throws TodoServiceException if retrieval fails
     */
    @WebMethod(operationName = "getUser")
    @WebResult(name = "user", targetNamespace = "http://api.todoservice.com/types")
    User getUser(
        @WebParam(name = "userId", targetNamespace = "http://api.todoservice.com/wsdl") 
        Long userId
    ) throws TodoServiceException;

    /**
     * Retrieves todo items assigned to a specific user
     * 
     * @param userId Unique identifier of the user
     * @param statusFilter Optional status filter
     * @param limit Maximum number of results to return
     * @param offset Number of results to skip for pagination
     * @return List of todo items assigned to the user
     * @throws TodoServiceException if retrieval fails
     */
    @WebMethod(operationName = "getTodosByUser")
    @WebResult(name = "todos", targetNamespace = "http://api.todoservice.com/types")
    TodoList getTodosByUser(
        @WebParam(name = "userId", targetNamespace = "http://api.todoservice.com/wsdl") 
        Long userId,
        @WebParam(name = "statusFilter", targetNamespace = "http://api.todoservice.com/types") 
        TodoStatus statusFilter,
        @WebParam(name = "limit", targetNamespace = "http://api.todoservice.com/wsdl") 
        Integer limit,
        @WebParam(name = "offset", targetNamespace = "http://api.todoservice.com/wsdl") 
        Integer offset
    ) throws TodoServiceException;

    /**
     * Bulk updates the status of multiple todo items
     * 
     * @param todoIds List of todo item IDs to update
     * @param newStatus New status to apply to all specified todos
     * @return Number of successfully updated todo items
     * @throws TodoServiceException if bulk update fails
     */
    @WebMethod(operationName = "bulkUpdateStatus")
    @WebResult(name = "updatedCount", targetNamespace = "http://api.todoservice.com/wsdl")
    int bulkUpdateStatus(
        @WebParam(name = "todoIds", targetNamespace = "http://api.todoservice.com/wsdl") 
        List<Long> todoIds,
        @WebParam(name = "newStatus", targetNamespace = "http://api.todoservice.com/types") 
        TodoStatus newStatus
    ) throws TodoServiceException;
}
