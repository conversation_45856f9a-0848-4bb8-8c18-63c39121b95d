package com.todoservice.api;

import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.datatype.XMLGregorianCalendar;
import java.util.List;
import java.util.ArrayList;

/**
 * Todo Item Data Type
 * 
 * Represents a single todo item with all its properties including
 * title, description, status, priority, due date, timestamps,
 * assigned user, and associated tags.
 * 
 * <AUTHOR> Service Team
 * @version 1.0.0
 */
@XmlType(
    name = "TodoType",
    namespace = "http://api.todoservice.com/types",
    propOrder = {
        "id", "title", "description", "status", "priority", 
        "dueDate", "createdAt", "updatedAt", "assignee", "tags"
    }
)
@XmlRootElement(name = "Todo", namespace = "http://api.todoservice.com/types")
@XmlAccessorType(XmlAccessType.FIELD)
public class Todo {

    /**
     * Unique identifier for the todo item (auto-generated)
     */
    @XmlElement(name = "id", namespace = "http://api.todoservice.com/types")
    protected Long id;

    /**
     * Brief title or summary of the todo item (required)
     */
    @XmlElement(name = "title", namespace = "http://api.todoservice.com/types", required = true)
    protected String title;

    /**
     * Detailed description of the todo item (optional)
     */
    @XmlElement(name = "description", namespace = "http://api.todoservice.com/types")
    protected String description;

    /**
     * Current status of the todo item (required)
     */
    @XmlElement(name = "status", namespace = "http://api.todoservice.com/types", required = true)
    protected TodoStatus status;

    /**
     * Priority level of the todo item (optional)
     */
    @XmlElement(name = "priority", namespace = "http://api.todoservice.com/types")
    protected Priority priority;

    /**
     * Due date and time for the todo item (optional)
     */
    @XmlElement(name = "dueDate", namespace = "http://api.todoservice.com/types")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dueDate;

    /**
     * Timestamp when the todo item was created (auto-generated)
     */
    @XmlElement(name = "createdAt", namespace = "http://api.todoservice.com/types")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar createdAt;

    /**
     * Timestamp when the todo item was last updated (auto-generated)
     */
    @XmlElement(name = "updatedAt", namespace = "http://api.todoservice.com/types")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar updatedAt;

    /**
     * User assigned to this todo item (optional)
     */
    @XmlElement(name = "assignee", namespace = "http://api.todoservice.com/types")
    protected User assignee;

    /**
     * List of tags associated with this todo item (optional)
     */
    @XmlElement(name = "tags", namespace = "http://api.todoservice.com/types")
    protected List<String> tags;

    /**
     * Default constructor
     */
    public Todo() {
        this.tags = new ArrayList<>();
        this.status = TodoStatus.PENDING;
    }

    /**
     * Constructor with required fields
     * 
     * @param title The title of the todo item
     */
    public Todo(String title) {
        this();
        this.title = title;
    }

    /**
     * Constructor with title and description
     * 
     * @param title The title of the todo item
     * @param description The description of the todo item
     */
    public Todo(String title, String description) {
        this(title);
        this.description = description;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public TodoStatus getStatus() {
        return status;
    }

    public void setStatus(TodoStatus status) {
        this.status = status;
    }

    public Priority getPriority() {
        return priority;
    }

    public void setPriority(Priority priority) {
        this.priority = priority;
    }

    public XMLGregorianCalendar getDueDate() {
        return dueDate;
    }

    public void setDueDate(XMLGregorianCalendar dueDate) {
        this.dueDate = dueDate;
    }

    public XMLGregorianCalendar getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(XMLGregorianCalendar createdAt) {
        this.createdAt = createdAt;
    }

    public XMLGregorianCalendar getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(XMLGregorianCalendar updatedAt) {
        this.updatedAt = updatedAt;
    }

    public User getAssignee() {
        return assignee;
    }

    public void setAssignee(User assignee) {
        this.assignee = assignee;
    }

    public List<String> getTags() {
        if (tags == null) {
            tags = new ArrayList<>();
        }
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    /**
     * Add a tag to this todo item
     * 
     * @param tag The tag to add
     */
    public void addTag(String tag) {
        if (tag != null && !tag.trim().isEmpty()) {
            getTags().add(tag.trim());
        }
    }

    /**
     * Remove a tag from this todo item
     * 
     * @param tag The tag to remove
     * @return true if the tag was removed, false if it wasn't found
     */
    public boolean removeTag(String tag) {
        return getTags().remove(tag);
    }

    /**
     * Check if this todo item has a specific tag
     * 
     * @param tag The tag to check for
     * @return true if the todo has the tag, false otherwise
     */
    public boolean hasTag(String tag) {
        return getTags().contains(tag);
    }

    /**
     * Check if this todo item is overdue
     * 
     * @return true if the todo has a due date and it's in the past
     */
    public boolean isOverdue() {
        if (dueDate == null) {
            return false;
        }
        XMLGregorianCalendar now = new XMLGregorianCalendar();
        return dueDate.compare(now) < 0;
    }

    /**
     * Check if this todo item is completed
     * 
     * @return true if the status is COMPLETED
     */
    public boolean isCompleted() {
        return TodoStatus.COMPLETED.equals(status);
    }

    /**
     * Mark this todo item as completed
     */
    public void markCompleted() {
        this.status = TodoStatus.COMPLETED;
    }

    /**
     * Mark this todo item as in progress
     */
    public void markInProgress() {
        this.status = TodoStatus.IN_PROGRESS;
    }

    @Override
    public String toString() {
        return "Todo{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", status=" + status +
                ", priority=" + priority +
                ", dueDate=" + dueDate +
                ", assignee=" + (assignee != null ? assignee.getUsername() : "none") +
                ", tags=" + tags +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Todo todo = (Todo) o;
        return id != null && id.equals(todo.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
