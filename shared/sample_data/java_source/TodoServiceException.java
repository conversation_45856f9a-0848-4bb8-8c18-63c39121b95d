package com.todoservice.api;

import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.ws.WebFault;

/**
 * Todo Service Exception
 * 
 * Custom exception class for Todo Service operations.
 * Provides structured error information including error codes,
 * messages, and additional context for debugging and client handling.
 * 
 * <AUTHOR> Service Team
 * @version 1.0.0
 */
@WebFault(
    name = "TodoServiceFault",
    targetNamespace = "http://api.todoservice.com/wsdl",
    faultBean = "com.todoservice.api.TodoServiceException$TodoServiceFaultInfo"
)
public class TodoServiceException extends Exception {

    private static final long serialVersionUID = 1L;

    /**
     * Fault information for SOAP fault details
     */
    private TodoServiceFaultInfo faultInfo;

    /**
     * Default constructor
     */
    public TodoServiceException() {
        super();
        this.faultInfo = new TodoServiceFaultInfo();
    }

    /**
     * Constructor with message
     * 
     * @param message Error message
     */
    public TodoServiceException(String message) {
        super(message);
        this.faultInfo = new TodoServiceFaultInfo();
        this.faultInfo.setErrorMessage(message);
    }

    /**
     * Constructor with message and cause
     * 
     * @param message Error message
     * @param cause Root cause exception
     */
    public TodoServiceException(String message, Throwable cause) {
        super(message, cause);
        this.faultInfo = new TodoServiceFaultInfo();
        this.faultInfo.setErrorMessage(message);
    }

    /**
     * Constructor with error code and message
     * 
     * @param errorCode Error code
     * @param message Error message
     */
    public TodoServiceException(String errorCode, String message) {
        super(message);
        this.faultInfo = new TodoServiceFaultInfo();
        this.faultInfo.setErrorCode(errorCode);
        this.faultInfo.setErrorMessage(message);
    }

    /**
     * Constructor with error code, message, and cause
     * 
     * @param errorCode Error code
     * @param message Error message
     * @param cause Root cause exception
     */
    public TodoServiceException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.faultInfo = new TodoServiceFaultInfo();
        this.faultInfo.setErrorCode(errorCode);
        this.faultInfo.setErrorMessage(message);
    }

    /**
     * Constructor with fault info
     * 
     * @param message Error message
     * @param faultInfo Detailed fault information
     */
    public TodoServiceException(String message, TodoServiceFaultInfo faultInfo) {
        super(message);
        this.faultInfo = faultInfo;
    }

    /**
     * Constructor with fault info and cause
     * 
     * @param message Error message
     * @param faultInfo Detailed fault information
     * @param cause Root cause exception
     */
    public TodoServiceException(String message, TodoServiceFaultInfo faultInfo, Throwable cause) {
        super(message, cause);
        this.faultInfo = faultInfo;
    }

    /**
     * Get the fault information
     * 
     * @return Fault information for SOAP fault details
     */
    public TodoServiceFaultInfo getFaultInfo() {
        return faultInfo;
    }

    /**
     * Set the fault information
     * 
     * @param faultInfo Fault information
     */
    public void setFaultInfo(TodoServiceFaultInfo faultInfo) {
        this.faultInfo = faultInfo;
    }

    /**
     * Get the error code
     * 
     * @return Error code, or null if not set
     */
    public String getErrorCode() {
        return faultInfo != null ? faultInfo.getErrorCode() : null;
    }

    /**
     * Set the error code
     * 
     * @param errorCode Error code
     */
    public void setErrorCode(String errorCode) {
        if (faultInfo == null) {
            faultInfo = new TodoServiceFaultInfo();
        }
        faultInfo.setErrorCode(errorCode);
    }

    // Static factory methods for common error types

    /**
     * Create exception for todo not found
     * 
     * @param todoId The ID of the todo that was not found
     * @return TodoServiceException with appropriate error details
     */
    public static TodoServiceException todoNotFound(Long todoId) {
        return new TodoServiceException(
            "TODO_NOT_FOUND",
            "Todo item with ID " + todoId + " was not found"
        );
    }

    /**
     * Create exception for user not found
     * 
     * @param userId The ID of the user that was not found
     * @return TodoServiceException with appropriate error details
     */
    public static TodoServiceException userNotFound(Long userId) {
        return new TodoServiceException(
            "USER_NOT_FOUND",
            "User with ID " + userId + " was not found"
        );
    }

    /**
     * Create exception for validation errors
     * 
     * @param message Validation error message
     * @return TodoServiceException with validation error details
     */
    public static TodoServiceException validationError(String message) {
        return new TodoServiceException(
            "VALIDATION_ERROR",
            "Validation failed: " + message
        );
    }

    /**
     * Create exception for unauthorized access
     * 
     * @param message Authorization error message
     * @return TodoServiceException with authorization error details
     */
    public static TodoServiceException unauthorized(String message) {
        return new TodoServiceException(
            "UNAUTHORIZED",
            "Unauthorized: " + message
        );
    }

    /**
     * Create exception for internal server errors
     * 
     * @param message Error message
     * @param cause Root cause
     * @return TodoServiceException with internal error details
     */
    public static TodoServiceException internalError(String message, Throwable cause) {
        return new TodoServiceException(
            "INTERNAL_ERROR",
            "Internal server error: " + message,
            cause
        );
    }

    /**
     * Fault Information Bean for SOAP Fault Details
     */
    @XmlType(
        name = "TodoServiceFaultInfo",
        namespace = "http://api.todoservice.com/wsdl",
        propOrder = {"errorCode", "errorMessage", "timestamp", "details"}
    )
    @XmlRootElement(name = "TodoServiceFaultInfo", namespace = "http://api.todoservice.com/wsdl")
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class TodoServiceFaultInfo {

        /**
         * Error code identifying the type of error
         */
        @XmlElement(name = "errorCode", namespace = "http://api.todoservice.com/wsdl", required = true)
        protected String errorCode;

        /**
         * Human-readable error message
         */
        @XmlElement(name = "errorMessage", namespace = "http://api.todoservice.com/wsdl", required = true)
        protected String errorMessage;

        /**
         * Timestamp when the error occurred
         */
        @XmlElement(name = "timestamp", namespace = "http://api.todoservice.com/wsdl")
        protected String timestamp;

        /**
         * Additional error details
         */
        @XmlElement(name = "details", namespace = "http://api.todoservice.com/wsdl")
        protected String details;

        /**
         * Default constructor
         */
        public TodoServiceFaultInfo() {
            this.timestamp = java.time.Instant.now().toString();
        }

        /**
         * Constructor with error code and message
         * 
         * @param errorCode Error code
         * @param errorMessage Error message
         */
        public TodoServiceFaultInfo(String errorCode, String errorMessage) {
            this();
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        // Getters and Setters

        public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        public String getDetails() {
            return details;
        }

        public void setDetails(String details) {
            this.details = details;
        }

        @Override
        public String toString() {
            return "TodoServiceFaultInfo{" +
                    "errorCode='" + errorCode + '\'' +
                    ", errorMessage='" + errorMessage + '\'' +
                    ", timestamp='" + timestamp + '\'' +
                    ", details='" + details + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "TodoServiceException{" +
                "errorCode='" + getErrorCode() + '\'' +
                ", message='" + getMessage() + '\'' +
                ", faultInfo=" + faultInfo +
                '}';
    }
}
