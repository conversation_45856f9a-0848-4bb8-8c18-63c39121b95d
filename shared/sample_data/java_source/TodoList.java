package com.todoservice.api;

import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import java.util.List;
import java.util.ArrayList;

/**
 * Todo List Data Type
 * 
 * Represents a collection of todo items with pagination metadata.
 * Used for returning search results and lists of todos with
 * information about total count and pagination state.
 * 
 * <AUTHOR> Service Team
 * @version 1.0.0
 */
@XmlType(
    name = "TodoListType",
    namespace = "http://api.todoservice.com/types",
    propOrder = {"todos", "totalCount", "hasMore", "limit", "offset"}
)
@XmlRootElement(name = "TodoList", namespace = "http://api.todoservice.com/types")
@XmlAccessorType(XmlAccessType.FIELD)
public class TodoList {

    /**
     * List of todo items
     */
    @XmlElement(name = "todo", namespace = "http://api.todoservice.com/types")
    protected List<Todo> todos;

    /**
     * Total number of todo items matching the criteria (for pagination)
     */
    @XmlElement(name = "totalCount", namespace = "http://api.todoservice.com/types")
    protected Integer totalCount;

    /**
     * Indicates if there are more results available beyond this page
     */
    @XmlElement(name = "hasMore", namespace = "http://api.todoservice.com/types")
    protected Boolean hasMore;

    /**
     * Maximum number of items requested in this page
     */
    @XmlElement(name = "limit", namespace = "http://api.todoservice.com/types")
    protected Integer limit;

    /**
     * Number of items skipped (offset for pagination)
     */
    @XmlElement(name = "offset", namespace = "http://api.todoservice.com/types")
    protected Integer offset;

    /**
     * Default constructor
     */
    public TodoList() {
        this.todos = new ArrayList<>();
        this.totalCount = 0;
        this.hasMore = false;
        this.limit = 20;
        this.offset = 0;
    }

    /**
     * Constructor with todos list
     * 
     * @param todos List of todo items
     */
    public TodoList(List<Todo> todos) {
        this();
        if (todos != null) {
            this.todos = new ArrayList<>(todos);
            this.totalCount = todos.size();
        }
    }

    /**
     * Constructor with pagination parameters
     * 
     * @param todos List of todo items
     * @param totalCount Total number of items matching criteria
     * @param limit Maximum items per page
     * @param offset Number of items skipped
     */
    public TodoList(List<Todo> todos, Integer totalCount, Integer limit, Integer offset) {
        this(todos);
        this.totalCount = totalCount != null ? totalCount : (todos != null ? todos.size() : 0);
        this.limit = limit != null ? limit : 20;
        this.offset = offset != null ? offset : 0;
        this.hasMore = calculateHasMore();
    }

    // Getters and Setters

    public List<Todo> getTodos() {
        if (todos == null) {
            todos = new ArrayList<>();
        }
        return todos;
    }

    public void setTodos(List<Todo> todos) {
        this.todos = todos;
        updateHasMore();
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        updateHasMore();
    }

    public Boolean getHasMore() {
        return hasMore;
    }

    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
        updateHasMore();
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
        updateHasMore();
    }

    // Utility Methods

    /**
     * Add a todo item to this list
     * 
     * @param todo The todo item to add
     */
    public void addTodo(Todo todo) {
        if (todo != null) {
            getTodos().add(todo);
            if (totalCount != null) {
                totalCount++;
            }
            updateHasMore();
        }
    }

    /**
     * Remove a todo item from this list
     * 
     * @param todo The todo item to remove
     * @return true if the item was removed
     */
    public boolean removeTodo(Todo todo) {
        boolean removed = getTodos().remove(todo);
        if (removed && totalCount != null && totalCount > 0) {
            totalCount--;
            updateHasMore();
        }
        return removed;
    }

    /**
     * Get the number of items in this page
     * 
     * @return Number of todo items in the current page
     */
    public int getPageSize() {
        return getTodos().size();
    }

    /**
     * Check if this is the first page
     * 
     * @return true if offset is 0
     */
    public boolean isFirstPage() {
        return offset == null || offset == 0;
    }

    /**
     * Check if this is the last page
     * 
     * @return true if there are no more results
     */
    public boolean isLastPage() {
        return hasMore == null || !hasMore;
    }

    /**
     * Get the current page number (1-based)
     * 
     * @return Current page number
     */
    public int getCurrentPage() {
        if (limit == null || limit <= 0 || offset == null) {
            return 1;
        }
        return (offset / limit) + 1;
    }

    /**
     * Get the total number of pages
     * 
     * @return Total number of pages, or -1 if cannot be determined
     */
    public int getTotalPages() {
        if (totalCount == null || limit == null || limit <= 0) {
            return -1;
        }
        return (int) Math.ceil((double) totalCount / limit);
    }

    /**
     * Get the starting item number for this page (1-based)
     * 
     * @return Starting item number
     */
    public int getStartItemNumber() {
        if (offset == null) {
            return 1;
        }
        return offset + 1;
    }

    /**
     * Get the ending item number for this page (1-based)
     * 
     * @return Ending item number
     */
    public int getEndItemNumber() {
        return getStartItemNumber() + getPageSize() - 1;
    }

    /**
     * Calculate if there are more results available
     * 
     * @return true if there are more results beyond this page
     */
    private boolean calculateHasMore() {
        if (totalCount == null || offset == null || limit == null) {
            return false;
        }
        return (offset + getPageSize()) < totalCount;
    }

    /**
     * Update the hasMore flag based on current pagination state
     */
    private void updateHasMore() {
        this.hasMore = calculateHasMore();
    }

    /**
     * Create a summary string for this todo list
     * 
     * @return Summary string with pagination info
     */
    public String getSummary() {
        if (totalCount == null) {
            return String.format("%d items", getPageSize());
        }
        
        if (totalCount == 0) {
            return "No items found";
        }
        
        if (limit == null || getPageSize() >= totalCount) {
            return String.format("%d items", totalCount);
        }
        
        return String.format("Items %d-%d of %d (Page %d of %d)", 
                           getStartItemNumber(), getEndItemNumber(), 
                           totalCount, getCurrentPage(), getTotalPages());
    }

    /**
     * Filter todos by status
     * 
     * @param status The status to filter by
     * @return New TodoList containing only todos with the specified status
     */
    public TodoList filterByStatus(TodoStatus status) {
        List<Todo> filtered = new ArrayList<>();
        for (Todo todo : getTodos()) {
            if (status.equals(todo.getStatus())) {
                filtered.add(todo);
            }
        }
        return new TodoList(filtered);
    }

    /**
     * Filter todos by priority
     * 
     * @param priority The priority to filter by
     * @return New TodoList containing only todos with the specified priority
     */
    public TodoList filterByPriority(Priority priority) {
        List<Todo> filtered = new ArrayList<>();
        for (Todo todo : getTodos()) {
            if (priority.equals(todo.getPriority())) {
                filtered.add(todo);
            }
        }
        return new TodoList(filtered);
    }

    /**
     * Filter todos by assignee
     * 
     * @param userId The user ID to filter by
     * @return New TodoList containing only todos assigned to the specified user
     */
    public TodoList filterByAssignee(Long userId) {
        List<Todo> filtered = new ArrayList<>();
        for (Todo todo : getTodos()) {
            User assignee = todo.getAssignee();
            if (assignee != null && userId.equals(assignee.getId())) {
                filtered.add(todo);
            }
        }
        return new TodoList(filtered);
    }

    @Override
    public String toString() {
        return "TodoList{" +
                "todos=" + (todos != null ? todos.size() : 0) + " items" +
                ", totalCount=" + totalCount +
                ", hasMore=" + hasMore +
                ", limit=" + limit +
                ", offset=" + offset +
                '}';
    }
}
