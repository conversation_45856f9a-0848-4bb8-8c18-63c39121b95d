package com.todoservice.api;

import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.datatype.XMLGregorianCalendar;
import java.util.List;
import java.util.ArrayList;

/**
 * Search Criteria Data Type
 * 
 * Represents criteria for searching and filtering todo items.
 * Supports filtering by status, priority, assignee, date ranges,
 * and text search in title and description.
 * 
 * <AUTHOR> Service Team
 * @version 1.0.0
 */
@XmlType(
    name = "SearchCriteriaType",
    namespace = "http://api.todoservice.com/types",
    propOrder = {
        "statuses", "priorities", "assigneeId", "dueBefore", "dueAfter", 
        "createdBefore", "createdAfter", "textSearch", "tags", "hasAssignee"
    }
)
@XmlRootElement(name = "SearchCriteria", namespace = "http://api.todoservice.com/types")
@XmlAccessorType(XmlAccessType.FIELD)
public class SearchCriteria {

    /**
     * Filter by status (multiple values allowed)
     */
    @XmlElement(name = "status", namespace = "http://api.todoservice.com/types")
    protected List<TodoStatus> statuses;

    /**
     * Filter by priority (multiple values allowed)
     */
    @XmlElement(name = "priority", namespace = "http://api.todoservice.com/types")
    protected List<Priority> priorities;

    /**
     * Filter by assigned user ID
     */
    @XmlElement(name = "assigneeId", namespace = "http://api.todoservice.com/types")
    protected Long assigneeId;

    /**
     * Filter items due before this date
     */
    @XmlElement(name = "dueBefore", namespace = "http://api.todoservice.com/types")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dueBefore;

    /**
     * Filter items due after this date
     */
    @XmlElement(name = "dueAfter", namespace = "http://api.todoservice.com/types")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar dueAfter;

    /**
     * Filter items created before this date
     */
    @XmlElement(name = "createdBefore", namespace = "http://api.todoservice.com/types")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar createdBefore;

    /**
     * Filter items created after this date
     */
    @XmlElement(name = "createdAfter", namespace = "http://api.todoservice.com/types")
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar createdAfter;

    /**
     * Search text in title and description
     */
    @XmlElement(name = "textSearch", namespace = "http://api.todoservice.com/types")
    protected String textSearch;

    /**
     * Filter by tags (multiple values allowed)
     */
    @XmlElement(name = "tag", namespace = "http://api.todoservice.com/types")
    protected List<String> tags;

    /**
     * Filter by whether todo has an assignee (true) or not (false)
     * If null, both assigned and unassigned todos are included
     */
    @XmlElement(name = "hasAssignee", namespace = "http://api.todoservice.com/types")
    protected Boolean hasAssignee;

    /**
     * Default constructor
     */
    public SearchCriteria() {
        this.statuses = new ArrayList<>();
        this.priorities = new ArrayList<>();
        this.tags = new ArrayList<>();
    }

    // Getters and Setters

    public List<TodoStatus> getStatuses() {
        if (statuses == null) {
            statuses = new ArrayList<>();
        }
        return statuses;
    }

    public void setStatuses(List<TodoStatus> statuses) {
        this.statuses = statuses;
    }

    public List<Priority> getPriorities() {
        if (priorities == null) {
            priorities = new ArrayList<>();
        }
        return priorities;
    }

    public void setPriorities(List<Priority> priorities) {
        this.priorities = priorities;
    }

    public Long getAssigneeId() {
        return assigneeId;
    }

    public void setAssigneeId(Long assigneeId) {
        this.assigneeId = assigneeId;
    }

    public XMLGregorianCalendar getDueBefore() {
        return dueBefore;
    }

    public void setDueBefore(XMLGregorianCalendar dueBefore) {
        this.dueBefore = dueBefore;
    }

    public XMLGregorianCalendar getDueAfter() {
        return dueAfter;
    }

    public void setDueAfter(XMLGregorianCalendar dueAfter) {
        this.dueAfter = dueAfter;
    }

    public XMLGregorianCalendar getCreatedBefore() {
        return createdBefore;
    }

    public void setCreatedBefore(XMLGregorianCalendar createdBefore) {
        this.createdBefore = createdBefore;
    }

    public XMLGregorianCalendar getCreatedAfter() {
        return createdAfter;
    }

    public void setCreatedAfter(XMLGregorianCalendar createdAfter) {
        this.createdAfter = createdAfter;
    }

    public String getTextSearch() {
        return textSearch;
    }

    public void setTextSearch(String textSearch) {
        this.textSearch = textSearch;
    }

    public List<String> getTags() {
        if (tags == null) {
            tags = new ArrayList<>();
        }
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Boolean getHasAssignee() {
        return hasAssignee;
    }

    public void setHasAssignee(Boolean hasAssignee) {
        this.hasAssignee = hasAssignee;
    }

    // Utility Methods

    /**
     * Add a status to the filter criteria
     * 
     * @param status The status to add
     */
    public void addStatus(TodoStatus status) {
        if (status != null && !getStatuses().contains(status)) {
            getStatuses().add(status);
        }
    }

    /**
     * Add a priority to the filter criteria
     * 
     * @param priority The priority to add
     */
    public void addPriority(Priority priority) {
        if (priority != null && !getPriorities().contains(priority)) {
            getPriorities().add(priority);
        }
    }

    /**
     * Add a tag to the filter criteria
     * 
     * @param tag The tag to add
     */
    public void addTag(String tag) {
        if (tag != null && !tag.trim().isEmpty() && !getTags().contains(tag.trim())) {
            getTags().add(tag.trim());
        }
    }

    /**
     * Check if this criteria has any filters applied
     * 
     * @return true if any filter criteria are set
     */
    public boolean hasFilters() {
        return !getStatuses().isEmpty() ||
               !getPriorities().isEmpty() ||
               assigneeId != null ||
               dueBefore != null ||
               dueAfter != null ||
               createdBefore != null ||
               createdAfter != null ||
               (textSearch != null && !textSearch.trim().isEmpty()) ||
               !getTags().isEmpty() ||
               hasAssignee != null;
    }

    /**
     * Check if this criteria matches a given todo item
     * 
     * @param todo The todo item to check
     * @return true if the todo matches all specified criteria
     */
    public boolean matches(Todo todo) {
        if (todo == null) {
            return false;
        }

        // Check status filter
        if (!getStatuses().isEmpty() && !getStatuses().contains(todo.getStatus())) {
            return false;
        }

        // Check priority filter
        if (!getPriorities().isEmpty() && !getPriorities().contains(todo.getPriority())) {
            return false;
        }

        // Check assignee filter
        if (assigneeId != null) {
            User assignee = todo.getAssignee();
            if (assignee == null || !assigneeId.equals(assignee.getId())) {
                return false;
            }
        }

        // Check hasAssignee filter
        if (hasAssignee != null) {
            boolean todoHasAssignee = todo.getAssignee() != null;
            if (hasAssignee != todoHasAssignee) {
                return false;
            }
        }

        // Check due date filters
        if (dueBefore != null && todo.getDueDate() != null) {
            if (todo.getDueDate().compare(dueBefore) >= 0) {
                return false;
            }
        }

        if (dueAfter != null && todo.getDueDate() != null) {
            if (todo.getDueDate().compare(dueAfter) <= 0) {
                return false;
            }
        }

        // Check created date filters
        if (createdBefore != null && todo.getCreatedAt() != null) {
            if (todo.getCreatedAt().compare(createdBefore) >= 0) {
                return false;
            }
        }

        if (createdAfter != null && todo.getCreatedAt() != null) {
            if (todo.getCreatedAt().compare(createdAfter) <= 0) {
                return false;
            }
        }

        // Check text search
        if (textSearch != null && !textSearch.trim().isEmpty()) {
            String searchLower = textSearch.toLowerCase();
            boolean titleMatch = todo.getTitle() != null && 
                                todo.getTitle().toLowerCase().contains(searchLower);
            boolean descMatch = todo.getDescription() != null && 
                               todo.getDescription().toLowerCase().contains(searchLower);
            
            if (!titleMatch && !descMatch) {
                return false;
            }
        }

        // Check tags filter
        if (!getTags().isEmpty()) {
            List<String> todoTags = todo.getTags();
            boolean hasMatchingTag = false;
            
            for (String requiredTag : getTags()) {
                if (todoTags.contains(requiredTag)) {
                    hasMatchingTag = true;
                    break;
                }
            }
            
            if (!hasMatchingTag) {
                return false;
            }
        }

        return true;
    }

    /**
     * Create a copy of this search criteria
     * 
     * @return A new SearchCriteria instance with the same values
     */
    public SearchCriteria copy() {
        SearchCriteria copy = new SearchCriteria();
        copy.setStatuses(new ArrayList<>(getStatuses()));
        copy.setPriorities(new ArrayList<>(getPriorities()));
        copy.setAssigneeId(this.assigneeId);
        copy.setDueBefore(this.dueBefore);
        copy.setDueAfter(this.dueAfter);
        copy.setCreatedBefore(this.createdBefore);
        copy.setCreatedAfter(this.createdAfter);
        copy.setTextSearch(this.textSearch);
        copy.setTags(new ArrayList<>(getTags()));
        copy.setHasAssignee(this.hasAssignee);
        return copy;
    }

    /**
     * Clear all filter criteria
     */
    public void clear() {
        getStatuses().clear();
        getPriorities().clear();
        this.assigneeId = null;
        this.dueBefore = null;
        this.dueAfter = null;
        this.createdBefore = null;
        this.createdAfter = null;
        this.textSearch = null;
        getTags().clear();
        this.hasAssignee = null;
    }

    @Override
    public String toString() {
        return "SearchCriteria{" +
                "statuses=" + statuses +
                ", priorities=" + priorities +
                ", assigneeId=" + assigneeId +
                ", dueBefore=" + dueBefore +
                ", dueAfter=" + dueAfter +
                ", textSearch='" + textSearch + '\'' +
                ", tags=" + tags +
                ", hasAssignee=" + hasAssignee +
                '}';
    }
}
