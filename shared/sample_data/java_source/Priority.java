package com.todoservice.api;

import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;

/**
 * Priority Enumeration
 * 
 * Defines the priority levels for todo items to help with task prioritization
 * and resource allocation. Higher priority items should be addressed first.
 * 
 * <AUTHOR> Service Team
 * @version 1.0.0
 */
@XmlType(name = "PriorityType", namespace = "http://api.todoservice.com/types")
@XmlEnum
public enum Priority {

    /**
     * Low priority - can be addressed when time permits
     * These are nice-to-have items that don't have strict deadlines.
     */
    @XmlEnumValue("LOW")
    LOW("LOW", "Low priority - can be addressed when time permits", 1),

    /**
     * Medium priority - normal priority level
     * Standard priority for most todo items in regular workflow.
     */
    @XmlEnumValue("MEDIUM")
    MEDIUM("MEDIUM", "Medium priority - normal priority level", 2),

    /**
     * High priority - should be addressed soon
     * Important items that should be completed in the near term.
     */
    @XmlEnumValue("HIGH")
    HIGH("HIGH", "High priority - should be addressed soon", 3),

    /**
     * Urgent priority - requires immediate attention
     * Critical items that need to be addressed as soon as possible.
     */
    @XmlEnumValue("URGENT")
    URGENT("URGENT", "Urgent priority - requires immediate attention", 4);

    private final String value;
    private final String description;
    private final int numericValue;

    /**
     * Constructor for Priority enum
     * 
     * @param value The string value of the priority
     * @param description Human-readable description of the priority
     * @param numericValue Numeric value for sorting (higher = more urgent)
     */
    Priority(String value, String description, int numericValue) {
        this.value = value;
        this.description = description;
        this.numericValue = numericValue;
    }

    /**
     * Get the string value of this priority
     * 
     * @return The string representation of the priority
     */
    public String value() {
        return value;
    }

    /**
     * Get the description of this priority
     * 
     * @return Human-readable description of what this priority means
     */
    public String getDescription() {
        return description;
    }

    /**
     * Get the numeric value of this priority for sorting
     * Higher numbers indicate higher priority
     * 
     * @return Numeric value (1=LOW, 2=MEDIUM, 3=HIGH, 4=URGENT)
     */
    public int getNumericValue() {
        return numericValue;
    }

    /**
     * Convert a string value to Priority enum
     * 
     * @param value The string value to convert
     * @return The corresponding Priority enum value
     * @throws IllegalArgumentException if the value is not a valid priority
     */
    public static Priority fromValue(String value) {
        if (value == null) {
            throw new IllegalArgumentException("Priority value cannot be null");
        }
        
        for (Priority priority : Priority.values()) {
            if (priority.value.equals(value)) {
                return priority;
            }
        }
        
        throw new IllegalArgumentException("Invalid priority value: " + value);
    }

    /**
     * Convert a numeric value to Priority enum
     * 
     * @param numericValue The numeric value to convert (1-4)
     * @return The corresponding Priority enum value
     * @throws IllegalArgumentException if the value is not valid
     */
    public static Priority fromNumericValue(int numericValue) {
        for (Priority priority : Priority.values()) {
            if (priority.numericValue == numericValue) {
                return priority;
            }
        }
        
        throw new IllegalArgumentException("Invalid priority numeric value: " + numericValue);
    }

    /**
     * Check if this priority is higher than another priority
     * 
     * @param other The priority to compare against
     * @return true if this priority is higher than the other
     */
    public boolean isHigherThan(Priority other) {
        return other != null && this.numericValue > other.numericValue;
    }

    /**
     * Check if this priority is lower than another priority
     * 
     * @param other The priority to compare against
     * @return true if this priority is lower than the other
     */
    public boolean isLowerThan(Priority other) {
        return other != null && this.numericValue < other.numericValue;
    }

    /**
     * Check if this priority is urgent (HIGH or URGENT)
     * 
     * @return true if the priority is HIGH or URGENT
     */
    public boolean isUrgent() {
        return this == HIGH || this == URGENT;
    }

    /**
     * Check if this priority is critical (URGENT only)
     * 
     * @return true if the priority is URGENT
     */
    public boolean isCritical() {
        return this == URGENT;
    }

    /**
     * Get the next higher priority level
     * 
     * @return The next higher priority, or null if already at highest
     */
    public Priority getHigherPriority() {
        switch (this) {
            case LOW:
                return MEDIUM;
            case MEDIUM:
                return HIGH;
            case HIGH:
                return URGENT;
            case URGENT:
            default:
                return null; // Already at highest priority
        }
    }

    /**
     * Get the next lower priority level
     * 
     * @return The next lower priority, or null if already at lowest
     */
    public Priority getLowerPriority() {
        switch (this) {
            case URGENT:
                return HIGH;
            case HIGH:
                return MEDIUM;
            case MEDIUM:
                return LOW;
            case LOW:
            default:
                return null; // Already at lowest priority
        }
    }

    /**
     * Get a color code associated with this priority for UI display
     * 
     * @return CSS-style color code
     */
    public String getColorCode() {
        switch (this) {
            case LOW:
                return "#28a745"; // Green
            case MEDIUM:
                return "#ffc107"; // Yellow
            case HIGH:
                return "#fd7e14"; // Orange
            case URGENT:
                return "#dc3545"; // Red
            default:
                return "#6c757d"; // Gray
        }
    }

    /**
     * Get an icon name associated with this priority for UI display
     * 
     * @return Icon name (Font Awesome style)
     */
    public String getIconName() {
        switch (this) {
            case LOW:
                return "fa-arrow-down";
            case MEDIUM:
                return "fa-minus";
            case HIGH:
                return "fa-arrow-up";
            case URGENT:
                return "fa-exclamation-triangle";
            default:
                return "fa-question";
        }
    }

    /**
     * Compare priorities for sorting (higher priority first)
     * 
     * @param other The priority to compare against
     * @return negative if this is lower priority, positive if higher, 0 if equal
     */
    public int compareTo(Priority other) {
        if (other == null) {
            return 1;
        }
        return Integer.compare(other.numericValue, this.numericValue); // Reverse order for higher-first sorting
    }

    @Override
    public String toString() {
        return value + " (" + description + ")";
    }
}
