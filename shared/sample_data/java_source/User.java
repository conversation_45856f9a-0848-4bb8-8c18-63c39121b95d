package com.todoservice.api;

import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

/**
 * User Data Type
 * 
 * Represents a user who can be assigned to todo items.
 * Contains basic user information including identification,
 * contact details, and display preferences.
 * 
 * <AUTHOR> Service Team
 * @version 1.0.0
 */
@XmlType(
    name = "UserType",
    namespace = "http://api.todoservice.com/types",
    propOrder = {"id", "username", "email", "fullName"}
)
@XmlRootElement(name = "User", namespace = "http://api.todoservice.com/types")
@XmlAccessorType(XmlAccessType.FIELD)
public class User {

    /**
     * Unique user identifier
     */
    @XmlElement(name = "id", namespace = "http://api.todoservice.com/types", required = true)
    protected Long id;

    /**
     * Unique username (3-30 characters, alphanumeric and underscore only)
     */
    @XmlElement(name = "username", namespace = "http://api.todoservice.com/types", required = true)
    protected String username;

    /**
     * User's email address
     */
    @XmlElement(name = "email", namespace = "http://api.todoservice.com/types", required = true)
    protected String email;

    /**
     * User's full display name (optional)
     */
    @XmlElement(name = "fullName", namespace = "http://api.todoservice.com/types")
    protected String fullName;

    /**
     * Default constructor
     */
    public User() {
    }

    /**
     * Constructor with required fields
     * 
     * @param id Unique user identifier
     * @param username Unique username
     * @param email User's email address
     */
    public User(Long id, String username, String email) {
        this.id = id;
        this.username = username;
        this.email = email;
    }

    /**
     * Constructor with all fields
     * 
     * @param id Unique user identifier
     * @param username Unique username
     * @param email User's email address
     * @param fullName User's full display name
     */
    public User(Long id, String username, String email, String fullName) {
        this(id, username, email);
        this.fullName = fullName;
    }

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    // Utility Methods

    /**
     * Get the display name for this user
     * Returns full name if available, otherwise username
     * 
     * @return The best available display name
     */
    public String getDisplayName() {
        return (fullName != null && !fullName.trim().isEmpty()) ? fullName : username;
    }

    /**
     * Get the user's initials for display purposes
     * 
     * @return User's initials (up to 3 characters)
     */
    public String getInitials() {
        String name = getDisplayName();
        if (name == null || name.trim().isEmpty()) {
            return "?";
        }

        String[] parts = name.trim().split("\\s+");
        StringBuilder initials = new StringBuilder();
        
        for (int i = 0; i < Math.min(parts.length, 3); i++) {
            if (!parts[i].isEmpty()) {
                initials.append(parts[i].charAt(0));
            }
        }
        
        return initials.toString().toUpperCase();
    }

    /**
     * Validate the username format
     * Username must be 3-30 characters, alphanumeric and underscore only
     * 
     * @param username The username to validate
     * @return true if the username is valid
     */
    public static boolean isValidUsername(String username) {
        if (username == null) {
            return false;
        }
        return username.matches("^[a-zA-Z0-9_]{3,30}$");
    }

    /**
     * Validate the email format
     * Basic email validation using regex
     * 
     * @param email The email to validate
     * @return true if the email format is valid
     */
    public static boolean isValidEmail(String email) {
        if (email == null) {
            return false;
        }
        return email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$") 
               && email.length() <= 254;
    }

    /**
     * Validate this user's data
     * 
     * @return true if all required fields are valid
     */
    public boolean isValid() {
        return id != null && 
               isValidUsername(username) && 
               isValidEmail(email);
    }

    /**
     * Get a formatted string representation for logging
     * 
     * @return Formatted user information (without sensitive data)
     */
    public String toLogString() {
        return String.format("User{id=%d, username='%s', email='%s'}", 
                           id, username, 
                           email != null ? email.replaceAll("(.{2}).*@", "$1***@") : null);
    }

    /**
     * Create a copy of this user with masked sensitive information
     * 
     * @return User copy with masked email
     */
    public User createMaskedCopy() {
        User masked = new User();
        masked.id = this.id;
        masked.username = this.username;
        masked.fullName = this.fullName;
        
        // Mask email address
        if (this.email != null) {
            int atIndex = this.email.indexOf('@');
            if (atIndex > 2) {
                masked.email = this.email.substring(0, 2) + "***" + this.email.substring(atIndex);
            } else {
                masked.email = "***" + this.email.substring(atIndex);
            }
        }
        
        return masked;
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", fullName='" + fullName + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return id != null && id.equals(user.id);
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
