package com.todoservice.api;

import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;

/**
 * Todo Status Enumeration
 * 
 * Defines the possible status values for a todo item throughout its lifecycle.
 * Each status represents a different stage in the todo item's workflow.
 * 
 * <AUTHOR> Service Team
 * @version 1.0.0
 */
@XmlType(name = "TodoStatusType", namespace = "http://api.todoservice.com/types")
@XmlEnum
public enum TodoStatus {

    /**
     * Todo item is created but not yet started
     * This is the default status for newly created todo items.
     */
    @XmlEnumValue("PENDING")
    PENDING("PENDING", "Todo item is created but not yet started"),

    /**
     * Todo item is currently being worked on
     * Indicates that someone has started working on this todo item.
     */
    @XmlEnumValue("IN_PROGRESS")
    IN_PROGRESS("IN_PROGRESS", "Todo item is currently being worked on"),

    /**
     * Todo item has been completed
     * The work for this todo item has been finished successfully.
     */
    @XmlEnumValue("COMPLETED")
    COMPLETED("COMPLETED", "Todo item has been completed"),

    /**
     * Todo item has been cancelled
     * The todo item is no longer needed and has been cancelled.
     */
    @XmlEnumValue("CANCELLED")
    CANCELLED("CANCELLED", "Todo item has been cancelled"),

    /**
     * Todo item is temporarily on hold
     * Work on this todo item has been paused temporarily.
     */
    @XmlEnumValue("ON_HOLD")
    ON_HOLD("ON_HOLD", "Todo item is temporarily on hold");

    private final String value;
    private final String description;

    /**
     * Constructor for TodoStatus enum
     * 
     * @param value The string value of the status
     * @param description Human-readable description of the status
     */
    TodoStatus(String value, String description) {
        this.value = value;
        this.description = description;
    }

    /**
     * Get the string value of this status
     * 
     * @return The string representation of the status
     */
    public String value() {
        return value;
    }

    /**
     * Get the description of this status
     * 
     * @return Human-readable description of what this status means
     */
    public String getDescription() {
        return description;
    }

    /**
     * Convert a string value to TodoStatus enum
     * 
     * @param value The string value to convert
     * @return The corresponding TodoStatus enum value
     * @throws IllegalArgumentException if the value is not a valid status
     */
    public static TodoStatus fromValue(String value) {
        if (value == null) {
            throw new IllegalArgumentException("Status value cannot be null");
        }
        
        for (TodoStatus status : TodoStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        
        throw new IllegalArgumentException("Invalid status value: " + value);
    }

    /**
     * Check if this status represents an active state
     * Active states are PENDING and IN_PROGRESS
     * 
     * @return true if the status is active (not completed, cancelled, or on hold)
     */
    public boolean isActive() {
        return this == PENDING || this == IN_PROGRESS;
    }

    /**
     * Check if this status represents a completed state
     * 
     * @return true if the status is COMPLETED
     */
    public boolean isCompleted() {
        return this == COMPLETED;
    }

    /**
     * Check if this status represents a terminal state
     * Terminal states are COMPLETED and CANCELLED
     * 
     * @return true if the status is terminal (no further work expected)
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == CANCELLED;
    }

    /**
     * Check if this status can transition to another status
     * 
     * @param newStatus The status to transition to
     * @return true if the transition is allowed
     */
    public boolean canTransitionTo(TodoStatus newStatus) {
        if (newStatus == null) {
            return false;
        }

        // Same status is always allowed
        if (this == newStatus) {
            return true;
        }

        switch (this) {
            case PENDING:
                // From PENDING, can go to any other status
                return true;
                
            case IN_PROGRESS:
                // From IN_PROGRESS, can go to any status except PENDING
                return newStatus != PENDING;
                
            case ON_HOLD:
                // From ON_HOLD, can go back to PENDING or IN_PROGRESS, or to terminal states
                return newStatus == PENDING || newStatus == IN_PROGRESS || 
                       newStatus == COMPLETED || newStatus == CANCELLED;
                
            case COMPLETED:
                // From COMPLETED, can only reopen to PENDING or IN_PROGRESS
                return newStatus == PENDING || newStatus == IN_PROGRESS;
                
            case CANCELLED:
                // From CANCELLED, can only reopen to PENDING
                return newStatus == PENDING;
                
            default:
                return false;
        }
    }

    /**
     * Get the next logical status in the workflow
     * 
     * @return The next status in the typical workflow, or null if terminal
     */
    public TodoStatus getNextStatus() {
        switch (this) {
            case PENDING:
                return IN_PROGRESS;
            case IN_PROGRESS:
                return COMPLETED;
            case ON_HOLD:
                return IN_PROGRESS;
            case COMPLETED:
            case CANCELLED:
            default:
                return null; // Terminal states have no next status
        }
    }

    /**
     * Get all valid statuses that this status can transition to
     * 
     * @return Array of valid target statuses
     */
    public TodoStatus[] getValidTransitions() {
        switch (this) {
            case PENDING:
                return new TodoStatus[]{IN_PROGRESS, ON_HOLD, COMPLETED, CANCELLED};
            case IN_PROGRESS:
                return new TodoStatus[]{ON_HOLD, COMPLETED, CANCELLED};
            case ON_HOLD:
                return new TodoStatus[]{PENDING, IN_PROGRESS, COMPLETED, CANCELLED};
            case COMPLETED:
                return new TodoStatus[]{PENDING, IN_PROGRESS};
            case CANCELLED:
                return new TodoStatus[]{PENDING};
            default:
                return new TodoStatus[0];
        }
    }

    @Override
    public String toString() {
        return value + " (" + description + ")";
    }
}
