openapi: 3.0.3
info:
  title: Todo Service API
  description: |
    A comprehensive REST API for managing todo items with full CRUD operations,
    search functionality, user management, and advanced features like priorities,
    due dates, and task assignments.
    
    ## Features
    - Create, read, update, and delete todo items
    - Advanced search and filtering capabilities
    - User assignment and management
    - Priority levels and due date tracking
    - Tag-based categorization
    - Pagination support for large datasets
    
  version: 1.0.0
  contact:
    name: Todo Service API Support
    email: <EMAIL>
    url: https://api.todoservice.com/support
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://api.todoservice.com/terms

servers:
  - url: https://api.todoservice.com/v1
    description: Production server
  - url: https://staging-api.todoservice.com/v1
    description: Staging server
  - url: http://localhost:8080/v1
    description: Local development server

security:
  - ApiKeyAuth: []
  - BearerAuth: []

paths:
  /todos:
    get:
      summary: Search and list todo items
      description: |
        Retrieve a list of todo items with optional filtering and pagination.
        Supports various search criteria including status, priority, assignee, and date ranges.
      operationId: searchTodos
      tags:
        - Todos
      parameters:
        - name: status
          in: query
          description: Filter by todo status (multiple values allowed)
          required: false
          style: form
          explode: true
          schema:
            type: array
            items:
              $ref: '#/components/schemas/TodoStatus'
        - name: priority
          in: query
          description: Filter by priority level (multiple values allowed)
          required: false
          style: form
          explode: true
          schema:
            type: array
            items:
              $ref: '#/components/schemas/Priority'
        - name: assigneeId
          in: query
          description: Filter by assigned user ID
          required: false
          schema:
            type: integer
            format: int64
            example: 123
        - name: dueBefore
          in: query
          description: Filter items due before this date (ISO 8601 format)
          required: false
          schema:
            type: string
            format: date-time
            example: "2024-12-31T23:59:59Z"
        - name: dueAfter
          in: query
          description: Filter items due after this date (ISO 8601 format)
          required: false
          schema:
            type: string
            format: date-time
            example: "2024-01-01T00:00:00Z"
        - name: search
          in: query
          description: Search text in title and description
          required: false
          schema:
            type: string
            maxLength: 255
            example: "meeting"
        - name: tags
          in: query
          description: Filter by tags (multiple values allowed)
          required: false
          style: form
          explode: true
          schema:
            type: array
            items:
              type: string
              maxLength: 50
        - name: limit
          in: query
          description: Maximum number of items to return
          required: false
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
            example: 20
        - name: offset
          in: query
          description: Number of items to skip (for pagination)
          required: false
          schema:
            type: integer
            minimum: 0
            default: 0
            example: 0
        - name: sortBy
          in: query
          description: Field to sort by
          required: false
          schema:
            type: string
            enum: [id, title, status, priority, dueDate, createdAt, updatedAt]
            default: createdAt
        - name: sortOrder
          in: query
          description: Sort order
          required: false
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: List of todo items matching the search criteria
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TodoListResponse'
              examples:
                successful_search:
                  summary: Successful search with results
                  value:
                    todos:
                      - id: 1
                        title: "Complete project documentation"
                        description: "Write comprehensive API documentation"
                        status: "IN_PROGRESS"
                        priority: "HIGH"
                        dueDate: "2024-01-15T17:00:00Z"
                        createdAt: "2024-01-01T10:00:00Z"
                        updatedAt: "2024-01-10T14:30:00Z"
                        assignee:
                          id: 123
                          username: "john_doe"
                          email: "<EMAIL>"
                          fullName: "John Doe"
                        tags: ["documentation", "urgent"]
                    totalCount: 1
                    hasMore: false
                    limit: 20
                    offset: 0
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create a new todo item
      description: |
        Create a new todo item. The ID will be automatically generated.
        All fields except title are optional.
      operationId: createTodo
      tags:
        - Todos
      requestBody:
        description: Todo item to create
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TodoCreateRequest'
            examples:
              simple_todo:
                summary: Simple todo item
                value:
                  title: "Buy groceries"
                  description: "Get milk, bread, and eggs"
                  priority: "MEDIUM"
              complex_todo:
                summary: Complex todo with all fields
                value:
                  title: "Prepare quarterly report"
                  description: "Compile Q4 financial data and create presentation"
                  status: "PENDING"
                  priority: "HIGH"
                  dueDate: "2024-01-31T17:00:00Z"
                  assigneeId: 123
                  tags: ["finance", "quarterly", "report"]
      responses:
        '201':
          description: Todo item created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TodoResponse'
              examples:
                created_todo:
                  summary: Successfully created todo
                  value:
                    id: 42
                    title: "Buy groceries"
                    description: "Get milk, bread, and eggs"
                    status: "PENDING"
                    priority: "MEDIUM"
                    dueDate: null
                    createdAt: "2024-01-15T10:30:00Z"
                    updatedAt: "2024-01-15T10:30:00Z"
                    assignee: null
                    tags: []
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /todos/{todoId}:
    parameters:
      - name: todoId
        in: path
        description: Unique identifier of the todo item
        required: true
        schema:
          type: integer
          format: int64
          minimum: 1
          example: 42

    get:
      summary: Get a specific todo item
      description: Retrieve a single todo item by its unique identifier
      operationId: getTodo
      tags:
        - Todos
      responses:
        '200':
          description: Todo item found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TodoResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      summary: Update a todo item
      description: |
        Update an existing todo item. All fields are optional except the ID.
        Only provided fields will be updated.
      operationId: updateTodo
      tags:
        - Todos
      requestBody:
        description: Updated todo item data
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TodoUpdateRequest'
      responses:
        '200':
          description: Todo item updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TodoResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '422':
          $ref: '#/components/responses/ValidationError'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Delete a todo item
      description: Permanently delete a todo item by its unique identifier
      operationId: deleteTodo
      tags:
        - Todos
      responses:
        '204':
          description: Todo item deleted successfully
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /users:
    get:
      summary: List users
      description: Get a list of all users who can be assigned to todo items
      operationId: getUsers
      tags:
        - Users
      parameters:
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 50
        - name: offset
          in: query
          schema:
            type: integer
            minimum: 0
            default: 0
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /users/{userId}:
    parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: integer
          format: int64
          minimum: 1

    get:
      summary: Get user details
      description: Retrieve details of a specific user
      operationId: getUser
      tags:
        - Users
      responses:
        '200':
          description: User details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for authentication
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

  schemas:
    Todo:
      type: object
      description: A todo item with all its properties
      required:
        - title
        - status
      properties:
        id:
          type: integer
          format: int64
          description: Unique identifier (auto-generated)
          readOnly: true
          example: 42
        title:
          type: string
          description: Brief title or summary of the todo item
          minLength: 1
          maxLength: 255
          example: "Complete project documentation"
        description:
          type: string
          description: Detailed description of the todo item
          maxLength: 2000
          example: "Write comprehensive API documentation including examples and use cases"
        status:
          $ref: '#/components/schemas/TodoStatus'
        priority:
          $ref: '#/components/schemas/Priority'
        dueDate:
          type: string
          format: date-time
          description: Due date and time for the todo item
          example: "2024-01-31T17:00:00Z"
        createdAt:
          type: string
          format: date-time
          description: Timestamp when the todo item was created
          readOnly: true
          example: "2024-01-01T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Timestamp when the todo item was last updated
          readOnly: true
          example: "2024-01-15T14:30:00Z"
        assignee:
          $ref: '#/components/schemas/User'
        tags:
          type: array
          description: List of tags for categorizing the todo item
          items:
            type: string
            pattern: '^[a-zA-Z0-9\-_\s]{1,50}$'
            maxLength: 50
          maxItems: 20
          example: ["documentation", "urgent", "api"]

    TodoStatus:
      type: string
      description: Current status of the todo item
      enum:
        - PENDING
        - IN_PROGRESS
        - COMPLETED
        - CANCELLED
        - ON_HOLD
      example: "IN_PROGRESS"

    Priority:
      type: string
      description: Priority level of the todo item
      enum:
        - LOW
        - MEDIUM
        - HIGH
        - URGENT
      example: "HIGH"

    User:
      type: object
      description: User who can be assigned to todo items
      required:
        - id
        - username
        - email
      properties:
        id:
          type: integer
          format: int64
          description: Unique user identifier
          example: 123
        username:
          type: string
          description: Unique username
          pattern: '^[a-zA-Z0-9_]{3,30}$'
          minLength: 3
          maxLength: 30
          example: "john_doe"
        email:
          type: string
          format: email
          description: User's email address
          maxLength: 254
          example: "<EMAIL>"
        fullName:
          type: string
          description: User's full display name
          maxLength: 100
          example: "John Doe"

    TodoCreateRequest:
      type: object
      description: Request payload for creating a new todo item
      required:
        - title
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
          example: "Complete project documentation"
        description:
          type: string
          maxLength: 2000
          example: "Write comprehensive API documentation"
        status:
          $ref: '#/components/schemas/TodoStatus'
        priority:
          $ref: '#/components/schemas/Priority'
        dueDate:
          type: string
          format: date-time
          example: "2024-01-31T17:00:00Z"
        assigneeId:
          type: integer
          format: int64
          description: ID of the user to assign this todo to
          example: 123
        tags:
          type: array
          items:
            type: string
            maxLength: 50
          maxItems: 20
          example: ["documentation", "urgent"]

    TodoUpdateRequest:
      type: object
      description: Request payload for updating an existing todo item
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 255
        description:
          type: string
          maxLength: 2000
        status:
          $ref: '#/components/schemas/TodoStatus'
        priority:
          $ref: '#/components/schemas/Priority'
        dueDate:
          type: string
          format: date-time
          nullable: true
        assigneeId:
          type: integer
          format: int64
          nullable: true
        tags:
          type: array
          items:
            type: string
            maxLength: 50
          maxItems: 20

    TodoResponse:
      allOf:
        - $ref: '#/components/schemas/Todo'
        - type: object
          description: Response containing a single todo item

    TodoListResponse:
      type: object
      description: Response containing a list of todo items with pagination info
      required:
        - todos
        - totalCount
        - hasMore
        - limit
        - offset
      properties:
        todos:
          type: array
          description: Array of todo items
          items:
            $ref: '#/components/schemas/Todo'
        totalCount:
          type: integer
          description: Total number of todo items matching the criteria
          minimum: 0
          example: 150
        hasMore:
          type: boolean
          description: Indicates if there are more results available
          example: true
        limit:
          type: integer
          description: Maximum number of items returned in this response
          minimum: 1
          example: 20
        offset:
          type: integer
          description: Number of items skipped
          minimum: 0
          example: 0

    UserListResponse:
      type: object
      description: Response containing a list of users
      required:
        - users
        - totalCount
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
        totalCount:
          type: integer
          minimum: 0
        limit:
          type: integer
          minimum: 1
        offset:
          type: integer
          minimum: 0

    Error:
      type: object
      description: Standard error response
      required:
        - code
        - message
        - timestamp
      properties:
        code:
          type: string
          description: Error code identifying the type of error
          example: "VALIDATION_ERROR"
        message:
          type: string
          description: Human-readable error message
          example: "The title field is required"
        timestamp:
          type: string
          format: date-time
          description: When the error occurred
          example: "2024-01-15T10:30:00Z"
        details:
          type: object
          description: Additional error details
          additionalProperties: true

    ValidationError:
      allOf:
        - $ref: '#/components/schemas/Error'
        - type: object
          properties:
            fieldErrors:
              type: array
              description: Field-specific validation errors
              items:
                type: object
                properties:
                  field:
                    type: string
                    example: "title"
                  message:
                    type: string
                    example: "Title is required"
                  rejectedValue:
                    description: The value that was rejected

  responses:
    BadRequest:
      description: Bad request - invalid input
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "BAD_REQUEST"
            message: "Invalid request parameters"
            timestamp: "2024-01-15T10:30:00Z"

    Unauthorized:
      description: Unauthorized - authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "UNAUTHORIZED"
            message: "Authentication required"
            timestamp: "2024-01-15T10:30:00Z"

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "NOT_FOUND"
            message: "Todo item not found"
            timestamp: "2024-01-15T10:30:00Z"

    ValidationError:
      description: Validation error - invalid data
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ValidationError'
          example:
            code: "VALIDATION_ERROR"
            message: "Validation failed"
            timestamp: "2024-01-15T10:30:00Z"
            fieldErrors:
              - field: "title"
                message: "Title is required"
                rejectedValue: ""

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            code: "INTERNAL_ERROR"
            message: "An unexpected error occurred"
            timestamp: "2024-01-15T10:30:00Z"

tags:
  - name: Todos
    description: Operations for managing todo items
  - name: Users
    description: Operations for managing users
