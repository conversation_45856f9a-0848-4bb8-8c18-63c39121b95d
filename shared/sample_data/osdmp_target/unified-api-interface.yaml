openapi: 3.0.3

info:
  title: SilverRail API
  version: 1.0.43
  description: |
    Compatible with OSDM version 3.2.0
    
    The OSDM specification supports two modes of operation: Retailer Mode 
    and Distributor Mode. The API works identically in both modes, except that in distributor mode the API also 
    returns fare information.

    The following resources are key to get started:

      -  [Processes](https://osdm.io/spec/processes/)
      -  [Models](https://osdm.io/spec/models/)
      -  [Getting started](https://osdm.io/spec/getting-started/)

  contact:
    name: SilverRail Technologies
    url: https://silverrailtech.com/
    email: <EMAIL>
  license:
    name: Apache 2.0
    url: https://www.apache.org/licenses/LICENSE-2.0.html

# SilverRail Change - Multi-line formatting removed for spring generator
tags:
  - name: Places
    description: resources to search for places
  - name: Trips
    description: resources to search for trips
  - name: Offers
    description: Resources to search for available travel offers
  - name: Availabilities
    description: resources to query availabilities before or after pre-booking
  - name: BookingPart
    description: resources to add and remove offer parts to a pre-booked booking
  - name: Bookings
    description: resources to manipulate bookings
  - name: BookedOffers
    description: resources to manipulate bookings
  - name: Bookings Search
    description: resources to search for bookings
  - name: Bookings Split
    description: resources to split a booking
  - name: Purchaser
    description: resources to manipulate a purchaser's information at every stage of the flow
  - name: Passengers
    description: resources to manipulate a passenger's information at every stage of the flow
  - name: Fulfillments
    description: resources to retrieve fulfillments, e.g. tickets
  - name: Refund
    description: resources to get and accept a refund offer
  - name: Exchange
    description: resources to get exchange offers and book it
  - name: On Hold
    description: resources to put a pre-booking on hold for a given time limit
  - name: Release
    description: resources to release
  - name: Complaint Management
    description: resources to manage complaints
  - name: Cancel Fulfillments
    description: resources to cancel fulfillments
  - name: Booking Documents
    description: resources to get documents
  - name: Reimbursement Management
    description: resources to manage reimbursements
  - name: Travel Account
    description: resource to get a travel account
  - name: Master Data
    description: resources to get master data

paths:
  /places:
    get:
      tags:
        - Master Data
      summary: Returns all places.
      operationId: getPlaces
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/ifNoneMatch'
        - name: page
          in: query
          description: | # SilverRail change: more detailed description
            Can be used for pagination. When not given, the service returns the first page.
            Clients should not generate page ids themselves. Instead they should follow the
            entry in response _links object with rel=next.
            That link expires when the data on the server changes. This ensures that clients
            do not combine inconsistent pages into a corrupted data set. An expired page
            will return HTTP 400 error. Typically links have a fairly long lifetime,
            however there is no guarantee of any specific minimum lifetime.
          schema:
            type: string
      responses:
        '200':
          description: |
            places found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlaceResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.
            ETag:
              schema:
                type: string
              description: version tag
        '303':
          $ref: '#/components/responses/SeeOtherResponse'
        '304':
          $ref: '#/components/responses/NotModifiedResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    post:
      tags:
        - Places
      summary: returns place information for a given place request
      description: |
        Returns places for a given place request based on the OJP specification.
      operationId: postPlaces
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
      requestBody:
        description: |
          request for place
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlaceRequest'
      responses:
        '200':
          description: |
            places found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlaceResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /places/{placeId}:
    get:
      tags:
        - Places
      summary: returns a place
      operationId: getPlacesId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: placeId
          in: path
          description: |
            id of the place to get.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            place found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlaceResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /trips-collection:
    post:
      tags:
        - Trips
      summary: returns a collection of trips for a given OJP trip request
      description: |
        Returns trips for a given trip request based on the OJP specification.
      operationId: postTrips
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
      requestBody:
        description: |
          request for trips
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TripSearchCriteria'
      responses:
        '200':
          description: |
            trips found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TripCollectionResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /trips-collections/{tripsCollectionId}:
    get:
      tags:
        - Trips
      summary: Returns a collection of trips.
      description: |
        Returns a collection of trips for a for a given trips collection id.
        The unique codes of the origin and destination can be resolved using the places service.
      operationId: getTripsCollectionId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: tripsCollectionId
          in: path
          description: |
            id of the trips
          required: true
          schema:
            type: string
        - name: page
          in: query
          schema:
            type: string
        - name: embed
          in: query
          description: |
            Influences whether referenced resources are returned in full or as references only.
            Default value: ALL
          schema:
            type: array
            maxItems: 2
            items:
              $ref: '#/components/schemas/TripsCollectionResponseContent'
      responses:
        '200':
          description: |
            trips found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TripCollectionResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /trips/{tripId}:
    get:
      tags:
        - Trips
      summary: Returns a trip element representing a travel trip.
      description: |
        A trip consists of one or more legs for a given tripId. Depending on the
        embed either references or full location definitions is returned.
      operationId: getTripsId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: tripId
          in: path
          description: |
            id of the trip to get.
          required: true
          schema:
            type: string
        - name: stopBehavior
          in: query
          description: |
            Influences what stops are to be returned in response
            (ORIGIN_DESTINATION_ONLY returns no intermediate stops;
            REAL_BOARDING_ALIGHTING returns all stops except virtual stops).
            Default value: ORIGIN_DESTINATION_ONLY
          schema:
            $ref: '#/components/schemas/StopBehavior'
        - name: embed
          in: query
          description: |
            Influences whether referenced resources are returned in full or as references only.
            Default value: ALL
          schema:
            type: array
            maxItems: 1
            items:
              $ref: '#/components/schemas/TripResponseContent'
      responses:
        '200':
          description: |
            trip found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TripResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /offers:
    post:
      tags:
        - Offers
      summary: Returns travel offers matching the search criteria.
      description: |
        Returns available travel offers based on user-specified search criteria
        such as origin, destination, departure time, and passenger details.
      operationId: createOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OfferCollectionRequest'
      responses:
        '200':
          description: |
            A collection of offers matching the search criteria.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OfferCollectionResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/booked-offers/{bookedOfferId}/additional-offers:
    get:
      tags:
        - Offers
      summary: Get additional offers of booked offer for a given booking.
      operationId: getBookingBookedOffersAdditionalOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: bookedOfferId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            additional offers found for booked offer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdditionalOfferCollectionResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/on-hold-offer:
    post:
      tags:
        - On Hold
      summary: Creates an on hold offer.
      description: |
        On hold offer created
      operationId: createOnHoldOffer
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnHoldOfferRequest'
      responses:
        '200':
          description: |
            on-hold offer created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnHoldOfferResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/on-hold-offer/{onHoldOfferId}:
    patch:
      tags:
        - On Hold
      summary: Confirms an on hold offer.
      operationId: confirmOnHoldOffer
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: onHoldOfferId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnHoldOfferPatchRequest'
      responses:
        '200':
          description: |
            on-hold offer confirmed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnHoldOfferResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/passengers/{passengerId}:
    get:
      tags:
        - Passengers
      summary: Returns the passenger's information at booking step.
      operationId: getBookingPassengersId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: passengerId
          in: path
          description: |
            id of the passenger
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            passenger found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PassengerResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Passengers
      summary: Allows updating a passenger's information at booking step.
      operationId: patchBookingPassenger
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking the passenger is in.
          required: true
          schema:
            type: string
        - name: passengerId
          in: path
          description: |
            id of the passenger
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Passenger'
      responses:
        '200':
          description: |
            passenger successfully patched
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PassengerResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/purchaser:
    get:
      tags:
        - Purchaser
      summary: Returns the purchaser's information at booking step.
      operationId: getBookingPurchaser
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          description: |
            id of the booking the purchaser is in.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            purchaser found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaserResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Purchaser
      summary: Allows updating a purchaser's information at booking step.
      operationId: patchBookingPurchaser
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking the purchaser is in.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Purchaser'
      responses:
        '200':
          description: |
            purchaser patched
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaserResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'

  /bookings/{bookingId}/booked-offers:
    post:
      tags:
        - BookedOffers
      summary: Creates bookedOffers from offers and adds them in a booking.
      operationId: postBookingBookedOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookedOfferRequest'
      responses:
        '200':
          description: |
            booking pre-booked
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookedOfferResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'

  /bookings/{bookingId}/booked-offers/{bookedOfferId}:
    get:
      tags:
        - BookedOffers
      summary: Gets a bookedOffer of a booking.
      operationId: getBookingBookedOffersId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: bookedOfferId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            bookedOffer found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookedOfferResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    delete:
      tags:
        - BookedOffers
      summary: Delete a bookedOffer from a booking.
      operationId: deleteBookingBookedOffersId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: bookedOfferId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/booked-offers/{offerId}/passengers/{passengerId}:
    delete:
      tags:
        - BookedOffers
      summary: Delete a passenger from bookedOffer of a booking.
      description: |
        Deletes all booked offer parts of the passenger only if this valid from a tariff point of view. This implies that the offer part are sufficiently partitioned and no repricing is needed.
      operationId: deleteBookingBookedOffersIdPassengerId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: offerId
          in: path
          required: true
          schema:
            type: string
        - name: passengerId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/booked-offers/{bookedOfferId}/reservations:
    post:
      tags:
        - BookingPart
      summary: Adds an optional reservation to a pre-booked booking.
      operationId: createBookingBookedOffersReservations
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: bookedOfferId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookedOfferReservationRequest'
      responses:
        '200':
          description: |
            reservation added to booked offer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookedOfferReservationResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/booked-offers/{bookedOfferId}/reservations/{reservationId}:
    delete:
      tags:
        - BookingPart
      summary: Removes an optional reservation from pre-booked booking.
      operationId: deleteBookingBookedOffersReservations
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: bookedOfferId
          in: path
          required: true
          schema:
            type: string
        - name: reservationId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/booked-offers/{bookedOfferId}/ancillaries:
    post:
      tags:
        - BookingPart
      summary: Adds an ancillary to a pre-booked booking.
      operationId: createBookingBookedOffersAncillaries
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: bookedOfferId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookedOfferAncillaryRequest'
      responses:
        '200':
          description: |
            ancillary added to booked offer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookedOfferAncillaryResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/booked-offers/{bookedOfferId}/ancillaries/{ancillaryId}:
    delete:
      tags:
        - BookingPart
      summary: Removes an ancillary from pre-booked booking.
      operationId: deleteBookingBookedOffersAncillary
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: bookedOfferId
          in: path
          required: true
          schema:
            type: string
        - name: ancillaryId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/booked-offers/{bookedOfferId}/admissions/{admissionId}:
    delete:
      tags:
        - BookingPart
      summary: Removes an admission from pre-booked booking.
      description: |
        Removes an admission from pre-booked booking.It is up to the provider
        to change or remove dependent bookedOfferParts or to reject the request.
        A repricing might occur.
      operationId: deleteBookingBookedOffersAdmission
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: bookedOfferId
          in: path
          required: true
          schema:
            type: string
        - name: admissionId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings:
    post:
      tags:
        - Bookings
      summary: Creates a booking based on a previously requested offer.
      operationId: postBookings
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingRequest'
      responses:
        '200':
          description: |
            booking pre-booked
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookingResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}:
    get:
      tags:
        - Bookings
      summary: Returns a booking.
      operationId: getBookingsId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          description: |
            id of the booking to get.
          required: true
          schema:
            type: string
        - name: embed
          in: query
          description: |
            Influences whether referenced resources are returned in full or as references only.
            Default value: ALL
          schema:
            type: array
            maxItems: 7
            items:
              $ref: '#/components/schemas/BookingResponseContent'
      responses:
        '200':
          description: |
            booking found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookingResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Bookings
      summary: Updates a booking but does *not* confirm the booking.
      description: |
        Updates fulfillment types, place selection and add payment. Does *not* confirm the booking. The booking is confirmed by calling 'POST /bookings/{bookingId}/fulfillments'.

      operationId: updateBooking
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking to be patched
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingPatchRequest'
      responses:
        '200':
          description: |
            booking fulfillment type updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookingResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    delete:
      tags:
        - Bookings
      summary: Deletes a booking.
      description: |
        It is only possible before the booking is confirmed or in case of a technical problem in confirming multiple
        independent bookings within a sales transaction. Deletes on a confirmed booking must be documented
        and evidence on the issue must be provided on request.
        The delete on a confirmed booking is allowed immediately after the confirmation of the booking,
        but must be repeated according to the error handling rules in case the delete fails.
      operationId: deleteBookingsId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking to delete.
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'

  /bookings/{bookingId}/cleanup:
    post:
      tags:
        - Bookings
      summary: Performs a complete cleanup of a booking in a single step
      description: |
        The booking is cleaned up completely: confirmed items are refunded, and unconfirmed items are deleted.
      operationId: postBookingCleanup
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking.
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingCleanupRequest'
      responses:
        '202':
          $ref: '#/components/responses/CleanupRequestAcceptedResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/history:
    get:
      tags:
        - Bookings
      summary: Returns the history of changes to a booking.
      operationId: getBookingsIdHistory
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          description: |
            id of the booking to get.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            booking history found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookingHistoryResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings-search:
    post:
      tags:
        - Bookings Search
      summary: Search for bookings based on search parameters.
      operationId: searchBookings
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: page
          in: query
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingSearchRequest'
      responses:
        '200':
          description: |
            booking search results found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookingSearchResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/split:
    post:
      tags:
        - Bookings Split
      summary: Split a booking into multiple bookings.
      description: |
        Splits a booking into a set of bookings according to the defined groups of a passenger(s). Only possible if allowed by the underlying tariff, if the bookedOffers are sufficiently partitioned and no pricing is needed.
      operationId: splitBookings
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking to be split
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BookingSplitRequest'
      responses:
        '200':
          description: |
            booking split
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BookingSplitResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/fulfillments:
    post:
      tags:
        - Fulfillments
      summary:
        Confirms the booking a triggers the fulfillment of the booking
        synchronously or asynchronously.
      description: |
        Confirms the booking and triggers the fulfillment of the booking.

        If the fulfillments are created synchronously the service directly returns the fulfillments. The fulfillments are then in state FULFILLED.

        If the fulfillments are created asynchronously the service starts the creation of the fulfillments. No fulfillmentIds are returned. The fulfillments are in stage CONFIRMED. The booking needs to be retrieved later to obtain the fulfillments.
      operationId: postFulfillments
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking to be patched
          required: true
          schema:
            type: string
      requestBody: # SilverRail change
        required: false
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FulfillmentPostRequest'

      responses:
        '200':
          description: |
            Fulfillment successfully completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FulfillmentCollectionResponse'
        '202':
          $ref: '#/components/responses/FulfillmentRequestAcceptedResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Fulfillments
      summary: Finalizes the fulfillments in asynchronous mode.
      description: |
        Finalizes the fulfillment in case of an asynchronous fulfillment mode.
      operationId: finalizeFulfillments
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking to be patched
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FulfillmentPatchRequest'
      responses:
        '200':
          description: |
            Fulfillment successfully completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FulfillmentCollectionResponse'
        '202':
          $ref: '#/components/responses/FulfillmentRequestAcceptedResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /fulfillments/{fulfillmentId}:
    get:
      tags:
        - Fulfillments
      summary: Returns the fulfillment, aka. ticket for the provided id.
      operationId: getFulfillmentId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: fulfillmentId
          in: path
          description: |
            id of the fulfillment to get.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            fulfillment found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FulfillmentResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Fulfillments
      summary: Activates a fulfillment, i.e. changes the status to AVAILABLE.
      description: |
        Changes the fulfillment to status AVAILABLE. In the case of multi-journey product, one of the fulfillment is now 'activated' and can be used to travel.
      operationId: patchFulfillmentId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: fulfillmentId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FulfillmentActivationPatchRequest'
      responses:
        '200':
          description: |
            fulfillment patched
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FulfillmentResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/refund-offers:
    post:
      tags:
        - Refund
      summary: Initiates a refund process by creating a refundOffer resource.
      description: |
        The RefundOffer contains the required information on the potential operation. One refund offer can then be
        accepted via a PATCH, deleted or left to die at the end of its lifetime.
      operationId: postRefundOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking.
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefundOfferRequest'
      responses:
        '200':
          description: |
            refund offer created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefundOfferCollectionResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/refund-offers/{refundOfferId}:
    get:
      tags:
        - Refund
      summary: Returns the refund offer for the ids provided.
      operationId: getRefundOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: refundOfferId
          in: path
          description: |
            id of the refund offer to get.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            refund offer found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefundOfferResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Refund
      summary: Allows to accept and confirm a refund offer.
      operationId: patchRefundOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: refundOfferId
          in: path
          description: |
            id of the refund offer
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefundOfferPatchRequest'
      responses:
        '200':
          description: |
            refund offer confirmed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefundOfferResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    delete:
      tags:
        - Refund
      summary: Deletes a refundOffer without waiting for expiry.
      operationId: deleteRefundOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: refundOfferId
          in: path
          description: |
            id of the refund offer
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            refund offer deleted

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/release-offers:
    post:
      tags:
        - Release
      summary: Initiates a release process by creating a releaseOffers resource.
      operationId: postReleaseOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking.
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReleaseOfferRequest'
      responses:
        '200':
          description: |
            release offer created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReleaseOfferCollectionResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/release-offers/{releaseOfferId}:
    get:
      tags:
        - Release
      summary: Returns the release offer for the ids provided.
      operationId: getReleaseOffer
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: releaseOfferId
          in: path
          description: |
            id of the refund offer to get.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            release offer found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReleaseOfferResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Release
      summary: Allows to accept and confirm a release offer.
      operationId: patchReleaseOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: releaseOfferId
          in: path
          description: |
            id of the refund offer
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReleaseOfferPatchRequest'
      responses:
        '200':
          description: |
            release offer confirmed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReleaseOfferResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    delete:
      tags:
        - Release
      summary: Deletes a release offer without waiting for expiry.
      operationId: deleteReleaseOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: releaseOfferId
          in: path
          description: |
            id of the refund offer
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            release offer deleted

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/cancel-fulfillments-offers:
    post:
      tags:
        - Cancel Fulfillments
      summary: Initiates a cancel fulfillments process
      description: |
        Initiates a cancel fulfillments process by creating a
        cancelFulfillmentsOffers resource.
      operationId: postCancelFulfillmentsOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelFulfillmentsOfferRequest'
      responses:
        '200':
          description: |
            Refund offer created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelFulfillmentsOfferCollectionResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/cancel-fulfillments-offers/{cancelFulfillmentsOfferId}:
    get:
      tags:
        - Cancel Fulfillments
      summary: Returns the cancel fulfillments offer for the ids provided.
      operationId: getCancelFulfillmentOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: cancelFulfillmentsOfferId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            RefundOffer found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelFulfillmentsOfferResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Cancel Fulfillments
      summary: Allows to accept and confirm a cancel fulfillments offer.
      operationId: patchCancelFulfillmentsOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: cancelFulfillmentsOfferId
          in: path
          description: |
            id of the refund offer
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelFulfillmentsOfferPatchRequest'
      responses:
        '200':
          description: |
            RefundOffer confirmed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CancelFulfillmentsOfferResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    delete:
      tags:
        - Cancel Fulfillments
      summary: Deletes a cancel fulfillments offer without waiting for expiry.
      operationId: deleteCancelFulfillmentOffers
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
        - name: cancelFulfillmentsOfferId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/exchange-offers:
    post:
      tags:
        - Exchange
      summary: Returns exchange offers for specified fulfillments
      description: |
        Returns exchange offers for specified fulfillments submitted given
        requested new trip characteristics.
      operationId: createExchangeOffersCollection
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: embed
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/ExchangeOfferCollectionResponseContent'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExchangeOfferCollectionRequest'
      responses:
        '200':
          description: |
            Collection of exchange offers found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExchangeOfferCollectionResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/exchange-operations:
    post:
      tags:
        - Exchange
      summary: Pre-books an exchangeOffer as part of an exchange operation.
      operationId: createBookingsExchangeOperations
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExchangeOperationRequest'
      responses:
        '200':
          description: |
            exchange offer pre-booked
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExchangeOperationResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/exchange-operations/{exchangeOperationId}:
    get:
      tags:
        - Exchange
      summary: Returns the exchange operation with the id provided.
      description: |
        Returns the exchange operation with the id provided. It may be a
        provisional or a confirmed exchange.
      operationId: getBookingsExchangeOperations
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          description: |
            id of the booking
          required: true
          schema:
            type: string
            nullable: false
        - name: exchangeOperationId
          in: path
          description: |
            id of the exchange operation
          required: true
          schema:
            type: string
        - name: embed
          in: query
          description: |
            Influences whether referenced resources are returned in full or as references only.
            Default value: ALL
          schema:
            type: array
            maxItems: 5
            items:
              $ref: '#/components/schemas/ExchangeOperationResponseContent'
      responses:
        '200':
          description: |
            ExchangeOperation found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExchangeOperationResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Exchange
      summary: Allows to update an ongoing exchange operation.
      operationId: updateBookingsExchangeOperations
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking to be exchanged.
          required: true
          schema:
            type: string
            nullable: false
        - name: exchangeOperationId
          in: path
          description: |
            id of the exchange operation.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExchangeOperationPatchRequest'
      responses:
        '200':
          description: |
            Exchange successfully completed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ExchangeOperationResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    delete:
      tags:
        - Exchange
      summary: Cancels an ongoing exchange operation in provisional state.
      operationId: deleteBookingsExchangeOperation
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          description: |
            id of the booking containing the exchange operation
          required: true
          schema:
            type: string
        - name: exchangeOperationId
          in: path
          description: |
            id of the exchangeOperation to delete.
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /complaints:
    post:
      tags:
        - Complaint Management
      summary: Allows adding a complaint.
      description: |
        Create a complaint request for part of a booking.
      operationId: createComplaint
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Complaint'
      responses:
        '200':
          description: |
            complaint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplaintResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /complaints/{complaintId}:
    get:
      tags:
        - Complaint Management
      summary: Returns a complaint.
      description: |
        Get a complaint including its current state.
      operationId: getComplaint
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: complaintId
          in: path
          description: |
            id of the complaint
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            the requested complaint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplaintResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Complaint Management
      summary: Allows updating a complaint.
      description: |
        Update a complaint request, i.e add missing documents or change state.
      operationId: patchComplaint
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: complaintId
          in: path
          description: |
            id of the complaint to be patched
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComplaintPatchRequest'
      responses:
        '200':
          description: |
            complaint updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplaintResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /coach-layouts:
    get:
      tags:
        - Master Data
      summary: Returns all coach layouts.
      description: |
        Retrieve the coach layout description needed for graphical reservation. The coach
        layouts can either be retrieved as a complete list or specificity for a train identified via offerId and reservationId or fareId
      operationId: getCoachLayouts
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: page
          in: query
          schema:
            type: string
      responses:
        '200':
          description: |
            coach layouts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CoachLayoutCollectionResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /coach-layouts/{layoutId}:
    get:
      tags:
        - Master Data
      summary: Returns a coach layout for a provided id.
      description: |
        Retrieve a coach layout description needed for graphical reservation for a given layout id.
      operationId: getCoachLayoutsLayoutId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: layoutId
          in: path
          description: |
            id of the layout
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            coach layouts
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CoachLayoutResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /reduction-cards:
    get:
      tags:
        - Master Data
      summary: Returns all reduction card definitions.
      description: |
        returns a collection of reduction card definitions
      operationId: getReductionCards
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: page
          in: query
          schema:
            type: string
      responses:
        '200':
          description: |
            Reduction cards provided
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReductionCardCollectionResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /zones:
    get:
      tags:
        - Master Data
      summary: Returns all zone definitions.
      operationId: getZones
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: page
          in: query
          schema:
            type: string
      responses:
        '200':
          description: |
            zones provided
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ZoneCollectionResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).
            Expires:
              schema:
                type: string
                description: |
                  Gives the date/time after which the response is considered stale (in 'HTTP-date' format as defined by RFC 7231).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/documents:
    post:
      tags:
        - Booking Documents
      summary: Allows adding a document to a booking.
      operationId: createDocuments
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentRequest'
      responses:
        '200':
          description: |
            booking documents created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentCollectionResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/documents/{documentId}:
    get:
      tags:
        - Booking Documents
      summary: Returns a booking document.
      description: |
        booking documents found
      operationId: getDocument
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: documentId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            booking document found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    delete:
      tags:
        - Booking Documents
      summary: Delete a document from a booking.
      operationId: deleteBookingsDocumentsId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/idempotencyKey'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
        - name: documentId
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: |
            In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/reimbursements:
    post:
      tags:
        - Reimbursement Management
      summary: Create reimbursement for a booking.
      description: |
        Create a reimbursement request for part of a booking.
      operationId: createReimbursement
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReimbursementRequest'
      responses:
        '200':
          description: |
            Reimbursement created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReimbursementResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /bookings/{bookingId}/reimbursements/{reimbursementId}:
    get:
      tags:
        - Reimbursement Management
      summary: Get reimbursement of a booking.
      description: |
        Get reimbursement including its current state.
      operationId: getReimbursement
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: reimbursementId
          in: path
          required: true
          schema:
            type: string
            nullable: false
      responses:
        '200':
          description: |
            reimbursement found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReimbursementResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    patch:
      tags:
        - Reimbursement Management
      summary: Update reimbursement of a booking.
      description: |
        Update a reimbursement request, i.e add missing documents or change state.
      operationId: updateReimbursement
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: bookingId
          in: path
          required: true
          schema:
            type: string
            nullable: false
        - name: reimbursementId
          in: path
          required: true
          schema:
            type: string
            nullable: false
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReimbursementPatchRequest'
      responses:
        '200':
          description: |
            reimbursement updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReimbursementResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '409':
          $ref: '#/components/responses/ConflictResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /travel-accounts:
    get:
      tags:
        - Travel Account
      summary: Returns a travel account.
      operationId: getTravelAccounts
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: issuer
          in: query
          required: true
          schema:
            type: string
        - name: travelAccount
          in: query
          required: true
          schema:
            type: string
        - name: embed
          in: query
          description: |
            Default value: ALL
          schema:
            type: array
            items:
              $ref: '#/components/schemas/TravelAccountResponseContent'
      responses:
        '200':
          description: |
            travel account found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TravelAccountResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /availabilities/place-map:
    get:
      tags:
        - Availabilities
      summary: Get place map including availabilities.
      description: |
        Get place map including availabilities.
      operationId: getAvailabilitiesPlaceMap
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: contextId
          in: query
          required: true
          schema:
            type: string
        - name: contextType
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/ContextType'
        - name: resourceId
          in: query
          required: true
          schema:
            type: string
        - name: resourceType
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/ResourceType'
      responses:
        '200':
          description: |
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlaceAvailabilityResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    post:
      tags:
        - Availabilities
      summary: Get multiple place maps including availabilities.
      description: |
        Get multiple place maps including availabilities.
      operationId: getAvailabilitiesPlaceMaps
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AvailabilityScope'
      responses:
        '200':
          description: |
            place availabilities for the requested fares and offer parts
          content:
            application/json:
              schema:
                type: array
                maxItems: 50
                items:
                  $ref: '#/components/schemas/PlaceAvailability'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /availabilities/nearby:
    get:
      tags:
        - Availabilities
      summary: Get availabilities nearby a given place.
      description: |
        Get availabilities nearby a given place.
      operationId: getAvailabilitiesNearby
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: contextId
          in: query
          description: |
            denotes the offerId when contextType is OFFER, bookingId when contextType is BOOKING
          required: true
          schema:
            type: string
        - name: contextType
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/ContextType'
        - name: resourceId
          in: query
          description: |
            denotes the reservationId wen resourceType is RESERVATION, fareId when resourceType is FARE
          required: true
          schema:
            type: string
        - name: resourceType
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/ResourceType'
        - name: coachNumber
          in: query
          description: |
            coach number of the referenced place
          required: true
          schema:
            type: string
        - name: placeNumber
          in: query
          description: |
            place number of the referenced place
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            place availabilities found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlaceAvailabilityCollectionResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /availabilities/preferences:
    get:
      tags:
        - Availabilities
      summary: Get availabilities for a set of preferences.
      description: |
        Get availabilities for a set of preferences.
      operationId: getAvailabilitiesPreferences
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: contextId
          in: query
          required: true
          schema:
            type: string
        - name: contextType
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/ContextType'
        - name: resourceId
          in: query
          required: true
          schema:
            type: string
        - name: resourceType
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/ResourceType'
      responses:
        '200':
          description: |
            place availabilities found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlaceAvailabilityCollectionResponse'
          headers:
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /products:
    get:
      tags:
        - Master Data
      summary: Returns all products.
      operationId: getProducts
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: page
          in: query
          schema:
            type: string
      responses:
        '200':
          description: |
            Product found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductCollectionResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
  /products/{productId}:
    get:
      tags:
        - Master Data
      summary: Returns a product for the provided id.
      operationId: getProductsId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: productId
          in: path
          description: |
            id of the product to get
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            Product found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.
            Content-Language:
              schema:
                type: string
                description: |
                  The language of translatable strings in the response (see RFC2616-sec14.12).

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'

  /routes: # SilverRail change - new endpoint
    get:
      tags:
        - Master Data
      summary: Returns all routes.
      operationId: getRoutes
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - $ref: '#/components/parameters/ifNoneMatch'
        - name: page
          in: query
          description: |
            Can be used for pagination. When not given, the service returns the first page.
            Clients should not generate page ids themselves. Instead they should follow the
            entry in response _links object with rel=next.
            That link expires when the data on the server changes. This ensures that clients
            do not combine inconsistent pages into a corrupted data set. An expired page
            will return HTTP 400 error. Typically links have a fairly long lifetime,
            however there is no guarantee of any specific minimum lifetime.
          schema:
            type: string
      responses:
        '200':
          description: |
            routes found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RouteReferenceResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.
            ETag:
              schema:
                type: string
              description: version tag
        '303':
          $ref: '#/components/responses/SeeOtherResponse'
        '304':
          $ref: '#/components/responses/NotModifiedResponse'
        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'
    post:
      tags:
        - Routes
      summary: Returns route information for a given route request
      operationId: postRoutes
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
      requestBody:
        description: |
          request for route
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RouteReferenceRequest'
      responses:
        '200':
          description: |
            places found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RouteReferenceResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'

  /routes/{routeId}: # SilverRail change - new endpoint
    get:
      tags:
        - Routes
      summary: returns a route
      operationId: getRoutesId
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
        - name: routeId
          in: path
          description: |
            id of the route to get.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: |
            route found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RouteReferenceResponse'
          headers:
            Cache-Control:
              schema:
                type: string
                default: 'public, max-age=10000'
                description: |
                  Resource is fairly persistent and has a medium time to live to allow short-term caching.

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'

  /stop-events: # SilverRail change - new endpoint
    post:
      tags:
        - stop-events
      summary: Returns a collection of departure/arrival stop events for a given OJP StopEventRequest.
      description: |
        Returns stop events for a given stop event request based on the OJP specification.
      operationId: postStopEvents
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StopEventRequest'
      responses:
        '200':
          description: |
            trips found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StopEventResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'

  /service-info: # SilverRail change - new endpoint
    post:
      tags:
        - service-info
      summary: Returns information about specific journeys.
      description: |
        Returns information about journeys that run on a given day
        and either match a given journeyRef or routeRef.
      operationId: postServiceInfo
      parameters:
        - $ref: '#/components/parameters/requestor'
        - $ref: '#/components/parameters/acceptLanguage'
        - $ref: '#/components/parameters/traceParent'
        - $ref: '#/components/parameters/traceState'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServiceInfoRequest'
      responses:
        '200':
          description: |
            Results found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServiceInfoResponse'

        '400':
          $ref: '#/components/responses/BadRequestResponse'
        '401':
          $ref: '#/components/responses/UnauthorizedResponse'
        '403':
          $ref: '#/components/responses/ForbiddenResponse'
        '404':
          $ref: '#/components/responses/NotFoundResponse'
        '406':
          $ref: '#/components/responses/NotAcceptableResponse'
        '415':
          $ref: '#/components/responses/UnsupportedMediaTypeResponse'
        '500':
          $ref: '#/components/responses/InternalServerErrorResponse'
        '501':
          $ref: '#/components/responses/NotImplementedResponse'
        '503':
          $ref: '#/components/responses/ServiceUnavailableResponse'
        'default':
          $ref: '#/components/responses/DefaultErrorResponse'

security:
  - oAuth2ClientCredentials: []

components:
  securitySchemes:
    oAuth2ClientCredentials:
      type: oauth2
      description: |
        See https://swagger.io/docs/specification/authentication/oauth2/
      flows:
        clientCredentials:
          tokenUrl: 'https://login.microsoftonline.com/tbd'
          refreshUrl: 'https://refresh.url/tbd'
          scopes: {}

  parameters:
    acceptLanguage:
      name: Accept-Language
      in: header
      description: |
        Describes the set of natural languages that are preferred for localized text in the response
        to the request (see RFC2616-sec14.4). Supporting English (en) is a must.
      schema:
        type: string

    idempotencyKey:
      name: Idempotency-Key
      in: header
      description: |
        The HTTP Idempotency request header field can be used to carry idempotency key in order to make
        non-idempotent HTTP methods such as POST or PATCH fault-tolerant.
      schema:
        type: string

    ifNoneMatch:
      name: If-None-Match
      in: header
      description: |
        The HTTP If-None-Match is used to check whether the providing system has a newer version of the requested data.
      schema:
        type: string

    requestor:
      name: Requestor
      in: header
      description: |
        The requestor header contains detailed information about who is calling the API. It can include information such as channel, organization, sales unit or workstation id and be used to configure e.g. the fare range provided to the caller. The content of the string is part of a bilateral contract by the two parties and not standardized by OSDM. It is recommend to encrypt the information transferred.
      schema:
        type: string
      required: true

    traceParent:
      name: traceparent
      in: header
      description: |
        The traceparent header describes the position of the incoming request in its trace graph in a portable,
        fixed-length format. Its design focuses on fast parsing. Every tracing tool MUST properly set
        traceparent even when it only relies on vendor-specific information in tracestate (see W3C Trace Context).
      schema:
        type: string

    traceState:
      name: tracestate
      in: header
      description: |
        The tracestate extends traceparent with vendor-specific data represented by a set of name/value pairs.
        Storing information in tracestate is optional (see W3C Trace Context).
      schema:
        type: string

  responses:
    SeeOtherResponse:
      description: |
        See Other (303)
      headers:
        Location:
          schema:
            type: string
          description: |
            Location (URN) of the response data
        ETag:
          schema:
            type: string
          description: |
            Version tag

    NotModifiedResponse:
      description: |
        Not Modified (304)

    BadRequestResponse:
      description: |
        Bad Request (400)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    CleanupRequestAcceptedResponse:
      description: |
        Cleanup successfully initiated
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/WarningCollection'

    ConflictResponse:
      description: |
        Conflict (409)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    DefaultErrorResponse:
      description: |
        default error response
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    ForbiddenResponse:
      description: |
        Forbidden (403)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    FulfillmentRequestAcceptedResponse:
      description: |
        Fulfillment successfully initiated, retrieve booking to retrieve the fulfillment documents
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/WarningCollection'

    InternalServerErrorResponse:
      description: |
        Internal Server Error (500)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    NotAcceptableResponse:
      description: |
        Not Acceptable (406)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    NotFoundResponse:
      description: |
        Not Found (404)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    NotImplementedResponse:
      description: |
        Not Implemented (501)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    ServiceUnavailableResponse:
      description: |
        Service Unavailable (503)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    UnauthorizedResponse:
      description: |
        Unauthorized (401)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

    UnsupportedMediaTypeResponse:
      description: |
        Unsupported Media Type (415)
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/Problem'

  schemas:
    AbstractBookingPart:
      type: object
      additionalProperties: false
      discriminator:
        propertyName: objectType
      required:
        - objectType
        - id
        - createdOn
        - validFrom
        - price
        - status
        - passengerIds
        - refundable
        - exchangeable
      properties:
        objectType:
          description: |
            The type of object.
          type: string
        id:
          type: string
          nullable: false
        summary:
          type: string
          nullable: true
        createdOn:
          description: |
            Validity of offer towards passenger
          type: string
          format: date-time
          nullable: false
        confirmableUntil:
          description: |
            Date until the booking part needs to be confirmed. Must be provided for a booking part in PREBOOKED stated.
            For later states, the value is ignored and can be null.
          type: string
          format: date-time
          nullable: false
        validFrom:
          type: string
          format: date-time
          nullable: false
        validUntil:
          description: |
            Validity of offer towards passenger
          type: string
          format: date-time
          nullable: true
        confirmedOn:
          type: string
          format: date-time
          nullable: true
        price:
          $ref: '#/components/schemas/Price'
        refundAmount:
          $ref: '#/components/schemas/Price'
          description: Amount to be refunded to the purchaser
        tripCoverage:
          $ref: '#/components/schemas/TripCoverage'
        summaryProductId:
          type: string
          description: |
            Id of the product representing the commercial attributes of this booking part. Although not currently
            mandatory, this attribute should in all cases be filled in order to allow matching a booking response
            to the data in the booking request
        products:
          description: Information about the specific products and journey legs linked to the offer.
          type: array
          items:
            $ref: '#/components/schemas/ProductLegAssociation'
        status:
          $ref: '#/components/schemas/BookingPartStatus'
        offerMode:
          $ref: '#/components/schemas/OfferMode'
        bookingPartCode:
          type: string
          description: |
            The unique booking code for the part in the provider system.
        distributorBookingRef:
          description: |
            reference to the booking in the downstream distributor system
          type: string
          nullable: true
          deprecated: true
        retailerBookingRef:
          description: |
            reference to the booking in the downstream distributor system
          type: string
          nullable: true
          deprecated: true
        passengerIds:
          description: |
            Id of the passenger
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        availableFulfillmentOptions:
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentOption'
        refundable:
          description: Whether the offer is refundable and under what conditions.
          type: string
          enum:
            - 'FULLY_REFUNDABLE'
            - 'WITH_CONDITION'
            - 'PARTIALLY_REFUNABLE'
            - 'NON_REFUNDABLE'
        exchangeable:
          $ref: '#/components/schemas/ExchangeableType'
        afterSaleConditions:
          description: |
            Fine grained specification of the after sale condition of the booking.
          type: array
          items:
            $ref: '#/components/schemas/AfterSaleCondition'
        appliedCorporateCodes:
          type: array
          items:
            $ref: '#/components/schemas/CorporateCode'
        appliedPassengerTypes:
          type: array
          items:
            $ref: '#/components/schemas/AppliedPassengerType'
        appliedPromotionCodes:
          type: array
          items:
            $ref: '#/components/schemas/PromotionCode'
        appliedReductions:
          type: array
          items:
            $ref: '#/components/schemas/CardReference'
        indicatedConsumption:
          $ref: '#/components/schemas/IndicatedConsumption'
        accountingRef:
          $ref: '#/components/schemas/AccountingRef'

    AccountingRef:
      description: |
        reference to accounting data in case the accounting is not provided by the API provider
      type: object
      required:
        - accountingCompany
      properties:
        accountingCompany:
          $ref: '#/components/schemas/CompanyRef'
        accountingIds:
          description: |
            ids of accounting data used to link the booking part with accounting data in the billing send by the accounting company
          type: array
          items:
            type: string

    AbstractOfferPart:
      type: object
      additionalProperties: false
      discriminator:
        propertyName: objectType
      required:
        - objectType
        - id
        - createdOn
        - validFrom
        - price
        - offerMode
        - passengerRefs
        - refundable
        - exchangeable
        - products
        - availableFulfillmentOptions
      properties:
        objectType:
          description: |
            The type of offer component or object referenced.  In SilverOne, AdmissionOfferPart is the equivalent of a ticket.
          type: string
          example: AdmissionOfferPart
        id:
          description: |
            A unique identifier for the object, using a structured format combining product code, owner or operator code, and region/station/product codes.
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the offer.
          type: string
          nullable: true
        createdOn:
          description: |
            An ISO 8601 formatted timestamp for when the offer was created.
          type: string
          format: date-time
          nullable: false
          example: 2024-11-18T19:11:07.19Z
        validFrom:
          description: |
            An ISO 8601 formatted timestamp to denote when the offer is valid from.
          type: string
          format: date-time
          nullable: false
          example: '2024-11-18T19:11:07.19Z'
        validUntil:
          description: |
            An ISO 8601 formatted timestamp to denote when the offer is valid to.
          type: string
          format: date-time
          nullable: true
          example: '2024-11-18T19:11:07.19Z'
        price:
          $ref: '#/components/schemas/Price'
        tripCoverage:
          $ref: '#/components/schemas/TripCoverage'
        priceGuaranteedUntil:
          description: |
            Indicates until when the price for the given offer is guaranteed.
          type: string
          format: date-time
          nullable: true
        offerMode:
          $ref: '#/components/schemas/OfferMode'
        isReusable:
          description: |
            Whether the offer can be reused for multiple booking requests.  
            A value of `true` indicates that the offer can be applied to more than one booking without needing to be recreated or modified and would be typically seen for open tickets or promotions.  
            A value of `false` indicates that the offer is specific to a single booking; once used, it cannot be applied again and is typically seen with time-limited promotions or specific seat reservations.
          type: boolean
          nullable: true
          default: false
        passengerRefs:
          description: A list of references for the passengers associated with this offer.
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        numericAvailability:
          type: integer
          format: int32
          nullable: true
        refundable:
          $ref: '#/components/schemas/RefundType'
        exchangeable:
          $ref: '#/components/schemas/ExchangeableType'
        afterSalesConditions:
          type: array
          items:
            $ref: '#/components/schemas/AfterSaleCondition'
        tripTags:
          description: |
            List of tags (and their type) that allow identifying sets of compatible offers when trying to combine multiple offers covering one single trip.
            At least one (not all) tripTags must be in common to allow combination
            If no tag is provided, there is no trip constraint on this specific offer.
          type: array
          items:
            type: string
          nullable: true
        returnTags:
          description: |
            List of tags (and their type) that allow identifying sets of compatible offers when booking a return trip
            involving return-specific fares. All returnTags must be present in the counterpart offer to allow combination
            If no tag is provided, there is no return constraint on this specific offer.
          type: array
          items:
            type: string
          nullable: true
        offerTag:
          $ref: '#/components/schemas/OfferTag'
        requestedInformation:
          $ref: '#/components/schemas/RequestedInformation'
        summaryProductId:
          type: string
          description: Id of the product representing the commercial attributes of this offer part. Although not currently
            mandatory, this attribute should in all cases be facilitate product based processing at the client
        products:
          description: |
            Information about the specific products and journey legs linked to the offer.
          type: array
          items:
            $ref: '#/components/schemas/ProductLegAssociation'
          minItems: 1
        availableFulfillmentOptions:
          description: |
            Information about the different ways an offer can be fulfilled after booking.
            Each object in the array provides key details about how a customer can receive or access their tickets or travel rights.
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentOption'
          minItems: 1
        appliedCorporateCodes:
          type: array
          items:
            $ref: '#/components/schemas/CorporateCode'
        appliedPassengerTypes:
          type: array
          items:
            $ref: '#/components/schemas/AppliedPassengerType'
        appliedPromotionCodes:
          type: array
          items:
            $ref: '#/components/schemas/PromotionCode'
        appliedReductionCardTypes:
          deprecated: true
          type: array
          items:
            $ref: '#/components/schemas/ReductionCardType'
        regionalValiditySummary:
          $ref: '#/components/schemas/RegionalValiditySummary'
        indicatedConsumption:
          $ref: '#/components/schemas/IndicatedConsumption'
        _links:
          description: |
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    AbstractTravelAccount:
      type: object
      additionalProperties: false
      discriminator:
        propertyName: objectType
      required:
        - objectType
        - issuer
        - validFrom
        - validUntil
        - number
      properties:
        objectType:
          description:
            Attribute is used as discriminator for inheritance between data
            types.
          type: string
        holder:
          $ref: '#/components/schemas/PersonDetail'
        issuer:
          $ref: '#/components/schemas/CompanyRef'
        validFrom:
          type: string
          format: date-time
          nullable: false
        validUntil:
          type: string
          format: date-time
          nullable: false
        number:
          type: string
          nullable: false

    AbstractTravelAccountUnit:
      type: object
      additionalProperties: false
      discriminator:
        propertyName: objectType
      required:
        - objectType
      properties:
        objectType:
          description:
            Attribute is used as discriminator for inheritance between data
            types.
          type: string

    AccommodationSubType:
      type: string
      description: |
        Accommodation sub type definition out of the Accommodation Sub Type Code List
      default: 'ANY_SEAT'
      nullable: false
      x-extensible-enum:
        - "AISLE"
        - "AIR-CONDITIONED"
        - "ANY_SEAT"
        - "BISTRO"
        - "BICYCLE"
        - "BUSINESS"
        - "BUSINESS_COMFORT"
        - "CABIN8"
        - "CAR_SMALL"
        - "CAR_LARGE"
        - "CARRE"
        - "CHILDREN_AREA"
        - "CLUB"
        - "CLUB_2"
        - "CLUB_4"
        - "COMPARTMENT"
        - "COMPLETE"
        - "CONFERENCE"
        - "CONNECTING_DOOR"
        - "COUCHETTE_2"
        - "COUCHETTE_4"
        - "COUCHETTE_5"
        - "COUCHETTE_6"
        - "COUCHETTE_COMFORT_4"
        - "COUCHETTE_COMFORT_5"
        - "COUCHETTE_COMFORT_6"
        - "COUCHETTE_PRM_2"
        - "COUCHETTE_PRM_3"
        - "COUCHETTE_PRM_4"
        - "DOUBLE"
        - "DOUBLE_SWC"
        - "DOUBLE_SWC_DB"
        - "DOUBLE_S"
        - "EASY_ACCESS"
        - "FACE_2_FACE"
        - "EXCELLENCE"
        - "FAMILY"
        - "FRONT_VIEW"
        - "HISTORIC_COACH"
        - "INCLUDING_MEAL"
        - "INCLUDING_DRINK"
        - "KIOSQUE"
        - "LADIES"
        - "LOWER_BED"
        - "LOWER_DECK"
        - "MEN"
        - "MIDDLE_BED"
        - "MIDDLE_SEAT"
        - "MINI_SUITE"
        - "MIXED"
        - "MOTOR_CYCLE"
        - "MOTOR_CYCLE_SC"
        - "NEAR_ANIMALS"
        - "NEAR_DINING"
        - "NEAR_PLAY_AREA"
        - "NEAR_BICYCLE_AREA"
        - "NEAR_WHEELCHAIR"
        - "OPEN_SPACE"
        - "PANORAMA"
        - "PHONE"
        - "POWER"
        - "PRAM"
        - "PRAM_WITH_SEAT"
        - "RESTAURANT"
        - "SILENCE"
        - "SINGLE"
        - "SINGLE_SWC"
        - "SINGLE_SWC_DOUBLE"
        - "SIDE_BY_SIDE"
        - "SALON"
        - "SLEEPERETTE"
        - "SLEEPER_DELUXE"
        - "SOLO"
        - "SOLO_COM"
        - "SPECIAL_SLEEPER"
        - "TABLE"
        - "TANDEM"
        - "TOURIST_SLEEPER_2"
        - "TOURIST_SLEEPER_3"
        - "TOURIST_SLEEPER_4"
        - "TOURIST_SLEEPER_3_SWC"
        - "UPPER_BED"
        - "UPPER_DECK"
        - "VIDEO"
        - "WHEELCHAIR"
        - "WHEELCHAIR_AND_SEAT"
        - "WHEELCHAIR_NO_SEAT"
        - "WIFI"
        - "WINDOW"
        - "WITH_ANIMALS"
        - "WITH_SMALL_CHILDREN"
        - "WITHOUT_ANIMALS"

    AccommodationType:
      type: string
      description: |
        Accommodation type definition out of the Accommodation Type Code List
      default: 'SEAT'
      nullable: false
      x-extensible-enum:
        - "SEAT"
        - "COUCHETTE"
        - "BERTH"
        - "VEHICLE"
        - "STORAGE"

    ActualPassengerType:
      description: |
        Actual Passenger type according to UIC passenger, i.e. passenger type list. Returns the passenger type according to the underlying fare system. E.g. a passenger of type PERSON is categorized as a ADULT or YOUTH.

      type: string
      x-extensible-enum:
        - 'DOG'
        - 'PET'
        - 'LUGGAGE'
        - 'PRM'
        - 'BICYCLE'
        - 'PRAM'
        - 'COMPANION_DOG'
        - 'CAR'
        - 'MOTORCYCLE'
        - 'TRAILER'
        - 'FAMILY_CHILD'
        - 'WHEELCHAIR'
        - 'PERSON'
        - 'YOUNG_CHILD'
        - 'CHILD'
        - 'YOUTH'
        - 'ADULT'
        - 'SENIOR'
      default: 'PERSON'

    AdditionalOfferCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        offers:
          type: array
          items:
            $ref: '#/components/schemas/Offer'

    Address:
      description: |
        A descriptive data associated with a place that can be used to describe the unique geographical context of a
        place for the purposes of identifying it. may be refined as either a road address, a postal address or both.
        Provided by OJP.
      allOf:
        - $ref: '#/components/schemas/Place'
        - type: object
          additionalProperties: false
          properties:
            name:
              description: |
                Name or description of address for use in passenger information.
              type: string
              nullable: true
              example: Oude Trambaan 7, 2265CA Leidschendam, Nederland
            code:
              description: |
                id of the address
              type: string
              nullable: true
              example: NL:1916200000022621
            countryName:
              description: |
                Country of the address.
              type: string
              nullable: true
              example: The Netherlands
            countryCode:
              $ref: '#/components/schemas/CountryCode'
            postCode:
              description: |
                Postal code of the address.
              type: string
              nullable: true
              example: 2265CA
            city:
              description: |
                City name
              type: string
              nullable: true
              example: Leidschendam
            topographicPlaceName:
              description: |
                TopographicPlace name of the address. If set it should at least contain the city name.
              type: string
              nullable: true
              example: Leidschendam
            street:
              description: |
                Street name of the address. Can also contain the house number.
              type: string
              nullable: true
              example: Oude Trambaan
            houseNumber:
              description: |
                House number of the address. House number can either be in this separate field, or can be
                contained in the street field.
              type: string
              nullable: true
              example: 7
            ref:
              $ref: '#/components/schemas/AddressRef'

    AddressRef:
      description: |
        Reference to an Address.
      allOf:
        - $ref: '#/components/schemas/PlaceRef'
        - type: object
          additionalProperties: false
          required:
            - addressRef
          properties:
            addressRef:
              type: string
              nullable: false

    Admission:
      allOf:
        - $ref: '#/components/schemas/AbstractBookingPart'
        - type: object
          additionalProperties: false
          properties:
            isReservationRequired:
              type: boolean
              nullable: true
              default: false
            feeRefs:
              type: array
              items:
                $ref: '#/components/schemas/BookingPartReference'
            reservationRefs:
              type: array
              items:
                $ref: '#/components/schemas/BookingPartReference'
            ancillaryRefs:
              type: array
              items:
                $ref: '#/components/schemas/BookingPartReference'
            regulatoryConditions:
              type: array
              items:
                $ref: '#/components/schemas/RegulatoryCondition'

    AdmissionOfferPart:
      allOf:
        - $ref: '#/components/schemas/AbstractOfferPart'
        - type: object
          additionalProperties: false
          properties:
            isReservationRequired:
              type: boolean
              nullable: true
            reservations:
              type: array
              items:
                $ref: '#/components/schemas/ReservationRelation'
            ancillaries:
              type: array
              items:
                $ref: '#/components/schemas/AncillaryRelation'
            feeRefs:
              type: array
              items:
                $ref: '#/components/schemas/OfferPartReference'
            regulatoryConditions:
              type: array
              items:
                $ref: '#/components/schemas/RegulatoryCondition'
            includedReservations:
              type: array
              items:
                $ref: '#/components/schemas/ReservationOfferPart'
            throughTicketTags:
              type: array
              items:
                $ref: '#/components/schemas/ThroughTicketTag'

    AfterSaleCondition:
      type: object
      additionalProperties: false
      description: |
        After sale amounts for a given offer.
      properties:
        condition:
          $ref: '#/components/schemas/AfterSaleConditionType'
        validFrom:
          type: string
          format: date-time
          nullable: true
        validUntil:
          type: string
          format: date-time
          nullable: true
        afterSaleFee:
          $ref: '#/components/schemas/Price'
        reimbursementMethod:
          $ref: '#/components/schemas/ReimbursementMethod'

    AfterSaleConditionType:
      type: string
      x-extensible-enum:
        - 'PLACE_CHANGE'
        - 'EXCHANGE'
        - 'REFUND'

    AfterSalesConditionsLink:
      type: object
      additionalProperties: false
      required:
        - conditions
      properties:
        conditions:
          description: |
            Structured description of the after-sales conditions.
          type: array
          items:
            $ref: '#/components/schemas/AfterSaleCondition'
          minItems: 1
        isSupportingIndividualContracts:
          description: |
            Indicates that the after sales of this fare can be treated independently per person.
          type: boolean
          nullable: true
          default: false

    Alight:
      type: object
      additionalProperties: false
      description: |
        Details about the stop or station where passengers alight the service for a specific leg of a journey.  It contains information about the location, including station details, stop places, scheduled departure times, and status indicators.
      required:
        - stopPlaceRef
        - stopPlaceName
        - serviceArrival
      properties:
        stopPlaceRef:
          $ref: '#/components/schemas/StopPlaceRef'
        stopPlaceName:
          type: string
          description: |
            A human-readable name for the stop.
          nullable: false
          example: Luzern
        plannedStopPointName:
          description: |
            Name of the bay/quay/terminal where to board/alight from the vehicle. According to
            planned timetable.
          type: string
          nullable: true
          example: 3
        estimatedStopPointName:
          description: |
            Name of the bay/quay/terminal where to board the vehicle. As to the latest realtime status.
          type: string
          nullable: true
          example: 8
        serviceArrival:
          $ref: '#/components/schemas/ServiceTime'
        status:
          $ref: '#/components/schemas/StopCallStatus'

    AlightSpecification:
      type: object
      additionalProperties: false
      description: |
        Minimal alight specification
      required:
        - stopPlaceRef
        - serviceArrival
      properties:
        stopPlaceRef:
          $ref: '#/components/schemas/StopPlaceRef'
        serviceArrival:
          $ref: '#/components/schemas/ServiceTime'

    Ancillary:
      allOf:
        - $ref: '#/components/schemas/AbstractBookingPart'
        - type: object
          additionalProperties: false
          required:
            - type
          properties:
            feeRefs:
              type: array
              items:
                $ref: '#/components/schemas/BookingPartReference'
            type:
              $ref: '#/components/schemas/AncillaryType'

    AncillaryGroup:
      type: object
      additionalProperties: false
      required:
        - id
        - name
        - ancillaryRefs
      properties:
        id:
          type: string
          nullable: false
        name:
          type: string
          nullable: false
        ancillaryRefs:
          type: array
          items:
            $ref: '#/components/schemas/OfferPartReference'
          minItems: 1

    AncillaryOfferPart:
      allOf:
        - $ref: '#/components/schemas/AbstractOfferPart'
        - type: object
          additionalProperties: false
          required:
            - type
          properties:
            feeRefs:
              type: array
              items:
                $ref: '#/components/schemas/OfferPartReference'
            category:
              description: |
                Categorization of the ancillary such as 'Meal' or 'Gift'.
              type: string
              nullable: true
            type:
              $ref: '#/components/schemas/AncillaryType'

    AncillaryRelation:
      type: object
      additionalProperties: false
      required:
        - minGroupItemsToBeBooked
        - ancillaryGroup
      properties:
        minGroupItemsToBeBooked:
          type: integer
          format: int32
          nullable: false
        maxGroupItemsToBeBooked:
          type: integer
          format: int32
          nullable: true
        ancillaryGroup:
          $ref: '#/components/schemas/AncillaryGroup'

    AncillarySelection:
      type: object
      additionalProperties: false
      description: |
        Is some cases one out of a set of ancillary offerparts must be booked.
      required:
        - ancillaryId
        - passengerRefs
      properties:
        ancillaryId:
          type: string
          nullable: false
        passengerRefs:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false

    AncillaryType:
      type: string
      x-extensible-enum:
        - 'MEAL'
        - 'BEVERAGE'

    AnonymousPassengerSpecification:
      type: object
      description: |
        A minimal specification of a passenger when requesting offers without requiring GDPR-relevant attributes such as name, address, or other personal identifiers. 
        This enables fare calculations and eligibility determination while ensuring compliance with privacy regulations.
      additionalProperties: false
      required:
        - externalRef
        - type
      properties:
        externalRef:
          description: |
            A stable and unique reference to link the passenger to an external reference number that may have been provided.  When passed in a request, it must be echoed back in the response.
          type: string
          nullable: false
        dateOfBirth:
          description: |
            Date of birth of the passenger. Only needed for passengers of type persons, family child, PRM.
            and wheelchair.
          type: string
          format: date
          nullable: true
        age:
          description: |
            The age of the passenger.  Used to generate offers appropriate to the passenger’s age.
          type: integer
          format: int32
          minimum: 0
          nullable: true
        type:
          $ref: '#/components/schemas/PassengerType'
        prmNeeds:
          type: array
          items:
            $ref: '#/components/schemas/PRMNeedType'
        cards:
          type: array
          items:
            $ref: '#/components/schemas/CardReference'
        gender:
          $ref: '#/components/schemas/Gender'

    ApplicabilityType:
      type: string
      x-extensible-enum:
        - 'BOOKING'
        - 'TRIP'
        - 'OFFERPART'

    AppliedPassengerType:
      type: object
      additionalProperties: false
      description: |
        Applied passenger types and reductions per passenger, per offer part and per part of the trip.
      required:
        - passengerRef
        - type
        - description
      properties:
        passengerRef:
          description: |
            id of the passenger.  
            In the offer flow the id refers to the externalRef as the passenger id is created at booking time only.
          type: string
          nullable: false
        type:
          $ref: '#/components/schemas/ActualPassengerType'
        description:
          description: |
            Clear text representation of the actual passenger type, suitable to be presented to a client.
          type: string
          nullable: false
        tripCoverage:
          $ref: '#/components/schemas/TripCoverage'
        appliedReductionCardTypes:
          deprecated: true
          type: array
          items:
            $ref: '#/components/schemas/ReductionCardType'
        appliedReductions:
          type: array
          items:
            $ref: '#/components/schemas/CardReference'

    AttachableItemType:
      description: |
        Indication of items attached to the car
      type: string
      x-extensible-enum:
        - ROOF_RACK
        - BICYCLE_STAND

    AvailabilityScope:
      type: object
      description: fare or offerParts which should be covered by the available seats
      required:
        - partReferences
      properties:
        singleSelectionMapsRequired:
          description: if true the place map must include places for a single reservation or fare only
          type: boolean
          default: true
        partReferences:
          type: array
          items:
            $ref: '#/components/schemas/PartReference'

    PartReference:
      type: object
      description: reference to a fare or offerPart
      required:
        - contextId
        - contextType
        - resourceId
        - resourceType
      properties:
        contextId:
          type: string
        contextType:
          $ref: '#/components/schemas/ContextType'
        resourceId:
          type: string
        resourceType:
          $ref: '#/components/schemas/ResourceType'

    AvailabilityStatus:
      description: |
        status of the place or coach
      type: string
      enum:
        - 'ALLOCATED'
        - 'FREE'
        - 'RESTRICTED'
      default: 'FREE'

    AvailablePlace:
      type: object
      additionalProperties: false
      description: |
        Describes the details of the available places.
      required:
        - accommodationType
        - accommodationSubType
        - tripLegCoverage
      properties:
        accommodationType:
          $ref: '#/components/schemas/AccommodationType'
        accommodationSubType:
          $ref: '#/components/schemas/AccommodationSubType'
        placeProperties:
          description: |
            Selectable place properties for the offer. For AccommodationSubType=ANY_SEAT all available and bookable properties need to be returned to give a sales tool a list of possible selections.
          type: array
          items:
            type: string
          nullable: true
        numericAvailability:
          description: |
            For the AccommodationSubType=ANY_SEAT the total amount of places for the combination of AccommodationType and AccommodationSubType needs to returned.
          type: integer
          format: int32
          nullable: true
        tripLegCoverage:
          $ref: '#/components/schemas/TripLegCoverage'

    AvailablePlacePreferences:
      type: object
      additionalProperties: false
      properties:
        preferenceGroups:
          description: |
            possible preferences to be requested in reservation without changing the fare
          type: array
          items:
            $ref: '#/components/schemas/PlacePreferenceGroup'
        graphicalReservation:
          description: |
            graphical reservation is supported, interface type 'NO','UIC_918',..
          type: string
          nullable: true

    BackOfficeStatus:
      description: |
        Status of the request sent to the back office.
        Valid Values:
        - 'INITIATED'
        - 'EVALUATING'
        - 'DECIDED'
        - 'SETTLED'
        - 'INFORMATION_MISSING'
        - 'REQUEST_UPDATED': Additional information, i.e. documents have been provided which should trigger reevaluation.
      type: string
      enum:
        - 'INITIATED'
        - 'EVALUATING'
        - 'DECIDED'
        - 'SETTLED'
        - 'INFORMATION_MISSING'
        - 'REQUEST_UPDATED'

    BankAccountReference:
      type: object
      additionalProperties: false
      description: |
        bank account
      properties:
        iban:
          description: |
            IBAN to be used in Europe
          type: string
          pattern: '[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}'
          nullable: true
        accountId:
          description: |
            bank account id to be used outside of EU
          type: string
          nullable: true
        bankIdCode:
          $ref: '#/components/schemas/BankIdCode'
        bankId:
          type: string
          nullable: true
        ownerName:
          type: string
          nullable: true
          example: John Doe

    BankIdCode:
      type: string
      x-extensible-enum:
        - 'SWIFT'

    BarCodeType:
      description: |
        Item according to IRS 90918-9: FCB, TLB, SSB
      type: string
      x-extensible-enum:
        - 'FCB'
        - 'TLB'
        - 'SSB'
        - 'DOSIPAS'

    BaseTripPolicyFilter:
      type: object
      additionalProperties: false
      # SilverRail change
      description: |
        Policies that control the trip search behavior for both public and individual transport.
        Provided by OJP and SilverRail extensions added.
      properties:
        noSingleStep:
          description: |
            The user is not able to climb one step, i.e. wheel chair.
          type: boolean
          nullable: true
          default: false
        noSight:
          description: |
            The user is not able to see.
          type: boolean
          nullable: true
          default: false
        # SilverRail change
        includeOvertaken:
          description: |
            Specifies whether to return overtaken trips. <br/>
            Trip A is considered overtaken by trip B if any of the following applies: <br/>
            1) Trip B departs later than Trip A and arrives at destination earlier than Trip A. <br/>    
            2) Trip B departs at the same time as that of Journey A and arrives at destination earlier than Trip A. <br/>        
            3) Trip B departs later than Trip A and arrives at destination at the same time as that of Trip A. <br/>
          type: boolean

    Board:
      type: object
      additionalProperties: false
      description: |
        Details about the stop or station where passengers board the service for a specific leg of a journey.  It contains information about the location, including station details, stop places, scheduled departure times, and status indicators.
      required:
        - stopPlaceRef
        - stopPlaceName
        - serviceDeparture
      properties:
        stopPlaceRef:
          $ref: '#/components/schemas/StopPlaceRef'
        stopPlaceName:
          type: string
          description: |
            A human-readable name for the stop.
          nullable: false
          example: Luzern
        plannedStopPointName:
          description: |
            Name of the bay/quay/terminal where to board/alight from the vehicle. According to
            planned timetable.
          type: string
          nullable: true
          example: 3
        estimatedStopPointName:
          description: |
            Name of the bay/quay/terminal where to board the vehicle. As to the latest realtime status.
          type: string
          nullable: true
          example: 8
        serviceDeparture:
          $ref: '#/components/schemas/ServiceTime'
        status:
          $ref: '#/components/schemas/StopCallStatus'

    BoardSpecification:
      type: object
      additionalProperties: false
      description: |
        Minimal board specification.
      required:
        - stopPlaceRef
        - serviceDeparture
      properties:
        stopPlaceRef:
          $ref: '#/components/schemas/StopPlaceRef'
        serviceDeparture:
          $ref: '#/components/schemas/ServiceTime'

    BookedOffer:
      type: object
      additionalProperties: false
      description: |
        The attribute 'offerSummary' is meaningful at trip-offer-collection response time only.
      required:
        - offerId
      properties:
        offerId:
          type: string
          nullable: false
          description: Note that the offerId returned does not necessarily match the offerId given in the Booking Request.
        summary:
          description: |
            A human-readable description of the booked offer.
          type: string
          nullable: true
        admissions:
          type: array
          items:
            $ref: '#/components/schemas/Admission'
        reservations:
          type: array
          items:
            $ref: '#/components/schemas/Reservation'
        ancillaries:
          type: array
          items:
            $ref: '#/components/schemas/Ancillary'
        fees:
          type: array
          items:
            $ref: '#/components/schemas/Fee'
        fares:
          type: array
          items:
            $ref: '#/components/schemas/Fare'
        tripCoverage:
          $ref: '#/components/schemas/TripCoverage'
        appliedThroughTicketTags:
          type: array
          items:
            $ref: '#/components/schemas/ThroughTicketTag'
        products:
          type: array
          items:
            $ref: '#/components/schemas/Product'

    BookedOfferAncillaryRequest:
      type: object
      additionalProperties: false
      required:
        - offerId
        - ancillaryOfferId
        - passengerRefs
      properties:
        offerId:
          type: string
          nullable: false
        ancillaryOfferId:
          type: string
          nullable: false
        passengerRefs:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        tripCoverage:
          $ref: '#/components/schemas/TripCoverage'

    BookedOfferAncillaryResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        bookedOffers:
          type: array
          items:
            $ref: '#/components/schemas/BookedOffer'

    BookedOfferRequest:
      type: object
      description:
        adding an offer to a booking as pre-booked. additional passengers that
        were not already in the booking are added in additional passengers
        existing passengers are referenced in passengerRef.
      additionalProperties: false
      required:
        - offers
      properties:
        offers:
          type: array
          items:
            $ref: '#/components/schemas/OfferSelection'
          minItems: 1
        passengers:
          type: array
          items:
            $ref: '#/components/schemas/Passenger'
          deprecated: true
          description: |
            Existing passengers are linked with the given booking and do not need to be referenced in the request.
        additionalPassengerSpecifications:
          type: array
          items:
            $ref: '#/components/schemas/PassengerSpecification'
        promotionCodes:
          type: array
          items:
            $ref: '#/components/schemas/PromotionCode'

    BookedOfferReservationRequest:
      type: object
      additionalProperties: false
      required:
        - offerId
        - reservationOfferId
        - passengerRefs
        - placeSelections
      properties:
        offerId:
          type: string
          nullable: false
        reservationOfferId:
          type: string
          nullable: false
        passengerRefs:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        placeSelections:
          type: array
          items:
            $ref: '#/components/schemas/PlaceSelection'
          minItems: 1
        tripCoverage:
          $ref: '#/components/schemas/TripCoverage'

    BookedOfferReservationResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        bookedOffers:
          type: array
          items:
            $ref: '#/components/schemas/BookedOffer'

    BookedOfferResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        bookedOffer:
          $ref: '#/components/schemas/BookedOffer'
          deprecated: true
        bookedOfferIds:
          type: array
          items:
            type: string
          description: |
            IDs of offers booked by the request. Will be always provided, single of the booked offer may not be provided in bookedOffer attribute.

    BookedOfferSummary:
      type: object
      additionalProperties: false
      required:
        - offerId
        - summary
        - bookingPartSummaries
      properties:
        offerId:
          type: string
          nullable: false
        summary:
          type: string
          nullable: false
        bookingPartSummaries:
          type: array
          items:
            $ref: '#/components/schemas/BookingPartSummary'
          minItems: 1

    Booking:
      type: object
      additionalProperties: false
      required:
        - id
        - createdOn
        - passengers
      properties:
        id:
          type: string
          nullable: false
        bookingCode:
          type: string
          description:
            The unique booking code in the provider system. Usually refers to an
            order number or PNR.
        externalRef:
          description: |
            The unique booking reference in the provider system. Usually refers to an order number or PNR.
          type: string
          nullable: true
        summary:
          description: |
            A human-readable description of the booking.
          type: string
          nullable: true
          example: Booking 2345 for Clemens Gantert
        createdOn:
          type: string
          format: date-time
          nullable: false
        passengers:
          type: array
          items:
            $ref: '#/components/schemas/Passenger'
          minItems: 1
        purchaser:
          $ref: '#/components/schemas/Purchaser'
        provisionalPrice:
          $ref: '#/components/schemas/Price'
        confirmedPrice:
          $ref: '#/components/schemas/Price'
        bookedOffers:
          type: array
          items:
            $ref: '#/components/schemas/BookedOffer'
        trips:
          description: |
            This trip will be included in the offer if the offer is part of the booking.
          type: array
          items:
            $ref: '#/components/schemas/Trip'
        requestedInformation:
          $ref: '#/components/schemas/RequestedInformation'
        confirmationTimeLimit:
          description: |
            Time until the booking needs to be confirmed or put on hold. After that, the booking is no longer valid.
          type: string
          format: date-time
          nullable: true
        fulfillmentType:
          $ref: '#/components/schemas/FulfillmentType'
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
        issuedVouchers:
          type: array
          items:
            $ref: '#/components/schemas/VoucherInformation'
        documents:
          type: array
          items:
            $ref: '#/components/schemas/Document'
        paymentMethods:
          type: array
          items:
            $ref: '#/components/schemas/PaymentMethod'
        refundMethods:
          type: array
          items:
            $ref: '#/components/schemas/RefundMethod'
        refundOffers:
          description: |
            Refund offers are created with the details of the to-be-confirmed refund operation.
            One refund offer can then be confirmed, which turns it into a refund.
          type: array
          items:
            $ref: '#/components/schemas/RefundOffer'
        releaseOffers:
          type: array
          items:
            $ref: '#/components/schemas/ReleaseOffer'
        cancelFulfillmentsOffers:
          type: array
          items:
            $ref: '#/components/schemas/CancelFulfillmentsOffer'
        exchangeOperations:
          type: array
          items:
            $ref: '#/components/schemas/ExchangeOperation'
        relatedBookingIds:
          description: |
            If this booking has been split, this attribute references the newly created bookingIds.
          items:
            type: string
          nullable: true
        _links:
          description: |
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'
    BookingCleanupRequest:
      type: object
      additionalProperties: false
      description: |
        Request to cleanup a complete booking.
        Unconfirmed items will be deleted, any confirmed items will be refunded. There is no confirmation required.
      properties:
        overruleCode:
          $ref: '#/components/schemas/OverruleCode'
        refundDate:
          description: |
            Indicates for passes the date taken as reference to compute possible partial refund. It is also the date taken
            as reference to invalidate the pass partially refunded.
          type: string
          format: date-time
          nullable: true

    BookingHistoryResponse:
      type: object
      additionalProperties: false
      required:
        - events
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        events:
          type: array
          items:
            $ref: '#/components/schemas/Event'
          minItems: 1

    BookingPartReference:
      type: object
      additionalProperties: false
      description: |
        References all the offer part elements in an uniform format.
        In its part, an OfferPartReference can refer to reservation, admission, ancillary or fee.
      required:
        - id
      properties:
        id:
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the offer part reference.
          type: string
          nullable: true
        _links:
          description: |
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    BookingPartStatus:
      type: string
      enum:
        - 'PREBOOKED'
        - 'ON_HOLD'
        - 'CONFIRMED'
        - 'FULFILLED'
        - 'CANCELLED'
        - 'RELEASED'
        - 'REFUNDED'
        - 'EXCHANGE_ONGOING'
        - 'EXCHANGED'
        - 'ERROR'

    BookingPartSummary:
      type: object
      additionalProperties: false
      required:
        - id
        - summary
        - productSummaries
        - status
        - type
      properties:
        id:
          type: string
          nullable: false
        summary:
          type: string
          nullable: false
        productSummaries:
          type: array
          items:
            $ref: '#/components/schemas/ProductSummary'
          minItems: 1
        status:
          $ref: '#/components/schemas/BookingPartStatus'
        type:
          $ref: '#/components/schemas/BookingPartType'

    BookingPartType:
      type: string
      enum:
        - 'ADMISSION'
        - 'RESERVATION'
        - 'ANCILLARY'

    BookingPatchRequest:
      type: object
      additionalProperties: false
      description: |
        Allows setting the required fulfillment type, e.g. value paper
        or e-ticket and preferred fulfillment media.
        The latter is optional and relevant in some cases only.
      properties:
        placeSelections:
          type: array
          items:
            $ref: '#/components/schemas/PlaceSelection'
        requestedFulfillmentType:
          $ref: '#/components/schemas/FulfillmentType'
        preferredFulfillmentMedia:
          $ref: '#/components/schemas/FulfillmentMediaType'
        usedPaymentMethods:
          type: array
          items:
            $ref: '#/components/schemas/PaymentMethod'

    BookingRequest:
      type: object
      additionalProperties: false
      required:
        - offers
        - passengerSpecifications
      properties:
        offers:
          description: |
            The list of offers that need to be provisionally booked, with possibly the reservations and ancillaries associated that should be booked as well.
          type: array
          items:
            $ref: '#/components/schemas/OfferSelection'
          minItems: 1
        passengerSpecifications:
          description: |
            If needed (cf requiredInformation) or desired, this array allows providing the required details on some or all passengers
          type: array
          items:
            $ref: '#/components/schemas/PassengerSpecification'
          minItems: 1
        purchaser:
          $ref: '#/components/schemas/PurchaserSpecification'
        promotionCodes:
          type: array
          items:
            $ref: '#/components/schemas/PromotionCode'
        embed:
          description: |
            Influences whether referenced resources are returned in full or as references only.
          type: array
          items:
            $ref: '#/components/schemas/BookingResponseContent'
        externalRef:
          type: string
          nullable: true

    BookingResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        booking:
          $ref: '#/components/schemas/Booking'

    BookingResponseContent:
      description: |
        Influences whether referenced resources are returned in full or as references only.
      type: string
      enum:
        - 'ALL'
        - 'BOOKING'
        - 'BOOKING_PASSENGERS'
        - 'BOOKING_PURCHASER'
        - 'BOOKING_TRIPS'
        - 'BOOKING_BOOKEDOFFERS'
        - 'BOOKING_FULFILLMENTS'
        - 'BOOKING_DOCUMENTS'
        - 'BOOKING_REFUNDOFFERS'
        - 'BOOKING_RELEASEOFFERS'
        - 'BOOKING_CANCELFULFILLMENTSOFFER'
        - 'BOOKING_EXCHANGEOPERATIONS'
        - 'NONE'
      default: 'ALL'

    BookingSearchParameters:
      type: object
      additionalProperties: false
      properties:
        numberOfResults:
          type: integer
          format: int32
          nullable: true
          default: 10
        status:
          type: array
          items:
            $ref: '#/components/schemas/BookingPartStatus'

    BookingSearchRequest:
      type: object
      additionalProperties: false
      properties:
        origin:
          $ref: '#/components/schemas/PlaceRef'
        destination:
          $ref: '#/components/schemas/PlaceRef'
        passenger:
          $ref: '#/components/schemas/PersonSearchRequest'
        purchaser:
          $ref: '#/components/schemas/PurchaserSearchRequest'
        bookingId:
          type: string
          nullable: true
        bookingCode:
          description: |
            reference to the booking in the downstream distributor system
          type: string
          nullable: true
        externalRef:
          description: reference to the booking in the retailer system
          type: string
          nullable: true
        distributorBookingRef:
          description: |
            reference to the booking in the downstream distributor system
          type: string
          nullable: true
          deprecated: true
        retailerBookingRef:
          type: string
          nullable: true
          deprecated: true
        fulfillmentId:
          type: string
          nullable: true
        fulfillmentControlNumber:
          type: string
          nullable: true
        travelDateRange:
          $ref: '#/components/schemas/DateRange'
        purchaseDateRange:
          $ref: '#/components/schemas/DateRange'
        parameters:
          $ref: '#/components/schemas/BookingSearchParameters'

    BookingSearchResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        bookingSearchResults:
          type: array
          items:
            $ref: '#/components/schemas/BookingSearchResult'

    BookingSearchResult:
      type: object
      additionalProperties: false
      description: |
        Summary of the booking found by the booking search. For more information, get the booking.
      required:
        - passengers
        - id
        - bookedOfferSummaries
      properties:
        purchaser:
          $ref: '#/components/schemas/Purchaser'
        passengers:
          type: array
          items:
            $ref: '#/components/schemas/Passenger'
          minItems: 1
        tripSummaries:
          type: array
          items:
            $ref: '#/components/schemas/TripSummary'
        id:
          type: string
          nullable: false
        confirmedPrice:
          $ref: '#/components/schemas/Price'
        provisionalPrice:
          $ref: '#/components/schemas/Price'
        bookedOfferSummaries:
          type: array
          items:
            $ref: '#/components/schemas/BookedOfferSummary'
          minItems: 1

    BookingSplit:
      type: object
      additionalProperties: false
      description: |
        Contains the newly created booking
      required:
        - bookingId
        - passengerIds
      properties:
        bookingId:
          type: string
          nullable: false
        passengerIds:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false

    BookingSplitGroup:
      type: object
      additionalProperties: false
      description: |
        Group of passenger(s) that is to be split into a separate booking.
      required:
        - passengerIds
      properties:
        passengerIds:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false

    BookingSplitRequest:
      type: object
      additionalProperties: false
      required:
        - groups
      properties:
        groups:
          type: array
          items:
            $ref: '#/components/schemas/BookingSplitGroup'
          minItems: 1

    BookingSplitResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        bookingSplitResult:
          $ref: '#/components/schemas/BookingSplitResult'

    BookingSplitResult:
      type: object
      additionalProperties: false
      required:
        - bookingSplit
      properties:
        bookingSplit:
          type: array
          items:
            $ref: '#/components/schemas/BookingSplit'
          minItems: 1

    Calendar:
      type: object
      additionalProperties: false
      properties:
        from:
          type: string
          format: date-time
          nullable: true
        until:
          type: string
          format: date-time
          nullable: true
        dates:
          description: |
            dates included in the calendar. In case no dates are provided the range is assumed to be valid
          type: array
          items:
            type: string
            format: date-time
          nullable: true
        utcOffset:
          description: |
            offset to UTC in minutes (number of minutes to be added to get UTC dates)
          type: integer
          format: int32
          nullable: true

    CancelFulfillmentsOffer:
      type: object
      additionalProperties: false
      required:
        - id
        - createdOn
        - validFrom
        - validUntil
        - status
        - fulfillments
      properties:
        id:
          description: |
            id of the refund offer
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the refund offer.
          type: string
          nullable: true
        createdOn:
          type: string
          format: date-time
          nullable: false
        validFrom:
          type: string
          format: date-time
          nullable: false
        validUntil:
          description: |
            time until the offer can be used
          type: string
          format: date-time
          nullable: false
        confirmedOn:
          type: string
          format: date-time
          nullable: true
        status:
          $ref: '#/components/schemas/CancelFulfillmentsStatus'
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
          minItems: 1
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    CancelFulfillmentsOfferCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        cancelFulfillmentOffers:
          type: array
          items:
            $ref: '#/components/schemas/CancelFulfillmentsOffer'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    CancelFulfillmentsOfferPatchRequest:
      type: object
      additionalProperties: false
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/CancelFulfillmentsStatus'

    CancelFulfillmentsOfferRequest:
      type: object
      additionalProperties: false
      properties:
        fulfillmentIds:
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentId'

    CancelFulfillmentsOfferResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        cancelFulfillmentsOffer:
          $ref: '#/components/schemas/CancelFulfillmentsOffer'

    CancelFulfillmentsStatus:
      type: string
      enum:
        - 'PROPOSED'
        - 'CONFIRMED'

    Car:
      type: object
      additionalProperties: false
      required:
        - weight
        - length
        - width
        - height
        - model
      properties:
        weight:
          type: integer
          format: int32
          minimum: 0
          nullable: false
        length:
          type: integer
          format: int32
          minimum: 0
          nullable: false
        width:
          type: integer
          format: int32
          minimum: 0
          nullable: false
        height:
          type: integer
          format: int32
          minimum: 0
          nullable: false
        model:
          description: |
            Free text description of the model (e.g. BMW X5)
          type: string
          nullable: false
        attachedItems:
          description: |
            Indication of items attached to the car
          type: array
          items:
            $ref: '#/components/schemas/AttachableItemType'
        licensePlate:
          description: |
            The license plate is a personal data item and must not be provided in an offer request.
            It must be patched into the offer after the customer accepted the offer for booking.
          type: string
          nullable: true

    CardReference:
      type: object
      additionalProperties: false
      description: |
        Number and issuer are needed to identify a travel account, the code identifies a reduction card.
      required:
        - type
      properties:
        code:
          description: |
            Code of the card type according to issuer. E.g. BahnCard50.
          type: string
          nullable: true
        number:
          description: |
            Number identifying the travel account, need issuer to be unique.
          type: string
          nullable: true
        issuer:
          $ref: '#/components/schemas/CompanyRef'
        type:
          $ref: '#/components/schemas/TravelAccountType'

    CardTypeReference:
      type: object
      additionalProperties: false
      required:
        - code
      properties:
        issuer:
          $ref: '#/components/schemas/CompanyRef'
        code:
          type: string
          nullable: false
        cardName:
          type: string
          nullable: true

    CarrierConstraint:
      type: object
      additionalProperties: false
      description: |
        Either excluded or included carriers can be set.
      properties:
        includedCarriers:
          type: array
          items:
            type: string
          nullable: true
        excludedCarriers:
          type: array
          items:
            type: string
          nullable: true

    CarrierFilter:
      type: object
      additionalProperties: false
      description: |
        Filter for in/exclusion of carriers. Provided by OJP.
      properties:
        exclude:
          description: |
            Whether carrier in list are to include or exclude from search.
          type: boolean
          nullable: true
          default: true
        carriers:
          description: |
            Reference to carrier
          type: array
          items:
            $ref: '#/components/schemas/CompanyRef'

    ClaimedJourneyDetails:
      type: object
      additionalProperties: false
      properties:
        impact:
          $ref: '#/components/schemas/ImpactType'
        finalDelay:
          type: string
          format: duration
          nullable: true
          example: PT30M
        onReturn:
          type: boolean
          nullable: true
          default: false
        delayedJourney:
          type: array
          items:
            $ref: '#/components/schemas/ClaimedLeg'

    ClaimedLeg:
      type: object
      additionalProperties: false
      properties:
        trainNumber:
          type: string
          nullable: true
        connectionMissed:
          description: |
            connection missed due to a delay on the previous leg
          type: boolean
          nullable: true
        serviceDegradation:
          type: array
          items:
            $ref: '#/components/schemas/ServiceDegradation'
        departureStation:
          $ref: '#/components/schemas/Place'
        arrivalStation:
          $ref: '#/components/schemas/Place'
        plannedDepartureTime:
          type: string
          format: date-time
          nullable: true
        timetabledArrivalTime:
          type: string
          format: date-time
          nullable: true
        observedArrivalTime:
          type: string
          format: date-time
          nullable: true

    Coach:
      type: object
      additionalProperties: false
      required:
        - compartments
        - number
        - status
        - layoutId
      properties:
        compartments:
          type: array
          items:
            $ref: '#/components/schemas/Compartment'
          minItems: 1
        number:
          description: |
            coach number
          type: string
          nullable: false
        status:
          $ref: '#/components/schemas/AvailabilityStatus'
        layoutId:
          description: |
            id to identify the coach layout
          type: string
          nullable: false
        layoutIdUpperDeck:
          description: |
            id to identify a layout for the upper deck in a double deck coach
          type: string
          nullable: true
        layoutIdLowerDeck:
          description: |
            id to identify a layout for the lower deck in a double deck coach
          type: string
          nullable: true
        direction:
          $ref: '#/components/schemas/TravelDirectionType'
        owner:
          $ref: '#/components/schemas/CompanyRef'
        specialCoach:
          $ref: '#/components/schemas/SpecialCoachType'
        reservationRefs:
          description: |
            references to the reservations/fares for which all places in the coach can be selected. The reservation ids must be part of the list of reservation ids for which the consumer has requested the available places and the consumer must accept a list by setting singleSelectionMapsRequired to false
          type: array
          items:
            type: string

    CoachLayout:
      type: object
      additionalProperties: false
      description: |
        coach layout providing data to draw a coach layout. The items of a coach
        are located via coordinates with (0,0) in the upper left corner. All
        coordinates are given without sign.
      required:
        - id
        - gridSize
      properties:
        id:
          description: |
            id of this coachLayout on this server
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the coach layout.
          type: string
          nullable: true
          example: Coach 3 in train T-122
        places:
          description: |
            list of places included in the layout
          type: array
          items:
            $ref: '#/components/schemas/CoachLayoutPlace'
        signs:
          type: array
          items:
            $ref: '#/components/schemas/CoachLayoutSign'
        internals:
          type: array
          items:
            $ref: '#/components/schemas/CoachLayoutInternal'
        compartmentNumbers:
          type: array
          items:
            $ref: '#/components/schemas/CoachLayoutCompartmentNumber'
        gridSize:
          $ref: '#/components/schemas/CoachLayoutGridSize'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    CoachLayoutCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        layouts:
          type: array
          items:
            $ref: '#/components/schemas/CoachLayout'

    CoachLayoutGridSize:
      type: object
      additionalProperties: false
      required:
        - x
        - y
      properties:
        x:
          type: integer
          format: int32
          nullable: false
        y:
          type: integer
          format: int32
          nullable: false

    CoachLayoutInternal:
      type: object
      additionalProperties: false
      required:
        - icon
        - coords
      properties:
        icon:
          type: string
          nullable: false
        mounting:
          $ref: '#/components/schemas/MountingType'
        coords:
          $ref: '#/components/schemas/LayoutCoordinates'

    CoachLayoutPlace:
      type: object
      additionalProperties: false
      required:
        - icon
        - number
        - coords
      properties:
        icon:
          type: string
          description: |
            Icon type of the graphical item type. Code list according to UIC90918-1
            graphical item type e.g. seat = 1
          nullable: false
        number:
          description: |
            place number as displayed physically on the place
          type: string
          nullable: false
        direction:
          $ref: '#/components/schemas/DirectionType'
        remarkId:
          $ref: '#/components/schemas/RemarkType'
        coords:
          $ref: '#/components/schemas/LayoutCoordinates'

    CoachLayoutResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        coachLayout:
          $ref: '#/components/schemas/CoachLayout'

    CoachLayoutSign:
      type: object
      additionalProperties: false
      required:
        - icon
        - coords
      properties:
        icon:
          description: |
            Icon type of the graphical item type. Code list according to UIC90918-1
            graphical item type e.g. silence area sign = 116

          type: string
          nullable: false
        direction:
          $ref: '#/components/schemas/DirectionType'
        coords:
          $ref: '#/components/schemas/LayoutCoordinates'

    CoachLayoutCompartmentNumber:
      description: Layout item to place a compartment number. The number has no icon, it only places the number
      type: object
      additionalProperties: false
      required:
        - number
        - coords
      properties:
        number:
          type: string
        coords:
          $ref: '#/components/schemas/LayoutCoordinates'

    CombinationTag:
      type: object
      additionalProperties: false
      description: |
        Tag to indicate that some products from a provider can be sold only when in conjunction
        with product(s) from another provider using the same tag. At least one, not all,
        combinationTags must be in common to allow combination. No combinationTags indicate
        that there are no combination constraints on the product.
      required:
        - id
      properties:
        id:
          type: string
          nullable: false
        needsToBeCombined:
          description: |
            If false it means that this product can be sold also when not in conjunction with
            a product with the same tag. This is needed to propose specific products from one
            provider that depend on public ones of another.
          type: boolean
          nullable: true
          default: false

    CompanyDetail:
      type: object
      additionalProperties: false
      required:
        - name
        - registrationNumber
        - taxId
      properties:
        name:
          type: string
          nullable: false
        registrationNumber:
          type: string
          nullable: false
        legalAddress:
          $ref: '#/components/schemas/Address'
        taxId:
          type: string
          nullable: false

    CompanyRef:
      type: string
      description: |
        A unique Id to identify the carrier. For rail, a Railway Interchange Coding System (RICS) company code 
        or compatible European Union Agency for Railways (ERA) company code are used (e.g.,"urn:uic:rics:1185:000011")
      nullable: false

    Compartment:
      type: object
      additionalProperties: false
      description: |
        Areas within a coach that holds places. Areas might be selectable
        as a whole. Usual areas would be classic compartments.
      required:
        - places
        - status
      properties:
        places:
          type: array
          items:
            $ref: '#/components/schemas/PlacePosition'
          minItems: 1
        number:
          description: Compartment number
          type: string
        isSelectable:
          description: |
            Indicates whether compartments are selectable as a whole only
          type: boolean
          nullable: true
          default: false
        reservationRefs:
          description: |
            references to the reservations/fares for which this place can be selected. The reservation ids must be part of the list of reservation ids for which the consumer has requested the available places and the consumer must accept a list by setting singleSelectionMapsRequired to false
          type: array
          items:
            type: string
        status:
          $ref: '#/components/schemas/AvailabilityStatus'
        travelClass:
          $ref: '#/components/schemas/TravelClass'
        serviceClass:
          $ref: '#/components/schemas/ServiceClassType'

    Complaint:
      type: object
      additionalProperties: false
      required:
        - id
        - status
      properties:
        id:
          type: string
          nullable: false
        status:
          $ref: '#/components/schemas/BackOfficeStatus'
        customerComplaint:
          $ref: '#/components/schemas/CustomerComplaint'
        missingInformation:
          description: |
            indication of missing customer information
          type: array
          items:
            type: string
          nullable: true
        decision:
          $ref: '#/components/schemas/ComplaintDecision'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    ComplaintChangeReason:
      description: |
        Known Values:
        - 'BANK_ACCOUNT'
        - 'SETTLED_TIME_LIMIT_EXCEEDED': A distributor settled the claim as the fare provided did not reply in due time. The compensation amount debited is provided.
        - 'PASSENGER_CHANGE'
      type: string
      x-extensible-enum:
        - 'BANK_ACCOUNT'
        - 'SETTLED_TIME_LIMIT_EXCEEDED'
        - 'PASSENGER_CHANGE'

    ComplaintDecision:
      type: object
      additionalProperties: false
      properties:
        compensationAmount:
          $ref: '#/components/schemas/Price'
        refundVoucher:
          $ref: '#/components/schemas/FulfillmentDocument'
        explanation:
          $ref: '#/components/schemas/SupportingDocument'
        shortExplanation:
          type: string
          nullable: true
        delayedJourney:
          description: |
            Allocator or fare provider view on the delays. This might differ from the customer view.
          type: array
          items:
            $ref: '#/components/schemas/Trip'

    ComplaintPatchRequest:
      type: object
      additionalProperties: false
      description: |
        Changes to a claim already made.
        This covers upload of additional supporting documents, changes to  the passenger and claim manager data.
        The patch of the compensation amount requires an agreement between the involved parties and is used in
        case the legal time line for deciding the claim is passed.
      properties:
        reason:
          $ref: '#/components/schemas/ComplaintChangeReason'
        compensationAmount:
          $ref: '#/components/schemas/Price'
        claimManager:
          $ref: '#/components/schemas/PersonDetail'
        affectedPassengers:
          type: array
          items:
            $ref: '#/components/schemas/Passenger'
        supportingDocuments:
          type: array
          items:
            $ref: '#/components/schemas/SupportingDocument'
        bankAccount:
          $ref: '#/components/schemas/BankAccountReference'

    ComplaintResponse:
      type: object
      additionalProperties: false
      required:
        - complaint
      properties:
        complaint:
          $ref: '#/components/schemas/Complaint'
        warnings:
          $ref: '#/components/schemas/WarningCollection'

    Condition:
      type: object
      additionalProperties: false
      description: |
        Describes sales, usage and after sales conditions applicable to the product.  See after sales conditions for individual after sales amounts. The language of the text is controlled by the accept-language header, i.e. supporting English is a must.
      required:
        - type
        - description
      properties:
        type:
          $ref: '#/components/schemas/ConditionType'
        description:
          description: |
            A human-readable explanation of the rule or restriction. (e.g., "Travel is allowed via any permitted route.").
          type: string
          minLength: 1
          nullable: false
        shortDescription:
          description: |
            A short name for the rule or restriction typically used for quick reference (e.g., “FareRouteRestriction”) or where space is restricted, for example, on a mobile phone.  Limited to 64 characters.
          type: string
          nullable: true

    ConditionType:
      type: string
      x-extensible-enum:
        - 'PRE_SALE'
        - 'SALE'
        - 'TRAVEL'
        - 'PLACE_CHANGE'
        - 'FULFILLMENT'
        - 'EXCHANGE'
        - 'REFUND'

    ContactDetail:
      type: object
      additionalProperties: false
      description: |
        Contact information needed to contact a person. Either an email, a phone number or an address needs to be provided.
      properties:
        summary:
          description: |
            A human-readable description of the contact.
          type: string
          nullable: true
        preferredLanguage:
          description: |
            ISO-639-1 language code
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        phoneNumber:
          description: |
            Preferably a mobile phone number, to be contacted in case changes to the booking occur, e.g., in case of delays or interruptions .
          type: string
          nullable: true
        address:
          type: string
          nullable: true

    ContextType:
      type: string
      enum:
        - 'OFFER'
        - 'BOOKING'

    ContinuousLeg:
      type: object
      additionalProperties: false
      description: |
        A leg of a journey that is not bound to a timetable. Provided by OJP.
      required:
        - start
        - end
        - service
      properties:
        start:
          $ref: '#/components/schemas/Place'
        end:
          $ref: '#/components/schemas/Place'
        service:
          $ref: '#/components/schemas/ContinuousService'
        timeWindowStart:
          description: |
            Time at which window begins.
          type: string
          format: date-time
          nullable: true
        timeWindowEnd:
          description: |
            Time at which window ends.
          type: string
          format: date-time
          nullable: true
        duration:
          description: |
            Duration of this leg according to user preferences like walk speed.
          type: string
          format: duration
          nullable: true
          example: 1800S
        situationFullRefs:
          type: array
          items:
            $ref: '#/components/schemas/SituationFullRef'
        co2Emission:
          $ref: '#/components/schemas/Quantity'

    ContinuousMode:
      description: |
        Types of modes that run at any time without a timetable. Provided by OJP.
      type: string
      x-extensible-enum:
        - 'WALK'
        - 'DEMAND_RESPONSIVE'
        - 'REPLACEMENT_SERVICE'

    ContinuousService:
      type: object
      additionalProperties: false
      description: |
        A vehicle movement on a continuous, non-timetabled service.
        Service of this leg. May be 'walk' in most cases, but also cycling or taxi etc.
        Provided by OJP.
      required:
        - operatingDayRef
        - journeyRef
        - mode
        - publishedServiceName
        - originText
        - destinationText
      properties:
        continuousMode:
          $ref: '#/components/schemas/ContinuousMode'
        individualMode:
          $ref: '#/components/schemas/IndividualMode'
        operatingDayRef:
          $ref: '#/components/schemas/OperationDayRef'
        journeyRef:
          $ref: '#/components/schemas/JourneyRef'
        mode:
          type: string
          nullable: false
          example: Ask Stefan
        publishedServiceName:
          type: string
          nullable: false
          example: Circle Line
        originText:
          type: string
          nullable: false
        destinationText:
          type: string
          nullable: false

    ControlSecurityType:
      description: |
        Known Values:
        - 'SiP': secure paper tickets
        - 'SiD': security in data (signed bar code)
        - 'SiS': security in system
      type: string
      x-extensible-enum:
        - 'SiP'
        - 'SiD'
        - 'SiS'

    CorporateCode:
      type: object
      additionalProperties: false
      description: |
        The corporate code is a token that reduces the price of an offer or enables  offers that are not available without it. Needs with a commercial agreement between parties.
      required:
        - code
        - issuer
      properties:
        code:
          description: |
            The corporate code issued by the referenced company.
          type: string
          nullable: false
          example: 123-232
        beneficiary:
          description: |
            The name of the party that benefits from the corporate code, intended to be presented to the end user.
          type: string
          nullable: true
          example: Siemens
        issuer:
          $ref: '#/components/schemas/CompanyRef'

    CountryCode:
      type: string
      description: |
        The ISO 3166-1 alpha-2 country code.
      nullable: false
      example: 'DE'

    Currency:
      type: string
      description: |
        The ISO 4217 currency code.
      nullable: false
      example: 'EUR'

    CustomerComplaint:
      type: object
      additionalProperties: false
      description: |
        complaint details provided by the passengers
      properties:
        applicationTime:
          description: |
            date and time when the claim was made. This starts the legal time line to process the claim
          type: string
          format: date-time
          nullable: true
        journeyDetails:
          $ref: '#/components/schemas/ClaimedJourneyDetails'
        claimManager:
          $ref: '#/components/schemas/PersonDetail'
        affectedPassengers:
          type: array
          items:
            $ref: '#/components/schemas/Passenger'
        supportingDocuments:
          type: array
          items:
            $ref: '#/components/schemas/SupportingDocument'
        bookingIds:
          description: |
            list of booking Ids
          type: array
          items:
            type: string
          nullable: true
        ticketControlNumber:
          description: |
            list of ticket control number (visible ticket identification for the customer)
          type: array
          items:
            type: string
          nullable: true
        requestedPaymentType:
          $ref: '#/components/schemas/PaymentType'
        bankAccount:
          $ref: '#/components/schemas/BankAccountReference'

    DateRange:
      type: object
      additionalProperties: false
      properties:
        startTime:
          type: string
          format: date-time
          nullable: true
        endDate:
          type: string
          format: date-time
          nullable: true

    DatedJourney:
      type: object
      additionalProperties: false
      description: |
        Details about the transport service used in the leg, including the mode, service name, and service status.
      required:
        - vehicleNumbers
        - carriers
        - journeyRef
        - operatingDayRef
      properties:
        operatingDayRef:
          $ref: '#/components/schemas/OperationDayRef'
        # SilverRail Change - Added journeyRef as per OJP, can be used to make subsequent requests
        journeyRef:
          allOf:
            - $ref: '#/components/schemas/JourneyRef'
            - nullable: false # The referenced JourneyRef is nullable, this overrides that
        mode:
          $ref: '#/components/schemas/Mode'
        productCategory:
          $ref: '#/components/schemas/ProductCategory'
        publishedServiceName:
          description: |
            The publicly known name of the service.
          type: string
          nullable: true
          example: S1
        vehicleNumbers:
          type: array
          description: |
            A list of vehicle numbers assigned to the leg.
          items:
            $ref: '#/components/schemas/VehicleNumber'
          minItems: 1
        lineNumbers:
          type: array
          items:
            $ref: '#/components/schemas/LineNumber'
        serviceStatus:
          $ref: '#/components/schemas/ServiceStatus'
        situationFullRefs:
          type: array
          items:
            $ref: '#/components/schemas/SituationFullRef'
        carriers:
          type: array
          items:
            $ref: '#/components/schemas/NamedCompany'
          minItems: 1
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/GeneralAttribute'

    DayTravelAccountUnit:
      allOf:
        - $ref: '#/components/schemas/AbstractTravelAccountUnit'
        - type: object
          additionalProperties: false

    DirectionType:
      description: |
        Direction of a place, defined optional as it might be omitted in
        case of berths in future. 
        Code list according to UIC90918-1
        Valid Values:
        - RIGHT: to right
        - LEFT: to left
        - UP: up (from right side of a coach pointing to the middle of the aisle)
        - DOWN: down (from right side of the coach pointing to the middle of the aisle)
      type: string
      enum:
        - 'RIGHT'
        - 'LEFT'
        - 'UP'
        - 'DOWN'

    Document:
      type: object
      additionalProperties: false
      description: |
        Non travel document created. Either downloadLink + downloadExpiry or content + format must be provided.
      required:
        - id
        - type
        - scope
      properties:
        id:
          type: string
          nullable: false
        passengerRef:
          description: |
            Reference to a passenger, to be specified when the document related to an individual passenger.
          type: string
          nullable: true
        downloadLink:
          type: string
          format: uri
          nullable: true
        downloadExpiry:
          description: |
            Expiration time of the download link, the document will not be available at the given URI  after this time.
          type: string
          format: date-time
          nullable: true
        content:
          description: |
            base64 encoded binary of the actual fulfillment document
          type: string
          format: byte
          nullable: true
        format:
          description: |
            Physical format of the document provided in the content field in Mime-Type format, e.g. application/pdf, image/jpeg, etc. Must be filled if the 'content' field is present.
          type: string
          nullable: true
          example: application/pdf
        type:
          $ref: '#/components/schemas/DocumentType'
        scope:
          $ref: '#/components/schemas/DocumentScope'

    DocumentCollectionResponse:
      type: object
      additionalProperties: false
      required:
        - documents
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        documents:
          type: array
          items:
            $ref: '#/components/schemas/Document'
          minItems: 1

    DocumentRequest:
      type: object
      additionalProperties: false
      required:
        - document
      properties:
        document:
          $ref: '#/components/schemas/DocumentSpecification'

    DocumentResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        document:
          $ref: '#/components/schemas/Document'

    DocumentScope:
      type: string
      enum:
        - 'BOOKING'
        - 'PASSENGER'

    DocumentSpecification:
      type: object
      additionalProperties: false
      description: |
        Non travel document created. Either downloadLink + downloadExpiry or content + format must be provided.
      required:
        - scope
        - type
      properties:
        passengerRef:
          description: |
            Reference to a passenger, to be specified when the document related to an individual passenger.
          type: string
          nullable: true
        content:
          description: |
            base64 encoded binary of the actual fulfillment document
          type: string
          format: byte
          nullable: true
        format:
          description: |
            Physical format of the document provided in the content field in Mime-Type format, e.g. application/pdf, image/jpeg, etc. Must be filled if the 'content' field is present.
          type: string
          nullable: true
          example: application/pdf
        scope:
          $ref: '#/components/schemas/DocumentScope'
        type:
          $ref: '#/components/schemas/DocumentType'

    DocumentType:
      type: string
      enum:
        - 'BOOKING_RECEIPT'
        - 'CO2_REPORT'
        - 'INFORMATION'
        - 'COMPLAINT_EXPLANATION'

    Event:
      type: object
      additionalProperties: false
      description: |
        Record of an action that occurred in the life cycle of the booking.
      required:
        - id
        - timestamp
        - type
        - details
      properties:
        id:
          description: |
            id of the event
          type: string
          nullable: false
        timestamp:
          description: |
            timestamp of the event
          type: string
          format: date-time
          nullable: false
        type:
          $ref: '#/components/schemas/EventType'
        details:
          $ref: '#/components/schemas/EventDetail'

    EventDetail:
      type: object
      additionalProperties: false
      required:
        - summary
        - requestor
        - change
        - resource
      properties:
        summary:
          description: |
            human readable description of the event
          type: string
          nullable: false
        requestor:
          description: |
            Contains detailed information about who is calling the API. It can include information such as channel, organization, sales unit or workstation id and be used to configure e.g. the fare range provided to the caller. The content of the string is part of a bilateral contract by the two parties and not standardized by OSDM. It is recommend to encrypt the information transferred.
          type: string
          nullable: false
        change:
          $ref: '#/components/schemas/HistoryStatus'
        resource:
          $ref: '#/components/schemas/Resource'

    EventType:
      type: string
      enum:
        - 'BOOKING_REAL_TIME_EVENT_OCCURRED'
        - 'BOOKING_REACCOMMODATED'
        - 'BOOKING_TRIP_CHANGED'
        - 'BOOKING_TRIP_CONFIRMED'
        - 'FULFILLMENT_AVAILABLE'
        - 'DOCUMENT_AVAILABLE'
        - 'FULFILLMENT_REFUNDED'
        - 'FULFILLMENT_EXCHANGED'
        - 'FULFILLMENT_CONTROLLED'
        - 'ACCOMMODATION_RELEASED'
        - 'PURCHASER_CHANGED'
        - 'PASSENGER_CHANGED'
        - 'REFUND_INITIATED'
        - 'EXCHANGE_INITIATED'

    ExchangeOffer:
      type: object
      additionalProperties: false
      required:
        - exchangeFee
        - exchangePrice
        - fulfillments
        - offerId
        - createdOn
        - preBookableUntil
        - passengerRefs
        - admissionOfferParts
      properties:
        exchangeFee:
          $ref: '#/components/schemas/Price'
        exchangePrice:
          $ref: '#/components/schemas/Price'
        exchangeOfferBreakDown:
          type: array
          items:
            $ref: '#/components/schemas/ExchangeOfferItem'
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
          minItems: 1
        offerId:
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the offer.
          type: string
          nullable: true
        offerSummary:
          $ref: '#/components/schemas/OfferSummary'
        createdOn:
          type: string
          format: date-time
          nullable: false
        preBookableUntil:
          description: |
            time until the offer can be pre-booked, however its availability is not guaranteed
          type: string
          format: date-time
          nullable: false
        passengerRefs:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        products:
          type: array
          items:
            $ref: '#/components/schemas/Product'
        tripCoverage:
          $ref: '#/components/schemas/TripCoverage'
        admissionOfferParts:
          $ref: '#/components/schemas/AdmissionOfferPart'
        reservationOfferParts:
          type: array
          items:
            $ref: '#/components/schemas/ReservationOfferPart'
        ancillaryOfferParts:
          type: array
          items:
            $ref: '#/components/schemas/AncillaryOfferPart'
        fees:
          type: array
          items:
            $ref: '#/components/schemas/Fee'
        exchangeBreakdown:
          $ref: '#/components/schemas/ExchangeBreakDown'
        fares:
          type: array
          items:
            $ref: '#/components/schemas/Fare'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    ExchangeBreakDown:
      type: object
      description: breakdown of fees taken in a refund
      properties:
        refundItems:
          type: array
          items:
            $ref: '#/components/schemas/ExchangeBreakdownItem'
          minItems: 1

    ExchangeBreakdownItem:
      type: object
      required:
        - bookingPartIds
        - exchangeFee
        - exchangePrice
      properties:
        exchangeFee:
          $ref: '#/components/schemas/Price'
        exchangePrice:
          $ref: '#/components/schemas/Price'
        refundableAmount:
          $ref: '#/components/schemas/Price'
        bookingPartIds:
          type: array
          items:
            type: string

    ExchangeOfferCollectionRequest:
      type: object
      additionalProperties: false
      description: |
        Defines the parameters needed to request an exchange offer, either based on either an
        existing trip (that is then passed in) plus a set of offer search
        criteria, or based on trip-search and offer search criteria. At least one
        of the trip or tripSearchCriteria must be set.
      required:
        - fulfillmentIds
        - anonymousPassengerSpecifications
      properties:
        fulfillmentIds:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        overruleCode:
          $ref: '#/components/schemas/OverruleCode'
        tripSpecifications:
          type: array
          items:
            $ref: '#/components/schemas/TripSpecification'
        tripIds:
          type: array
          items:
            type: string
          nullable: true
        tripSearchCriteria:
          $ref: '#/components/schemas/TripSearchCriteria'
        nonTripSearchCriteria:
          $ref: '#/components/schemas/NonTripSearchCriteria'
        requestedSections:
          type: array
          items:
            $ref: '#/components/schemas/Section'
        offerSearchCriteria:
          $ref: '#/components/schemas/OfferSearchCriteria'
        anonymousPassengerSpecifications:
          type: array
          items:
            $ref: '#/components/schemas/AnonymousPassengerSpecification'
          minItems: 1
        corporateCodes:
          type: array
          items:
            $ref: '#/components/schemas/CorporateCode'
        promotionCodes:
          type: array
          items:
            $ref: '#/components/schemas/PromotionCode'
        requestedFulfillmentOptions:
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentOption'
        embed:
          type: array
          items:
            $ref: '#/components/schemas/OfferCollectionResponseContent'

    ExchangeOfferCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        exchangeOffers:
          type: array
          items:
            $ref: '#/components/schemas/ExchangeOffer'
        trips:
          description: |
            Two trips are returned if  round trips are exchanged.
          type: array
          items:
            $ref: '#/components/schemas/Trip'
        passengers:
          type: array
          items:
            $ref: '#/components/schemas/Passenger'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    ExchangeOfferCollectionResponseContent:
      type: string
      enum:
        - 'ALL'
        - 'EXCHANGEOFFERS'
        - 'TRIP'
        - 'PASSENGERS'
        - 'NONE'
      default: 'ALL'

    ExchangeOperation:
      type: object
      additionalProperties: false
      required:
        - id
      properties:
        id:
          type: string
          nullable: false
        status:
          $ref: '#/components/schemas/ExchangeStatus'
        exchangeOffers:
          type: array
          items:
            $ref: '#/components/schemas/ExchangeOffer'
        ticketTimeLimit:
          type: string
          format: date-time
          nullable: true
        fulfillmentType:
          $ref: '#/components/schemas/FulfillmentType'
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
        issuedVouchers:
          type: array
          items:
            $ref: '#/components/schemas/VoucherInformation'
        trips:
          type: array
          items:
            $ref: '#/components/schemas/Trip'

    ExchangeOperationPatchRequest:
      type: object
      additionalProperties: false
      description: |
        Allows modifying a general setting for the booking:
         * setting the required fulfillment type, e.g. value paper
           or e-ticket and preferred fulfillment media.
           The latter is optional and relevant in some cases only.
         * Provisionally exchanging some of the fulfillments in the booking
      properties:
        requestedFulfillmentType:
          $ref: '#/components/schemas/FulfillmentType'
        preferredFulfillmentMedia:
          $ref: '#/components/schemas/FulfillmentMediaType'
        embed:
          description: |
            Influences whether referenced resources are returned in full or as references only.
          type: array
          items:
            $ref: '#/components/schemas/ExchangeOperationResponseContent'

    ExchangeOperationRequest:
      type: object
      additionalProperties: false
      required:
        - exchangeOffers
      properties:
        exchangeOffers:
          type: array
          items:
            $ref: '#/components/schemas/OfferSelection'
          minItems: 1

    ExchangeOperationResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        exchangeOperation:
          $ref: '#/components/schemas/ExchangeOperation'

    ExchangeOperationResponseContent:
      description: |
        Influences whether referenced resources are returned in full or as references only.
      type: string
      enum:
        - 'ALL'
        - 'EXCHANGEOPERATION'
        - 'EXCHANGEOPERATION_EXCHANGEOFFERS'
        - 'EXCHANGEOPERATION_FULFILLMENTS'
      default: 'ALL'

    ExchangeStatus:
      type: string
      enum:
        - 'PREBOOKED'
        - 'CONFIRMED'
        - 'FULFILLED'
        - 'ERROR'

    ExchangeableType:
      description: |
        If set to YES the offerpart is exchangeable without a fee before the day of departure. 
        NO when not exchangeable or exchangeable with 100% fee, WITH_CONDITIONS in all other cases.
      type: string
      enum:
        - "YES" # SilverRail change
        - "NO" # SilverRail change
        - 'WITH_CONDITION'

    ExchangeOfferItem:
      type: object
      required:
        - exchangeFee
        - exchangePrice
        - bookingParts
      properties:
        exchangeFee:
          description: Amount kept by the carrier and/or the distributor
          $ref: '#/components/schemas/Price'
        exchangePrice:
          $ref: '#/components/schemas/Price'
        bookingParts:
          type: array
          items:
            $ref: '#/components/schemas/BookingPartReference'


    ExcludedTimeRange:
      type: object
      additionalProperties: false
      description: |
        time ranges excluded from the validity (e.g. off peak fulfillments)
      required:
        - from
        - until
        - scope
      properties:
        from:
          description: |
            minutes of the day in the time zone of travel
          type: integer
          format: int32
          nullable: false
        until:
          description: |
            minutes of the day in the time zone of travel
          type: integer
          format: int32
          nullable: false
        scope:
          $ref: '#/components/schemas/ExclusionScope'

    ExclusionScope:
      type: string
      enum:
        - 'START_OF_TRAVEL'
        - 'COMPLETE_RANGE'

    Fare:
      type: object
      additionalProperties: false
      description: |
        The online representation of a fare.
      required:
        - id
        - type
        - prices
        - regionalConstraint
        - travelClass
        - afterSalesCondition
        - combinationConstraint
        - travelValidityConstraint
        - requiredCards
      properties:
        id:
          description: |
            id of the fare item to be included in accounting
          type: string
          nullable: false
        type:
          $ref: '#/components/schemas/FareType'
        name:
          $ref: '#/components/schemas/Text'
        description:
          $ref: '#/components/schemas/Text'
        prices:
          description: |
            allows specifying a price in multiple currencies
          type: array
          items:
            $ref: '#/components/schemas/Price'
          minItems: 1
        regionalConstraint:
          $ref: '#/components/schemas/RegionalConstraint'
        serviceConstraint:
          $ref: '#/components/schemas/ServiceConstraint'
        carrierConstraint:
          $ref: '#/components/schemas/CarrierConstraint'
        regulatoryConditions:
          type: array
          items:
            $ref: '#/components/schemas/RegulatoryCondition'
        serviceClass:
          $ref: '#/components/schemas/ServiceClass'
        travelClass:
          $ref: '#/components/schemas/TravelClass'
        afterSalesCondition:
          $ref: '#/components/schemas/AfterSalesConditionsLink'
        combinationConstraint:
          type: array
          items:
            $ref: '#/components/schemas/FareCombinationModel'
          minItems: 1
        fulfillmentConstraint:
          $ref: '#/components/schemas/FulfillmentConstraint'
        travelValidityConstraint:
          $ref: '#/components/schemas/TravelValidity'
        availablePlaces:
          type: array
          items:
            $ref: '#/components/schemas/AvailablePlace'
        placeSelection:
          $ref: '#/components/schemas/PlaceSelection'
        placeAllocation:
          type: array
          items:
            $ref: '#/components/schemas/PlaceAllocation'
        coveredSection:
          $ref: '#/components/schemas/Section'
        passengerRefs:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        passengerConstraints:
          description: |
            passenger constraint to be included in bar codes
          type: array
          items:
            $ref: '#/components/schemas/PassengerConstraints'
        involvedTCOs:
          type: array
          items:
            $ref: '#/components/schemas/CompanyRef'
        legacyAccountingIdentifier:
          $ref: '#/components/schemas/LegacyAccountingIdentifier'
        legacyReservationParameter:
          $ref: '#/components/schemas/LegacyReservationParameter'
        requiredCards:
          description: |
            One of the listed cards is required to be valid at the time of travel.
          type: array
          items:
            $ref: '#/components/schemas/CardReference'
          minItems: 1
        luggageConstraint:
          $ref: '#/components/schemas/LuggageConstraint'
        availablePreferences:
          type: array
          items:
            $ref: '#/components/schemas/AvailablePlacePreferences'

    FareCombinationModel:
      type: object
      additionalProperties: false
      description: |
        Defines the model according to which the fares can be combined.
      required:
        - model
      properties:
        model:
          description: |
            A distributor needs to support the following models:  SEPARATE_TICKET, SEPARATE_CONTRACT, CLUSTERING, COMBINING.

          type: string
          nullable: false
        combinableCarriers:
          description: |
            List of all carriers where the model can be applied, in case the list is empty
            all combinations are allowed.
          type: array
          items:
            $ref: '#/components/schemas/CompanyRef'
        isValidOnlyWhenCombined:
          description: |
            This combination model applies only in case the fare is combined with another carrier
          type: boolean
          nullable: true
          default: false
        referenceCluster:
          description: |
            In case of CLUSTERING model: the cluster to which the fare belongs
          type: string
          nullable: true
        allowedClusters:
          description: |
            In case of CLUSTERING model: the other clusters that allow a combination
          type: array
          items:
            type: string
          nullable: true
        allowedCommonContracts:
          description: |
            List of carriers where a common contract with separate fulfillments are allowed.
          type: array
          items:
            $ref: '#/components/schemas/CompanyRef'

    FareConnectionPoint:
      description: |
        Connection point connecting two fare regimes. The connection is possible between stations of the two provided station sets.
        A legacy border point code (id) might be provided as an additional code within the stations (code list BORDER_POINT provided in URN).
        In case the connection point is a real station this station is indicated.
        In case the connection point is between stations for each side of the border real stations must be provided.
        Multiple sets of station can be provided in the rare case that the connection point connects more than two station (A-B and A-C).
        Multiple stations within a set at one side of the border might be provided in case of changes (new stations build near the border).

        Providing the UIC code for the station is mandatory.
      allOf:
        - $ref: '#/components/schemas/Place'
        - type: object
          additionalProperties: false
          required:
            - stationSets
          properties:
            name:
              type: string
              nullable: true
            stationSets:
              type: array
              items:
                $ref: '#/components/schemas/StationSet'
              minItems: 1
            ref:
              $ref: '#/components/schemas/FareConnectionPointRef'

    FareConnectionPointRef:
      description: |
        Reference to a Fare Connection Point.
      allOf:
        - $ref: '#/components/schemas/PlaceRef'
        - type: object
          additionalProperties: false
          properties:
            name:
              type: string
              nullable: true

    FareReferenceStation:
      type: object
      additionalProperties: false
      description: |
        Reference to a list of stations jointly valid in a fare. The fare
        reference station defines a set of station that are equivalent from
        a tariff point of view. The code is issued by the carrier. Needed to
        generate bar codes as well as fulfillments.
      required:
        - name
        - code
        - carrier
        - stations
      properties:
        name:
          type: string
          nullable: false
        code:
          type: string
          nullable: false
        carrier:
          $ref: '#/components/schemas/CompanyRef'
        stations:
          type: array
          items:
            $ref: '#/components/schemas/StopPlaceRef'
          minItems: 1

    FareType:
      description: |
        Basic UIC fare types used in 90918-10, 90918-4, and 90918-9.
      type: string
      x-extensible-enum:
        - 'ADMISSION'
        - 'RESERVATION'
        - 'INTEGRATED_RESERVATION'
        - 'ANCILLARY'

    Fee:
      type: object
      additionalProperties: false
      description: |
        Fees are used to represent additional costs for services or products. In contrast to other offer parts in OSDM, the customer is not free whether to choose a fee or not: fees are generated and applied to other services or products by the providing system.
      required:
        - id
        - price
      properties:
        id:
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the fee.
          type: string
          nullable: true
        price:
          $ref: '#/components/schemas/Price'
        applicability:
          $ref: '#/components/schemas/ApplicabilityType'
        distributorBookingRef:
          description: |
            reference to the booking in the downstream distributor system
          type: string
          nullable: true
        refundable:
          $ref: '#/components/schemas/RefundType'
        conditions:
          description: |
            Structured description of the sales or after-sales conditions.
          type: array
          items:
            $ref: '#/components/schemas/Condition'
        productCode:
          description: |
            The product code expressed in the provider system (could be a
            mapping from an even lower-level provider).
          type: string
          nullable: true
          deprecated: true
        productRef:
          type: string
          description: |
            Reference to the Product representing the Fee
        accountingRef:
          $ref: '#/components/schemas/AccountingRef'

    Flexibility:
      description: |
        An indication of the level of flexibility for using the product, for example, whether cancelations are allowed (e.g., "SEMI_FLEXIBLE").
      type: string
      x-extensible-enum:
        - 'FULL_FLEXIBLE'
        - 'SEMI_FLEXIBLE'
        - 'NON_FLEXIBLE'

    Fulfillment:
      type: object
      additionalProperties: false
      description: |
        A fulfillment is a document (or part of a document in distributor mode) that
        - allows the passenger to prove its travel right
        - provides easier access to trains and stations
        - allows an exchange for other services (voucher)
        A fulfillment refers to services of one or multiple offer parts or a fare (distributor mode).
      required:
        - id
        - status
        - bookingRef
        - createdOn
      properties:
        id:
          type: string
          nullable: false
        status:
          $ref: '#/components/schemas/FulfillmentStatus'
        bookingRef:
          description: |
            the id of the booking the fulfillment is part of
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the fulfillment.
          type: string
          nullable: true
        createdOn:
          type: string
          format: date-time
          nullable: false
        controlNumber:
          description: |
            Ticket Control Number.
          type: string
          nullable: true
        bookingParts:
          type: array
          items:
            $ref: '#/components/schemas/BookingPartReference'
        availableUsage:
          $ref: '#/components/schemas/FulfillmentUsage'
        issuer:
          $ref: '#/components/schemas/CompanyRef'
        fulfillmentDocuments:
          description: |
            Final document created for fulfillment.
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentDocument'
        fulfillmentParts:
          description: |
            Fulfillment items to be integrated into tickets (distributor mode).
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentPart'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    FulfillmentActivationPatchRequest:
      type: object
      additionalProperties: false
      description: |
        Changes the fulfillment to status AVAILABLE. In the case of multi-journey  product, one of the fulfillment is
        now 'activated' and can be used to travel.
      required:
        - startOfUsage
      properties:
        selectedZoneIds:
          type: array
          items:
            type: string
          nullable: true
        travelDates:
          type: array
          items:
            type: string
            format: date
          nullable: true
        startOfUsage:
          type: string
          format: date-time
          nullable: false
        fromPlace:
          $ref: '#/components/schemas/PlaceRef'
        stockControlNumbers:
          description: |
            The ids of the secure paper stock that identifies the blank secure paper stock used for printing.

            The stock control number is only used for secure paper stock and triggers a printing of the stock control number in the ticket layout. This establishes a link between paper stock and ticket that is used to control the usage of blank secure paper stock at offices as secure paper has otherwise no intrinsic protection against internal fraud.
          type: array
          items:
            type: string
          nullable: true

    FulfillmentCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
        issuedVouchers:
          type: array
          items:
            $ref: '#/components/schemas/VoucherInformation'

    FulfillmentConstraint:
      type: object
      additionalProperties: false
      required:
        - acceptedControlSecurityTypes
      properties:
        acceptedControlSecurityTypes:
          type: array
          items:
            $ref: '#/components/schemas/ControlSecurityType'
          minItems: 1
        acceptedBarCodes:
          description: |
            for SiD fulfillment one of the listed bar codes is required
          type: array
          items:
            $ref: '#/components/schemas/BarCodeType'
        requiredBarCodes:
          description: |
            One of the listed bar codes must be provided.
          type: array
          items:
            $ref: '#/components/schemas/BarCodeType'
        requiredSiS:
          type: array
          items:
            $ref: '#/components/schemas/SiSType'
        isIndividualTicketingForbidden:
          description: |
            a separate fulfillment per passenger is forbidden
          type: boolean
          nullable: true
          default: false

    FulfillmentDocument:
      type: object
      additionalProperties: false
      description: |
        Document created for fulfillment. Either downloadLink + downloadExpiry or content + format must
        be provided.
      required:
        - medium
        - type
        - format
      properties:
        medium:
          $ref: '#/components/schemas/FulfillmentMediaType'
        type:
          $ref: '#/components/schemas/FulfillmentDocumentType'
        downloadLink:
          type: string
          format: uri
          nullable: true
        downloadExpiry:
          description: |
            Expiration time of the download link, the document will not be available at the given URI
            after this time.
          type: string
          format: date-time
          nullable: true
        content:
          description: |
            base64 encoded binary of the actual fulfillment document
          type: string
          format: byte
          nullable: true
        format:
          description: |
            Physical format of the document provided in the content field in Mime-Type format, e.g.
            application/pdf, image/jpeg, etc. Must be filled if the 'content' field is present.
          type: string
          nullable: false
          example: application/pdf

    FulfillmentDocumentType:
      description: |
        The type of fulfillment document.
      type: string
      x-extensible-enum:
        - 'BOARDING_PASS'
        - 'BOOKING_RECEIPT'
        - 'INFORMATION'
        - 'TICKET'
        - 'VOUCHER'

    FulfillmentId:
      type: string
      nullable: false

    FulfillmentItemSecurityFeature:
      type: object
      additionalProperties: false
      properties:
        type:
          $ref: '#/components/schemas/SecurityFeatureType'
        data:
          description: |
            base 64 encoded data
          type: string
          format: byte
          nullable: true

    FulfillmentItemSecurityFeatureLinks:
      type: object
      additionalProperties: false
      properties:
        type:
          $ref: '#/components/schemas/SecurityFeatureType'
        uri:
          description: |
            link to download the security feature
          type: string
          format: uri
          nullable: true

    FulfillmentMediaType:
      description: |
        The medium of delivery for a ticket or travel right.
      type: string
      x-extensible-enum:
        - 'ALLOCATOR_APP'
        - 'FULFILLMENT_PARTS'
        - 'PDF_A4'
        - 'PKPASS'
        - 'RCCST'
        - 'RCT2'
        - 'UIC_PDF'
        - 'TICKETLESS'

    FulfillmentOption:
      type: object
      additionalProperties: false
      description: |
        A fulfillment option, which define how a ticket or travel right is received or accessed.
      required:
        - type
        - media
      properties:
        type:
          $ref: '#/components/schemas/FulfillmentType'
        media:
          $ref: '#/components/schemas/FulfillmentMediaType'
        # SilverRail change
        code:
          description: |
            A SilverRail provided code that identifies the fulfillment option.
          type: string
          nullable: true

    FulfillmentPart:
      type: object
      additionalProperties: false
      description: |
        Fulfillment items to be integrated in to tickets,
        e.g. visual security elements, additional bar codes, control keys.
      properties:
        fulfillmentMedia:
          description: |
            list of fulfillment media where this item applies
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentMediaType'
        passengerRef:
          description: |
            reference to a passenger
          type: string
          nullable: true
        controlId:
          type: string
          nullable: true
        securityFeatures:
          description: |
            'visual elements, bar codes'
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentItemSecurityFeature'
        securityFeatureLinks:
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentItemSecurityFeatureLinks'
        fulfillmentInformationTexts:
          type: array
          items:
            $ref: '#/components/schemas/TextElement'
        isMandatory:
          description: |
            The use of the provided features is mandatory.
          type: boolean
          nullable: true
          default: false
        securePaperOnly:
          description: |
            The security feature must be used on secure paper.
          type: boolean
          nullable: true
          default: false

    FulfillmentStatus:
      description: |
        Individual fulfillments of a multi journey booking need an separate patch to create the usable fulfillment in status FULFILLED before they are in an intermediate state AVAILABLE where they don't provide a document for travel. Fulfillments can be ON_HOLD in case they are booked but not yet available (e.g. a booking providing two journeys per week) or passed in case the fulfillment was not patched for usage within the required time.
      type: string
      enum:
        - 'AVAILABLE'
        - 'ON_HOLD'
        - 'CONFIRMED'
        - 'FULFILLED'
        - 'CHECKEDIN'
        - 'CANCELLED'
        - 'RELEASED'
        - 'REFUNDED'
        - 'EXCHANGED'
        - 'EXPIRED'

    FulfillmentPostRequest: # SilverRail change
      type: object
      additionalProperties: false
      description: |
        Creates the fulfillment with optional Payment method
      properties:
        usedPaymentMethods:
          type: array
          items:
            $ref: '#/components/schemas/PaymentMethod'

    FulfillmentPatchRequest:
      type: object
      additionalProperties: false
      description: |
        Changes the fulfillment to status FULFILLED.
      properties:
        fulfillmentUpdates:
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentId'

    FulfillmentResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        fulfillment:
          $ref: '#/components/schemas/Fulfillment'

    FulfillmentType:
      description: |
        The type of fulfilment such as a physical ticket or an E-Ticket.
      type: string
      x-extensible-enum:
        - 'ETICKET'
        - 'CIT_PAPER'
        - 'PASS_CHIP'
        - 'PASS_REFERENCE'

    FulfillmentUsage:
      type: object
      additionalProperties: false
      description: |
        In case of multi-journey products describe parameters to be used to change the a fulfillment in status AVAILABLE to FULFILLED so it can be used by the passenger.
      required:
        - maxAvailableZones
      properties:
        fromPlaceRequired:
          type: boolean
          nullable: true
          default: false
        travelDates:
          description: |
            Travel dates that can be selected. Note: date only is used to cover properly the case of passes including
            multiple time zones.
          type: array
          items:
            type: string
          nullable: true
        travelValidityRanges:
          type: array
          items:
            $ref: '#/components/schemas/TravelValidityRange'
        maxAvailableZones:
          description: |
            upper limit to the number of selected zones
          type: integer
          format: int32
          nullable: false
        availableZones:
          type: array
          items:
            $ref: '#/components/schemas/ZoneDefinition'

    Gender:
      description: |
        The gender of the passenger, which is important in the case of night trains.
      type: string
      enum:
        - 'MALE'
        - 'FEMALE'
        - 'X'

    GeneralAttribute:
      type: object
      description: |
        Additional service features or characteristics.
      additionalProperties: false
      required:
        - text
      properties:
        text:
          type: string
          description: |
            A human-readable description of an attribute.
          nullable: false
        code:
          type: string
          description: |
            A short identifier for the attribute.
          nullable: true
        url:
          type: string
          nullable: true

    GeoPosition:
      type: object
      additionalProperties: false
      description: |
        WGS84 coordinates position. Provided by OJP.
      required:
        - longitude
        - latitude
      properties:
        longitude:
          $ref: '#/components/schemas/Longitude'
        latitude:
          $ref: '#/components/schemas/Latitude'

    GeoPositionRef:
      description: |
        Reference to a GeoPosition consisting of latitude and longitude.
      allOf:
        - $ref: '#/components/schemas/PlaceRef'
        - type: object
          additionalProperties: false
          required:
            - latitude
            - longitude
          properties:
            latitude:
              $ref: '#/components/schemas/Latitude'
            longitude:
              $ref: '#/components/schemas/Longitude'

    HistoryStatus:
      type: string
      enum:
        - 'PREBOOKED'
        - 'ON_HOLD'
        - 'CONFIRMED'
        - 'FULFILLED'
        - 'CANCELLED'
        - 'RELEASED'
        - 'REFUNDED'
        - 'EXCHANGED'
        - 'EXCHANGE_ONGOING'
        - 'ERROR'
        - 'AVAILABLE'
        - 'CHECKED_IN'

    IdentificationCard:
      type: object
      additionalProperties: false
      description: |
        Person identification information  to be exchanged for border control if legally required.

        It is not allowed to send personal information not required in the offer reply.
        It is legally not allowed to send these personal data already in the offer request.
      required:
        - id
        - type
      properties:
        id:
          description: |
            Identifier on the document.
          type: string
          nullable: false
        type:
          description: |
            Refer to code list for values
          type: string
          nullable: false
        name:
          description: |
            ICAO transliteration identical as written in the document
          type: string
          nullable: true
        nationality:
          description: |
            ISO 3166 2A codes
          type: string
          nullable: true
        birthCity:
          type: string
          nullable: true
        issuingCity:
          description: |
            place where the document is issued
          type: string
          nullable: true
        residenceCity:
          type: string
          nullable: true
        birthCountryCode:
          $ref: '#/components/schemas/CountryCode'
        issuingCountryCode:
          $ref: '#/components/schemas/CountryCode'
        residenceCountryCode:
          $ref: '#/components/schemas/CountryCode'
        issuingDate:
          type: string
          format: date-time
          nullable: true
        gender:
          $ref: '#/components/schemas/Gender'
        expirationDate:
          type: string
          format: date
          nullable: true

    ImpactType:
      description: |
        Known Values:
        - DELAY: delayed arrival
        - JOURNEY_ABANDONED: trip way abandoned during the travel
        - JOURNEY_NOT_STARTED: trip was not started due to the delay
        - SERVICE_DEGRADED: e.g. service class not provided
      type: string
      x-extensible-enum:
        - 'DELAY'
        - 'JOURNEY_ABANDONED'
        - 'JOURNEY_NOT_STARTED'
        - 'SERVICE_DEGRADED'

    IndicatedConsumption:
      type: object
      additionalProperties: false
      required:
        - amount
      properties:
        unit:
          $ref: '#/components/schemas/AbstractTravelAccountUnit'
        amount:
          type: integer
          format: int32
          nullable: false

    IndicatedTravelAccountConsumption:
      type: object
      additionalProperties: false
      required:
        - amount
      properties:
        amount:
          type: integer
          format: int32
          nullable: false
        scale:
          type: integer
          format: int32
          nullable: true
          default: 2

    IndividualMode:
      description: |
        Modes which an individual powers themselves (such as walk, cycle). Provided by OJP.
      type: string
      x-extensible-enum:
        - 'WALK'
        - 'CYCLE'
        - 'TAXI'
        - 'SELF_DRIVE_CAR'
        - 'OTHERS_DRIVE_CAR'
        - 'MOTORCYCLE'
        - 'TRUCK'
        - 'SCOOTER'
        - 'RIDE_POOL_CAR'
        - 'CAR_SHARING'
        - 'CYCLE_SHARING'
        - 'SCOOTER_SHARING'

    InitialPlaceInput:
      type: object
      additionalProperties: false
      description: |
        Initial input for the place information request. This input defines what is originally looked for.
        Provided by OJP.
      properties:
        name:
          description: |
            Name of the place object which is looked after. This is usually the user's input. If not given,
            the name of the resulting place objects is not relevant.
          type: string
          nullable: true
          example: Bern Bärengraben
        geoPosition:
          $ref: '#/components/schemas/GeoPosition'

    Intermediate:
      type: object
      additionalProperties: false
      description: |
        Details about the stop or station where passengers alight the service for a specific leg of a journey.  It contains information about the departure location, including station details, stop places, scheduled departure times, and status indicators.
      required:
        - stopPlaceRef
        - stopPlaceName
        - serviceArrival
        - serviceDeparture
      properties:
        stopPlaceRef:
          $ref: '#/components/schemas/StopPlaceRef'
        stopPlaceName:
          type: string
          nullable: false
          example: Luzern
        plannedStopPointName:
          description: |
            Name of the bay/quay/terminal where to board/alight from the vehicle. According to
            planned timetable.
          type: string
          nullable: true
          example: 3
        estimatedStopPointName:
          description: |
            Name of the bay/quay/terminal where to board the vehicle. As to the latest realtime status.
          type: string
          nullable: true
          example: 8
        serviceArrival:
          $ref: '#/components/schemas/ServiceTime'
        serviceDeparture:
          $ref: '#/components/schemas/ServiceTime'
        status:
          $ref: '#/components/schemas/StopCallStatus'

    IntermediateSpecification:
      type: object
      additionalProperties: false
      description: |
        Minimal intermediate specification.
      required:
        - stopPlaceRef
        - serviceArrival
        - serviceDeparture
      properties:
        stopPlaceRef:
          $ref: '#/components/schemas/StopPlaceRef'
        serviceArrival:
          $ref: '#/components/schemas/ServiceTime'
        serviceDeparture:
          $ref: '#/components/schemas/ServiceTime'

    JourneyRef:
      type: string
      description: |
        A unique identifier for the journey for reference purposes.
      nullable: true
      example: ServiceJourney:1

    Latitude:
      type: number
      format: double
      description: |
        Latitude from equator. -90 (South) to +90 (North). Decimal degrees. e.g. 56.356.
      minimum: -90.0 # SilverRail change (converted int to float)
      maximum: 90.0 # SilverRail change (converted int to float)
      nullable: false
      example: 47.37818

    LayoutCoordinates:
      type: object
      additionalProperties: false
      required:
        - x
        - y
        - zOrder
      properties:
        x:
          description: |
            horizontal coordinate of the center of the place
          type: integer
          format: int32
          nullable: false
        y:
          description: |
            vertical coordinate of the center of the place
          type: integer
          format: int32
          nullable: false
        zOrder:
          description: |
            Graphical layer where this item has to be placed.
            Code list according to UIC90918-1
              - value 0: lowest layer usually used for walls
              - value 1: middle layer usually used by places
              - value 2: top layer usually used by icons
          type: string
          nullable: false

    LegAttribute:
      type: object
      additionalProperties: false
      description: |
        Attributes that are not valid on the whole service, but only on section of a trip made on a single mode without interchange between boarding and alighting.
      required:
        - text
      properties:
        text:
          description: |
            Text of the attribute to be shown to the user.
          type: string
          nullable: false
        code:
          description: |
            Internal code of the attribute. Can be used for detection of double occurrences. Refers to Service Facilities / Leg Attributes from the Catalog of Code Lists
          type: string
          nullable: true
        fromStopSeqNumber:
          description: |
            The attribute is valid from the stop point with this sequence number within the leg. If missing it is valid from the beginning of the leg.
          type: integer
          format: int32
          minimum: 0
          nullable: true
        toStopSeqNumber:
          description: |
            The attribute is valid to the stop point (inclusively) with this sequence number within the leg. If missing it is valid to the end of the leg.
          type: integer
          format: int32
          minimum: 0
          nullable: true

    LegacyAccountingIdentifier:
      type: object
      additionalProperties: false
      description: |
        identifier of the fare in the current 301 accounting file
      required:
        - serialId
      properties:
        serialId:
          description: |
            sequential number of regional validities
          type: integer
          format: int32
          minimum: 0
          maximum: 99999
          nullable: false
        addId:
          description: |
            Sequential number of regional validities (leading positions in case the series
            is too short).
          type: integer
          format: int32
          minimum: 0
          maximum: 99
          nullable: true
        tariffId:
          description: |
            Tariff id for the old series format.
          type: integer
          format: int32
          minimum: 0
          maximum: 9999
          nullable: true

    LegacyReservationParameter:
      type: object
      additionalProperties: false
      description: |
        reservation parameter to support the UIC 90918-1 interface for booking
      required:
        - travelClass
        - serviceLevelCode
        - serviceCode
      properties:
        travelClass:
          description: |
            90918-1 class code in reservation
          type: string
          nullable: false
        serviceLevelCode:
          description: |
            service level code defined in UIC 90918-1
          type: string
          nullable: false
        serviceCode:
          description: |
            service code to be used in reservation
          type: string
          nullable: false
        berthType:
          description: |
            berth type code in UIC 90918-1 to be used in reservation
          type: string
          nullable: true
        coachTypeCode:
          description: |
            coach type code in UIC 90918-1 to be used in reservation
          type: string
          nullable: true
        compartmentTypeCode:
          description: |
            compartment type code in UIC 90918-1 to be used in reservation
          type: string
          nullable: true
        tariffs:
          description: |
            tariff according to UIC 90918-1 to be used for booking
          type: array
          items:
            $ref: '#/components/schemas/LegacyReservationTariff'

    LegacyReservationTariff:
      type: object
      additionalProperties: false
      description: |
        Legacy tariff code and number of items or persons in case UIC 90918-1 is used for booking.
      properties:
        number:
          description: |
            number of items with this tariff code
          type: integer
          format: int32
          nullable: true
        code:
          description: |
            legacy tariff code
          type: integer
          format: int32
          nullable: true

    Line:
      type: object
      additionalProperties: false
      description: |
        terminalStation - in case the product requires a destination within the zone (e.g. local ticket
        to go to the main rail station).
        entryStation - Station to enter the zone in case the product requires to enter the zone via a
        specific station (e.g. local zone ticket to start from the main rail station).
      required:
        - carrier
      properties:
        binaryLineId:
          description: |
            Id to support local traffic standards (e.g. VDV,...).
          type: string
          format: byte
          nullable: true
        carrier:
          $ref: '#/components/schemas/CompanyRef'
        city:
          type: integer
          format: int32
          nullable: true
        entryStation:
          $ref: '#/components/schemas/StopPlace'
        lineIds:
          type: array
          items:
            type: string
          nullable: true
        terminalStation:
          $ref: '#/components/schemas/StopPlace'
        nutsCode:
          description: |
            Nomenclature des units territoriales statistiques
             COMMISSION REGULATION (EU) No 31/2011
          type: string
          nullable: true

    LineNumber:
      type: string
      description: |
        Especially on regional traffic, a vehicle is defined by a line number.
        E.g. the line numbers of 'S 52' or 'B 19'.
      nullable: true
      example: 52, 19

    Link:
      type: object
      additionalProperties: false
      description: |
        The underlying system may provide links for pagination of links pointing on
        further information (such as HTML download links, web sites or extended APIs).
        Mechanism to add flexible extensions specific to an underlying system. Implemented
        the JSON-HAL specification.
      required:
        - rel
        - href
      properties:
        rel:
          description: |
            the 'rel' field in HATEOAS navigation.
          type: string
          nullable: false
          example: self
        href:
          description: |
            Allows to provide a value to the link, for example id of the targeted resource or displayable representation for the link.
          type: string
          format: uri
          nullable: false
        type:
          description: |
            Hint to indicate the media type expected when dereferencing the target resource.
          type: string
          nullable: true
          example: application/json
        value:
          description: |
            Allows to provide a value to the link, for example id of the targeted resource or displayable representation for the link.
          type: string
          nullable: true

    Longitude:
      type: number
      format: double
      description: |
        Longitude from Greenwich Meridian. -180 (West) to +180 (East). Decimal degrees. eg 2.356.
      minimum: -180.0 # SilverRail change (converted int to float)
      maximum: 180.0 # SilverRail change (converted int to float)
      nullable: false
      example: 8.54021

    LuggageConstraint:
      type: object
      additionalProperties: false
      description: |
        Constraint on the luggage allowed by a passenger to on board.
      properties:
        maxHandLuggage:
          description: |
            standard hand luggage pieces
          type: integer
          format: int32
          nullable: true
        maxLargeLuggage:
          description: |
            standard non-hand luggage pieces
          type: integer
          format: int32
          nullable: true
        restrictions:
          type: array
          items:
            $ref: '#/components/schemas/LuggageRestriction'
        restrictionRules:
          type: array
          items:
            $ref: '#/components/schemas/LuggageRestrictionRuleEnum'

    LuggageDimension:
      type: object
      additionalProperties: false
      required:
        - dimension
        - value
      properties:
        dimension:
          $ref: '#/components/schemas/LuggageDimensionEnum'
        value:
          description: |
            Value of the dimension: weight in kg, length in cm, volume in liter.
          type: string
          nullable: false

    LuggageDimensionEnum:
      description: |
        Type of the dimension. Combined sizes are the sum of the sizes.

        Explanation:  WIDTH_HEIGHT_LENGTH = sum of width, hight and length in cm (= linear equivalent).

      type: string
      enum:
        - 'HEIGHT'
        - 'LENGTH'
        - 'WEIGHT'
        - 'WIDTH'
        - 'WIDTH_HEIGHT'
        - 'WIDTH_HEIGHT_LENGTH'
        - 'VOLUME'

    LuggageRestriction:
      type: object
      additionalProperties: false
      required:
        - dimensionRestrictions
        - numberOfItems
      properties:
        dimensionRestrictions:
          description: |
            The dimension apply to the luggage items as upper limits.
          type: array
          items:
            $ref: '#/components/schemas/LuggageDimension'
          minItems: 1
        numberOfItems:
          description: |
            Number of luggage items allowed with this restriction.
          type: integer
          format: int32
          nullable: false
        registrationIds:
          description: |
            The registrationIds to be included in a bar code on the fulfillment (UIC IRS 90918-4) (e.g. on a luggage tag).
          type: array
          items:
            type: string
          nullable: true

    LuggageRestrictionRuleEnum:
      description: |
        Named luggage restriction rule.

        Valid Values:
        - CAN_CARRY: Weight is ok if you can carry it yourself.
      type: string
      enum:
        - 'CAN_CARRY'

    Mode:
      type: object
      additionalProperties: false
      description: |
        The mode of transport (e.g., TRAIN).
      required:
        - ptMode
      properties:
        ptMode:
          $ref: '#/components/schemas/PTMode'
        name:
          description: |
            Name of the mode
          type: string
          nullable: true
          example: rail
        shortName:
          description: |
            Short name or acronym of the mode
          type: string
          nullable: true
          example: S
        description:
          description: |
            Additional text that further describes the mode
          type: string
          nullable: true

    ModeFilter:
      type: object
      additionalProperties: false
      description: |
        List of public transport modes to include or exclude. Provided by OJP.
      properties:
        exclude:
          description: |
            Whether modes in list are to include or exclude from search.
          type: boolean
          nullable: true
          default: true
        transportModes:
          description: |
            List of Transport modes to include/exclude.
          type: array
          items:
            $ref: '#/components/schemas/Mode'

    MoneyTravelAccountUnit:
      allOf:
        - $ref: '#/components/schemas/AbstractTravelAccountUnit'
        - type: object
          additionalProperties: false
          required:
            - currency
          properties:
            currency:
              $ref: '#/components/schemas/Currency'
            scale:
              type: integer
              format: int32
              nullable: true
              default: 2

    MotorCycle:
      type: object
      additionalProperties: false
      properties:
        isSideCarIncluded:
          type: boolean
          nullable: true
          default: false
        licensePlate:
          description: |
            The license plate is a personal data item and must not be provided in an offer request.
            It must be patched into the offer after the customer accepted the offer for booking
          type: string
          nullable: true

    MountingType:
      type: string
      enum:
        - 'UPPER_BORDER'
        - 'LOWER_BORDER'
        - 'UPPER_TO_LOWER_BORDER'
        - 'FREE'

    MultiRideAccount:
      allOf:
        - $ref: '#/components/schemas/AbstractTravelAccount'
        - type: object
          additionalProperties: false
          required:
            - regionalConstraintSummary
            - balance
          properties:
            regionalConstraint:
              $ref: '#/components/schemas/RegionalConstraint'
            regionalConstraintSummary:
              type: string
              nullable: false
            balance:
              $ref: '#/components/schemas/MultiRideBalance'
            consumptions:
              type: array
              items:
                $ref: '#/components/schemas/TravelAccountConsumption'

    MultiRideBalance:
      type: object
      additionalProperties: false
      description: |
        The balance of the travel account.
      required:
        - total
        - remaining
        - unit
      properties:
        total:
          type: integer
          format: int32
          nullable: false
        remaining:
          type: integer
          format: int32
          nullable: false
        unit:
          $ref: '#/components/schemas/AbstractTravelAccountUnit'

    NamedCompany:
      type: object
      additionalProperties: false
      description: |
        Details of the carrier responsible for providing the service.
      required:
        - ref
      properties:
        ref:
          $ref: '#/components/schemas/CompanyRef'
        name:
          description: |
            The name of the carrier providing the service.
          type: string
          nullable: true
          example: RhB

    NamedTravelAccountUnit:
      allOf:
        - $ref: '#/components/schemas/AbstractTravelAccountUnit'
        - type: object
          additionalProperties: false
          required:
            - name
          properties:
            name:
              type: string
              nullable: false

    NonTripSearchCriteria:
      type: object
      additionalProperties: false
      description: |
        Defines the requested validity when searching specifically for a non-trip Offer. 

        The geographical validity can either be specified by a list of nutsCodes, by a list of places (e.g. stations) with the semantics that trips between either of any of the listed stations are covered (e.g. Frankfurt (Main) Hbf, Friedberg(Hess) and Hanau Hbf, which would imply that any travel between these stations is to be covered), or a list of zones (e.g. with carrier urn:vdv:rmv, the zones with the ids 50, 2501 and 3001 would cover the same area as the list of stations above).
      properties:
        validFrom:
          type: string
          format: date-time
          nullable: true
        nutsCodes:
          description: |
            Nomenclature des units territoriales statistiques COMMISSION REGULATION (EU) No 31/2011
          type: array
          items:
            type: string
          nullable: true
        zones:
          type: array
          items:
            $ref: '#/components/schemas/ZoneDefinition'
        places:
          type: array
          items:
            $ref: '#/components/schemas/PlaceRef'

    NotVia:
      type: object
      additionalProperties: false
      description: |
        Not-via restrictions for a trip, i.e. scheduled stop points or stop places that the trip is not allowed to pass through.  Provided by OJP.
      properties:
        notViaPlace:
          type: array
          items:
            $ref: '#/components/schemas/PlaceRef'

    Offer:
      type: object
      additionalProperties: false
      required:
        - offerId
        - createdOn
        - preBookableUntil
        - passengerRefs
      properties:
        offerId:
          description: |
            A unique identifier for the offer.  This Id is required to create a booking based on the offer.
          type: string
          nullable: false
        summary:
          description: |
            A structured summary of the offer, including key information such as station codes and departure/arrival times.
          type: string
          nullable: true
        offerSummary:
          $ref: '#/components/schemas/OfferSummary'
        createdOn:
          description: |
            An ISO 8601 formatted timestamp for when the offer was created (e.g., "2024-11-18T19:11:07.19Z").
          type: string
          format: date-time
          nullable: false
        preBookableUntil:
          description: |
            An ISO 8601 formatted timestamp indicating when the offer expires. The offer must be booked before this date/time. 
            However, this does not guarantee availability—while the offer remains valid until this timestamp, it may become 
            unavailable earlier due to inventory depletion, demand fluctuations, or operational constraints.
          type: string
          format: date-time
          nullable: false
        passengerRefs:
          description: |
            A list of passenger references associated with this offer.
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        products:
          description: |
            Details of the ticketing products available as part of an offer, booking, or admission.  It contains details such as the product type, conditions, flexibility, and descriptive information.
          type: array
          items:
            $ref: '#/components/schemas/Product'
        tripCoverage:
          $ref: '#/components/schemas/TripCoverage'
        admissionOfferParts:
          type: array
          description: |
            An admission represents a travel right or the entitlement to travel onboard a train between the given origin and destination, following the given route. It does not include   a seat reservation.
          items:
            $ref: '#/components/schemas/AdmissionOfferPart'
        reservationOfferParts:
          type: array
          items:
            $ref: '#/components/schemas/ReservationOfferPart'
        ancillaryOfferParts:
          type: array
          items:
            $ref: '#/components/schemas/AncillaryOfferPart'
        fees:
          type: array
          items:
            $ref: '#/components/schemas/Fee'
        fares:
          type: array
          items:
            $ref: '#/components/schemas/Fare'
        _links:
          description: |
            
            Java Property Name: 'links' 

          type: array
          items:
            $ref: '#/components/schemas/Link'

    OfferCollectionRequest:
      type: object
      additionalProperties: false
      description: |
        Defines the parameters needed to request an offer. Either a tripSearchCriteria, a list of trip specifications, or a list of tripIds can be passed in to request offers.

        If you are searching for fares you pass in the complete trip and the use the requestedSections attribute to define which part(s) you need fares (including virtual border points).

      required:
        - anonymousPassengerSpecifications
      properties:
        tripSpecifications:
          type: array
          items:
            $ref: '#/components/schemas/TripSpecification'
        tripIds:
          type: array
          items:
            type: string
          nullable: true
        tripSearchCriteria:
          $ref: '#/components/schemas/TripSearchCriteria'
        nonTripSearchCriteria:
          $ref: '#/components/schemas/NonTripSearchCriteria'
        requestedSections:
          type: array
          items:
            $ref: '#/components/schemas/Section'
        offerSearchCriteria:
          $ref: '#/components/schemas/OfferSearchCriteria'
        anonymousPassengerSpecifications:
          type: array
          items:
            $ref: '#/components/schemas/AnonymousPassengerSpecification'
          minItems: 1
        corporateCodes:
          type: array
          items:
            $ref: '#/components/schemas/CorporateCode'
        promotionCodes:
          type: array
          items:
            $ref: '#/components/schemas/PromotionCode'
        requestedFulfillmentOptions:
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentOption'
        embed:
          description: |
            Influences whether referenced resources are returned in full or as references only. Proposed default ALL
          type: array
          items:
            $ref: '#/components/schemas/OfferCollectionResponseContent'

    OfferCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        offers:
          description: |
            Ticketing options returned by the system, based on the search criteria.
            Each offer represents a potential fare and includes details such as pricing, validity, restrictions, flexibility and fulfilment options.
          type: array
          items:
            $ref: '#/components/schemas/Offer'
        anonymousPassengerSpecifications:
          description: |
            The passenger details used in the request to filter and refine offers.
          type: array
          items:
            $ref: '#/components/schemas/AnonymousPassengerSpecification'
        trips:
          description: |
            The overall journey structure for the planned travel route, which provides a comprehensive understanding of each trip's schedule, timing, and complexity (such as transfers or delays).
          type: array
          items:
            $ref: '#/components/schemas/Trip'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'
        # SilverRail change
        mapInfoParts:
          type: array
          items:
            $ref: '#/components/schemas/MapInfoPart'
        # SilverRail change
        tripContext:
          $ref: '#/components/schemas/TripContext'

    OfferCollectionResponseContent:
      type: string
      enum:
        - 'ALL'
        - 'OFFERS'
        - 'TRIPS'
        - 'NONE'
      default: 'ALL'

    OfferMode:
      description: |
        The type of passenger grouping for which the offer is valid.  If the offer can be used for individual passengers, the returned value is INDIVIDUAL.  
        If the offer applies to all the passengers as an entire group, the returned value is GROUP.
      type: string
      enum:
        - 'GROUP'
        - 'INDIVIDUAL'

    OfferPartReference:
      type: object
      additionalProperties: false
      description: |
        References all the offer part elements in an uniform format.
        In its part, an OfferPartReference can refer to reservation, admission, ancillary or fee.
      required:
        - id
      properties:
        id:
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the offer part reference.
          type: string
          nullable: true
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    OfferPartType:
      description: |
        Allows to request offer-parts of a certain type.
        To support H/H use ['RESERVATION', 'ANCILLARY'].
        Default is to get search for offers of all types.
      type: string
      enum:
        - 'ADMISSION'
        - 'RESERVATION'
        - 'ANCILLARY'
        - 'FARE_ADMISSION'
        - 'FARE_RESERVATION'
        - 'FARE_ANCILLARY'
        - 'ALL'

    OfferSearchCriteria:
      type: object
      additionalProperties: false
      properties:
        requestedOfferParts:
          type: array
          items:
            $ref: '#/components/schemas/OfferPartType'
        productTags:
          type: array
          items:
            $ref: '#/components/schemas/ProductTag'
          nullable: true
        flexibilities:
          description: |
            Defines the flexibility levels desired of the fares returned.
            This refers to the after sales flexibility levels as defined in IRS-90918-10
          type: array
          items:
            $ref: '#/components/schemas/Flexibility'
        travelClasses:
          description: |
            The classes returned might be different from the requested classes.
          type: array
          items:
            $ref: '#/components/schemas/TravelClass'
        serviceClassTypes:
          description: |
            The classes returned might be different from the requested classes.
          type: array
          items:
            $ref: '#/components/schemas/ServiceClassType'
          maxItems: 4
        offerMode:
          $ref: '#/components/schemas/OfferMode'
        currency:
          $ref: '#/components/schemas/Currency'

    OfferSelection:
      type: object
      additionalProperties: false
      description: |
        The Ids of the offers to be booked need to be passed in.
      required:
        - offerId
        - passengerRefs
      properties:
        offerId:
          description: |
            id of the selected offer or exchangeOffer
          type: string
          nullable: false
        optionalReservationSelections:
          description: |
            Mandatory reservations are booked when the booking is being booked.
          type: array
          items:
            $ref: '#/components/schemas/ReservationSelection'
        optionalAncillarySelections:
          type: array
          items:
            $ref: '#/components/schemas/AncillarySelection'
        placeSelections:
          type: array
          items:
            $ref: '#/components/schemas/PlaceSelection'
        afterSaleByRetailerOnly:
          description: |
            in case the distributor has proposed this offer in conjunction with an offer of another provider constrained
            by a combinationTag, this flag must be set to true to indicate to the provider that after-sale must be done
            on the totality of the distributor's booking. Only after-sale requests triggered by the distributor can be
            safely processed on this offer. When the flag is not set, standard provider logic applies.
          type: boolean
          nullable: true
          default: false
        appliedRegulatoryCondition:
          $ref: '#/components/schemas/RegulatoryCondition'
        passengerRefs:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        throughTicketTags:
          type: array
          items:
            $ref: '#/components/schemas/ThroughTicketTag'

    OfferSummary:
      type: object
      additionalProperties: false
      description: |
        The offer summary indicates the most relevant service class, level, flexibility or accommodation
        that describe this offer, but this does not imply that these values exactly apply to all parts of
        the offer.
        Let's consider, as a simplified example, a trip composed of two legs, one with both 1st and
        2nd class, and one with only 2nd.
        On this trip, the railway is expected to propose one offer with overallServiceClass FIRST composed
        of one admission in 1st class on the first legs and one admission in 2nd class on the second one,
        plus a second offer with overallServiceClass SECOND composed of two second class admissions.
        Likewise for all 'overall' attributes. The purpose of these attributes is to convey to the client
        system the service class, level, flexibility and accommodation that best represent this offer so to
        facilitate presentation to the final user.
      required:
        - minimalPrice
        - overallServiceClass
        - overallFlexibility
      properties:
        minimalPrice:
          $ref: '#/components/schemas/Price'
        overallServiceClass:
          $ref: '#/components/schemas/ServiceClass'
        overallTravelClass:
          $ref: '#/components/schemas/TravelClass'
        overallAccommodationType:
          $ref: '#/components/schemas/AccommodationType'
        overallAccommodationSubType:
          $ref: '#/components/schemas/AccommodationSubType'
        overallFlexibility:
          $ref: '#/components/schemas/Flexibility'
        minimalIndicatedConsumption:
          $ref: '#/components/schemas/IndicatedConsumption'

    OfferTag:
      type: object
      additionalProperties: false
      description: |
        The offerTag can/must (depending on the mandatory flag) be used in some cases to restrict the
        set of offers returned in a subsequent and related offer search to only compatible ones.
        Note the offerTag does not need to be unique.
      properties:
        id:
          type: string
          nullable: true
        isMandatory:
          type: boolean
          nullable: true
          default: false

    OnHoldOffer:
      type: object
      additionalProperties: false
      required:
        - id
        - summary
        - createdOn
        - validFrom
        - validUntil
        - onHoldFee
        - deposit
        - increasedTTL
      properties:
        id:
          type: string
          nullable: false
        summary:
          type: string
          nullable: false
        createdOn:
          type: string
          format: date-time
          nullable: false
        validFrom:
          type: string
          format: date-time
          nullable: false
        validUntil:
          type: string
          format: date-time
          nullable: false
        confirmedOn:
          type: string
          format: date-time
          nullable: true
        onHoldFee:
          $ref: '#/components/schemas/Fee'
        deposit:
          $ref: '#/components/schemas/Price'
        increasedTTL:
          description: |
            Increased time-to-live.
          type: string
          format: date-time
          nullable: false
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    OnHoldOfferPatchRequest:
      type: object
      additionalProperties: false

    OnHoldOfferRequest:
      type: object
      additionalProperties: false
      description: |
        Confirming the offer puts into the 'On-hold' state.
      required:
        - increaseTTL
      properties:
        increaseTTL:
          description: |
            increase of time to live (in minutes)
          type: integer
          format: int32
          nullable: false

    OnHoldOfferResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        onHoldOffer:
          $ref: '#/components/schemas/OnHoldOffer'

    OperatingDays:
      type: object
      additionalProperties: false
      description: |
        Day of public transport operation of which the characteristics are defined in a specific
        service calendar and which may last more than 24 hours. Provided by OJP.
      required:
        - from
        - until
        - pattern
      properties:
        from:
          description: |
            Start date of period
          type: string
          format: date-time
          nullable: false
        until:
          description: |
            End date of period
          type: string
          format: date-time
          nullable: false
        pattern:
          description: |
            Bit pattern for operating days between start date and end date.
            The length of the pattern is equal to the number of days from
            start date to end date. A bit value of '1' indicates that an event
            actually happens on the day that is represented by the bit position.
          type: string
          nullable: false

    OperationDayRef:
      type: string
      description: |
        The operating day or date (ISO 8601 date format) for the service.
      nullable: false # SilverRail Change - make non-nullable by default as this is more aligned with OJP.
      example: OperatingDay:12345 or 2024-11-23 # SilverRail Change - add '2024-11-23' example to provide more flexibility

    OsmTag:
      type: object
      additionalProperties: false
      description: |
        Structure of an Open Street Map tag. Provided by OJP.
      required:
        - tag
        - value
      properties:
        tag:
          description: |
            Name of Open Street Map tag (amenity, leisure, tourism, bike, ...)
          type: string
          nullable: false
          example: name
        value:
          description: |
            Value for Open Street Map tag (charging_station, hostel, yes, ...)
          type: string
          nullable: false
          example: Rozenrust

    OverruleCode:
      description: |
        Reason for and type of an after sale, code list in IRS 90918-10.
        The PRM_SUPPORT_UNAVAILABLE overrule code shall only be used by the UIC PRM ABT tool.
        Known Values:
        - 'CONNECTION_BROKEN'
        - 'DEATH'
        - 'EQUIPMENT_FAILURE'
        - 'PAYMENT_FAILURE'
        - 'PRM_SUPPORT_UNAVAILABLE'
        - 'SALES_STAFF_ERROR'
        - 'STOP_NOT_SERVED'
        - 'STRIKE'
        - 'TECHNICAL_FAILURE'
        - 'TICKET_NOT_USED'
        - 'INABILITY_TO_TRAVEL': Inability to travel due to accident or sickness.
        - 'EXTERNAL_COMPENSATION': Offer has been compensated outside of the provider system in another way.
        - 'DISRUPTION': Inability to operate due to disruption.
        - 'JOURNEY_OBSOLETE': Due to external factors it's senseless to start the trip, thus the travel is obsolete.
        - 'CERTIFIED_MEDICAL_CONDITION': A medical certificate certifies that the passenger is unable to travel.
        - 'DELAY_COMPENSATION': Allows to override conditions in context of passenger rights regulation (PRR).
      type: string
      x-extensible-enum:
        - 'CONNECTION_BROKEN'
        - 'DEATH'
        - 'EQUIPMENT_FAILURE'
        - 'PAYMENT_FAILURE'
        - 'PRM_SUPPORT_UNAVAILABLE'
        - 'SALES_STAFF_ERROR'
        - 'STOP_NOT_SERVED'
        - 'STRIKE'
        - 'TECHNICAL_FAILURE'
        - 'TICKET_NOT_USED'
        - 'INABILITY_TO_TRAVEL'
        - 'EXTERNAL_COMPENSATION'
        - 'DISRUPTION'
        - 'JOURNEY_OBSOLETE'
        - 'CERTIFIED_MEDICAL_CONDITION'
        - 'DELAY_COMPENSATION'

    PRMNeedType:
      type: string
      x-extensible-enum:
        - 'NEED_PRM_SUPPORT'
        - 'WHEELCHAIR'
        - 'ACCOMPANYING_DOG'
        - 'COMPANION_SEAT'
        - 'COMPANION'

    PTMode:
      description: |
        The public transport sub-mode (e.g., HIGH_SPEED_TRAIN).
      type: string
      x-extensible-enum:
        - 'HIGH_SPEED_TRAIN'
        - 'HISTORIC_TRAIN'
        - 'INTERCITY'
        - 'REGIONAL'
        - 'INTERREGIONAL'
        - 'URBAN'
        - 'TRAIN'
        - 'TRAM'
        - 'UNDERGROUND'
        - 'NIGHT_TRAIN'
        - 'SHARED_TAXI'
        - 'MOTOR_RAIL'
        - 'MOUNTAIN_TRAIN'
        - 'PLANE'
        - 'COACH_GROUP'
        - 'SHIP'
        - 'BUS'

    Passenger:
      type: object
      additionalProperties: false
      description: |
        Information about a passenger.

        Either the date of birth or the age at the time of travel needs to be set. We recommend to use date of birth as it is more stable than age.
      required:
        - id
        - externalRef
        - type
      properties:
        id:
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the passenger.
          type: string
          nullable: true
        externalRef:
          description: |
            A stable reference to a passenger from other elements, or from caller system.
            When passed in passenger specification in an offer request, it must be echoed back in the
            response.
          type: string
          nullable: false
        dateOfBirth:
          description: |
            date of birth of the passenger
          type: string
          format: date
          nullable: true
        age:
          type: integer
          format: int32
          minimum: 0
          nullable: true
        type:
          $ref: '#/components/schemas/PassengerType'
        cards:
          description: |
            reduction or loyalty cards owned by the passenger
          type: array
          items:
            $ref: '#/components/schemas/CardReference'
        gender:
          $ref: '#/components/schemas/Gender'
        detail:
          $ref: '#/components/schemas/PersonDetail'
        identificationCard:
          $ref: '#/components/schemas/IdentificationCard'
        transportableDetails:
          $ref: '#/components/schemas/Transportable'
        prmNeeds:
          description: |
            For the persons with reduced mobility (PRMs) its specific needs for support are expressed.
          type: array
          items:
            $ref: '#/components/schemas/PRMNeedType'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    PassengerConstraints:
      type: object
      additionalProperties: false
      properties:
        number:
          description: |
            number of passengers with this constraint
          type: integer
          format: int32
          nullable: true
        upperAgeLimit:
          type: integer
          format: int32
          nullable: true
        lowerAgeLimit:
          type: integer
          format: int32
          nullable: true

    PassengerResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        passenger:
          $ref: '#/components/schemas/Passenger'

    PassengerSpecification:
      type: object
      additionalProperties: false
      description: |
        Minimal specification of a passenger to request offers.

        Either the date of birth or the age at the time of travel needs to be set. We recommend to use date of birth as it is more stable than age.
      required:
        - externalRef
        - type
      properties:
        externalRef:
          description: |
            A stable reference to a passenger from other elements, or from caller system. When received in input of a request, it must be echoed back in the response.
          type: string
          nullable: false
          example: TK-id-12345
        dateOfBirth:
          description: |
            Date of birth of the passenger. Only needed for passengers of type persons, family child, PRM
            and wheelchair.
          type: string
          format: date
          nullable: true
        age:
          type: integer
          format: int32
          minimum: 0
          nullable: true
        cards:
          description: |
            reduction or loyalty cards owned by the passenger
          type: array
          items:
            $ref: '#/components/schemas/CardReference'
        prmNeeds:
          description: |
            For the persons with reduced mobility (PRMs) its specific needs for support are expressed.
          type: array
          items:
            $ref: '#/components/schemas/PRMNeedType'
        detail:
          $ref: '#/components/schemas/PersonDetail'
        type:
          $ref: '#/components/schemas/PassengerType'
        gender:
          $ref: '#/components/schemas/Gender'

    PassengerType:
      description: |
        The UIC passenger type (e.g. “ADULT” or “CHILD”.  Used to assign the correct fare rules or return eligible products and services.
      type: string
      x-extensible-enum:
        - 'DOG'
        - 'PET'
        - 'LUGGAGE'
        - 'PRM'
        - 'BICYCLE'
        - 'PRAM'
        - 'COMPANION_DOG'
        - 'CAR'
        - 'PERSON'
        - 'MOTORCYCLE'
        - 'TRAILER'
        - 'FAMILY_CHILD'
        - 'WHEELCHAIR'
      default: 'PERSON'

    PaymentMethod:
      type: object
      additionalProperties: false
      description: |
        Method of payment used by the customer to the distributor. On input of voucher the remaining sum attribute is not present.
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/PaymentType'
        voucherInformation:
          $ref: '#/components/schemas/VoucherInformation'
        remainingVoucherAmount:
          $ref: '#/components/schemas/Price'
        transactionId:
          type: string
          nullable: true
        transactionIds:
          type: array
          nullable: true
          items:
            type: string
          example: [
            "234567889997",
            "334455778899"
          ]
        #SilverRail Change
        paymentToken:
          description: Tokenised card token.
          example: 5647045c-14eb-47b8-84ca-e806f085ff4a
          type: string
          nullable: true
        method:
          description: general method of payment instrument like Credit (CC), Debit (DB), etc.
          type: string
          default: "CC"
          x-extensible-enum:
            - "CC"
            - "DB"
        fopType:
          description: form-of-payment; more detailed version for payment method. Like visa (VI), amex (AX), etc
          type: string
          default: "VI"
          x-extensible-enum:
            - "VI"
            - "AX"
            - "CA"
            - "UA"
            - "AX"
            - "DI"
            - "DC"
            - "JC"

    PaymentType:
      type: string
      x-extensible-enum:
        - 'CARD'
        - 'CASH'
        - 'DIRECT_DEBIT'
        - 'ELECTRONIC_PAYMENT'
        - 'INVOICE'
        - 'VOUCHER'
        - 'TRAVEL_ACCOUNT'

    RefundMethod:
      type: object
      additionalProperties: false
      description: |
        Method of refunding used by the distributor.
      properties:
        id:
          type: string
          nullable: true
        method:
          $ref: '#/components/schemas/PaymentMethodEnum'
        voucherInformation:
          $ref: '#/components/schemas/VoucherInformation'
        remainingVoucherAmount:
          $ref: '#/components/schemas/Price'
        transactionId:
          type: string
          nullable: true

    PersonDetail:
      type: object
      additionalProperties: false
      description: |
        Personal information potentially needed at booking step. To support privacy by design,
        it is not permitted to send personal information not required to create a valid booking.
      required:
        - firstName
        - lastName
      properties:
        preferredLanguage:
          description: |
            ISO-639-1 language code
          type: string
          deprecated: true
          nullable: true
        summary:
          description: |
            A human-readable description of the person.
          type: string
          nullable: true
        firstName:
          type: string
          nullable: false
        lastName:
          description: full last names of the person
          type: string
          nullable: false
          example: 'Diaz Lopez'
        firstFamilyName:
          description:
            first family name in case of multiple family names. The element
            lastName includes all family names.
          type: string
          nullable: false
          example: 'Diaz'
        secondFamilyName:
          description:
            second family name in case of multiple family names. The element
            lastName includes all family names.
          type: string
          nullable: false
          example: 'Lopez'
        email:
          type: string
          deprecated: true
          nullable: true
        phoneNumber:
          description: |
            Preferably a mobile phone number, to be contacted in case changes to the booking occur, e.g.,
            in case of interruptions.
          type: string
          deprecated: true
          nullable: true
        address:
          $ref: '#/components/schemas/Address'
          nullable: true
        contact:
          $ref: '#/components/schemas/ContactDetail'
        taxId:
          type: string
          description:
            Tax identification of the physical person for issuing an invoice or
            receipt
          nullable: true
          example: CZ1234567890

    PersonSearchRequest:
      type: object
      additionalProperties: false
      properties:
        firstName:
          type: string
          nullable: true
        lastName:
          type: string
          nullable: true
        dateOfBirth:
          type: string
          format: date
          nullable: true
        phoneNumber:
          type: string
          nullable: true
        email:
          type: string
          nullable: true

    Place:
      type: object
      additionalProperties: false
      description: |
        A geographic place of any type which may be specified as the origin or destination of a trip. Provided by OJP.
      discriminator:
        propertyName: objectType
      required:
        - objectType
        - id
      properties:
        objectType:
          description:
            Attribute is used as discriminator for inheritance between data
            types.
          type: string
        id:
          description: |
            id  defining the place. The code is provided as URN, relative URNs are allowed with base path urn:uic:stn '0850000'
          type: string
          nullable: false
          example: urn:uic:stn:8500000
        alternativeIds:
          description: |
            For a place with ids in different reference systems, the alternative ids can be returned. 
            The reference system is encoded in the string. E.g.: 'urn:uic:std:80000', 'x_swe:stn:10000', 'ch:1:sloid:343434'
          type: array
          items:
            type: string
          nullable: true
        geoPosition:
          $ref: '#/components/schemas/GeoPosition'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    PlaceAllocation:
      type: object
      additionalProperties: false
      description: |
        Describes the details of the reserved place(s).
      required:
        - accommodationType
        - accommodationSubType
        - reservedPlaces
        - tripLegCoverage
      properties:
        accommodationType:
          $ref: '#/components/schemas/AccommodationType'
        accommodationSubType:
          $ref: '#/components/schemas/AccommodationSubType'
        reservedPlaces:
          description: |
            Reserved places in a confirmed reservation.
            Multiple place packs are needed to combined person and bicycle reservations
          type: array
          items:
            $ref: '#/components/schemas/ReservedPlace'
          minItems: 1
        tripLegCoverage:
          $ref: '#/components/schemas/TripLegCoverage'

    PlaceAvailability:
      type: object
      additionalProperties: false
      description: |
        Availability of places on vehicle.
      required:
        - vehicle
      properties:
        reference:
          $ref: '#/components/schemas/PartReference'
        vehicle:
          $ref: '#/components/schemas/Vehicle'
        preSelections:
          description: |
            suggested pre-selection of places
          type: array
          items:
            $ref: '#/components/schemas/PlacePreSelection'


    PlaceAvailabilityCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        availablePlaces:
          type: array
          items:
            $ref: '#/components/schemas/AvailablePlace'

    PlaceAvailabilityResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        vehicleAvailability:
          $ref: '#/components/schemas/PlaceAvailability'


    PlaceAvailabilitiesResponse:
      type: object
      additionalProperties: false
      properties:
        warning:
          $ref: '#/components/schemas/WarningCollection'
        vehicleAvailabilities:
          type: array
          items:
            $ref: '#/components/schemas/PlaceAvailability'

    PlaceParam:
      type: object
      additionalProperties: false
      description: |
        More parameters for restricting the request. Provided by OJP.
      properties:
        type:
          $ref: '#/components/schemas/PlaceType'
        usage:
          $ref: '#/components/schemas/PlaceUsage'
        ptModes:
          $ref: '#/components/schemas/ModeFilter'
        serviceBrandFilter:
          $ref: '#/components/schemas/ServiceBrandFilter'
        carrierFilter:
          $ref: '#/components/schemas/CarrierFilter'
        pointOfInterestFilter:
          $ref: '#/components/schemas/PointOfInterestFilter'
        numberOfResults:
          description: |
            Maximum number of results to be returned. The service is allowed to return fewer objects if reasonable
            or otherwise appropriate.
          type: integer
          format: int32
          minimum: 1
          nullable: true

    PlacePosition:
      type: object
      additionalProperties: false
      description: |
        description of a place
      required:
        - number
        - status
      properties:
        number:
          description: |
            place number as indicated at the place in the coach
          type: string
          nullable: false
        status:
          $ref: '#/components/schemas/AvailabilityStatus'
        isSelectable:
          description: |
            Flag to indicate individual places as non-selectable in
            case complete compartments must be selected.
          type: boolean
          nullable: true
          default: true
        reservationRefs:
          description: |
            references to the reservations for which this place nca be selected. The reservation ids must be part of the list of reservation ids for which the consumer has requested the available places and the consumer must accept a list by setting singleSelectionMapsRequired to false
          type: array
          items:
            type: string
        placeProperties:
          type: array
          items:
            $ref: '#/components/schemas/PlaceProperty'

    PlacePreSelection:
      type: object
      additionalProperties: false
      properties:
        reservationId:
          description: id of the reservation in case the places have already been selected and the customer now wants to change the selection.
          type: string
        coach:
          type: string
          nullable: true
        place:
          type: string
          nullable: true

    PlacePreferenceGroup:
      type: object
      additionalProperties: false
      required:
        - preferenceGroup
        - preferences
      properties:
        preferenceGroup:
          type: string
          nullable: false
          example: 'SEAT_DIRECTION'
        preferences:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
          example: 'FORWARD_FACING'

    PlaceProperty:
      type: string
      description: |
        see code list
      nullable: false
      example: Power Connection

    PlaceRef:
      type: object
      description: |
        A specific location, such as a station, stop, or geographic point that identifies a trip origin or destination, a transfer point, or a stop along a journey.
      additionalProperties: false
      discriminator:
        propertyName: objectType
      required:
        - objectType
      properties:
        objectType:
          description: |
            The method used to identify the origin or destination, a transfer point, or a stop.  For example a station’s UID or its geographic coordinates (e.g., "GeoPositionRef").
          type: string

    PlaceRequest:
      type: object
      additionalProperties: false
      description: |
        Initial input for the place information request. This input defines what is originally looked for.
        Provided by OJP.
      properties:
        placeInput:
          $ref: '#/components/schemas/InitialPlaceInput'
        restrictions:
          $ref: '#/components/schemas/PlaceParam'

    PlaceResponse:
      type: object
      additionalProperties: false
      required:
        - places
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        places:
          type: array
          items:
            $ref: '#/components/schemas/Place'
          minItems: 1

    PlaceSelection:
      type: object
      additionalProperties: false
      description: |
        Place selection options and selected options in an offer
        - reference place for adjacent reservation
        - selected optional place properties
        - selected places from a graphical place selection
      required:
        - reservationId
        - tripLegCoverage
      properties:
        reservationId:
          type: string
          nullable: false
        referencePlace:
          $ref: '#/components/schemas/SelectedReferencePlace'
        accommodations:
          type: array
          items:
            $ref: '#/components/schemas/SelectedAccommodation'
        places:
          type: array
          items:
            $ref: '#/components/schemas/SelectedPlace'
        tripLegCoverage:
          $ref: '#/components/schemas/TripLegCoverage'

    PlaceType:
      description: |
        Provided by OJP.
      type: string
      enum:
        - 'STOP'
        - 'ADDRESS'
        - 'POI'
        - 'GEO_COORDINATE'
        - 'TOPOGRAPHIC_PLACE'

    PlaceUsage:
      description: |
        Provided by OJP.
      type: string
      enum:
        - 'ORIGIN'
        - 'VIA'
        - 'DESTINATION'

    PointOfInterest:
      description: |
        A type of place to or through which passengers may wish to navigate as part of
        their journey and which is modelled in detail by journey planners. Provided by OJP.
      allOf:
        - $ref: '#/components/schemas/Place'
        - type: object
          additionalProperties: false
          required:
            - name
            - code
          properties:
            name:
              description: |
                Name or description of point of interest for use in passenger information.
              type: string
              nullable: false
              example: Park Rozenrust
            code:
              description: |
                id of the Point of Interest.
              type: string
              nullable: false
            ref:
              $ref: '#/components/schemas/PointOfInterestRef'

    PointOfInterestCategory:
      type: object
      additionalProperties: false
      description: |
        A categorization of points of interest in respect of the activities undertaken at them (defined by key-value-pairs). Provided by OJP.
      properties:
        osmTags:
          description: |
            Open Street Map tag structure (key-value)
          type: array
          items:
            $ref: '#/components/schemas/OsmTag'
        pointOfInterestClassifications:
          description: |
            Classification of the POI (when it is not from OSM). The codification of the
            classification Id may include de codification source (for example
            'IGN:[classificationCode]')
          type: array
          items:
            type: string
          nullable: true

    PointOfInterestFilter:
      type: object
      additionalProperties: false
      description: |
        Filter POIs by category. Provided by OJP.
      properties:
        exclude:
          description: |
            Whether carriers are to be included or excluded from the filtering.
          type: boolean
          nullable: true
          default: true
        pointOfInterestCategory:
          description: |
            These POI categories can be used to filter POIs. If more than one is given the filtering
            is by logical 'OR' (when Exclude=FALSE) and logical 'AND' (when Exclude=TRUE).
          type: array
          items:
            $ref: '#/components/schemas/PointOfInterestCategory'

    PointOfInterestRef:
      description: |
        Reference to a Point of Interest. Provided by OJP.
      allOf:
        - $ref: '#/components/schemas/PlaceRef'
        - type: object
          additionalProperties: false
          required:
            - pointOfInterestRef
          properties:
            pointOfInterestRef:
              type: string
              nullable: false

    Polygon:
      type: object
      additionalProperties: false
      required:
        - edges
      properties:
        edges:
          type: array
          items:
            $ref: '#/components/schemas/GeoPosition'
          minItems: 1

    Price:
      type: object
      description: |
        The monetary value associated with a ticket, service, or fee.
      additionalProperties: false
      required:
        - currency
        - amount
      properties:
        currency:
          $ref: '#/components/schemas/Currency'
        amount:
          description: |
            The total payable amount, inclusive of any taxes, fees, and discounts.
          type: integer
          format: int32
          nullable: false
        scale:
          description: |
            The number of decimal places used to display the amount.
          type: integer
          format: int32
          nullable: true
          default: 2
        vats:
          type: array
          items:
            $ref: '#/components/schemas/VAT'

    Problem:
      type: object
      additionalProperties: false
      description: |
        Problem Details for HTTP APIs (RFC 7807) defines a 'problem detail' as a way
        to carry machine-readable details of errors in a HTTP response to avoid
        the need to define new error response formats for HTTP APIs.
        See: https://tools.ietf.org/html/rfc7807
      properties:
        code:
          description: |
            optional in version 3.1, will be made mandatory in upcoming releases
          type: string
          nullable: true
        type:
          description: |
            An absolute URI that identifies the problem type. When dereferenced,
            it SHOULD provide human-readable documentation for the problem type
            (e.g., using HTML).
          type: string
          format: uri
          nullable: true
          default: 'about:blank'
          example: https://example.com/probs/passenger-too-young
        title:
          description: |
            A short, summary of the problem type. Written in English and readable
            for engineers (usually not suited for non technical stakeholders and
            not localized);
          type: string
          nullable: true
          example: Service Unavailable
        status:
          description: |
            The HTTP status code generated by the origin server for this occurrence
            of the problem.
          type: integer
          format: int32
          minimum: 100
          maximum: 599
          nullable: true
          example: 503
        detail:
          description: |
            A human readable explanation specific to this occurrence
            of the problem.
          type: string
          nullable: true
          example: Connection to database timed out
        instance:
          description: |
            An absolute URI that identifies the specific occurrence of the problem.
          type: string
          format: uri
          nullable: true

    Product:
      type: object
      additionalProperties: false
      required:
        - id
        - code
        - owner
        - flexibility
      properties:
        id:
          description: |
            A unique identifier for the product.
          type: string
          nullable: false
        summary:
          description: |
            A short human-readable product description (e.g., "Anytime Day Single").
          type: string
          nullable: true
        type:
          $ref: '#/components/schemas/ProductType'
        code:
          description: |
            The product code expressed in the provider system (e.g., "SDS").
          type: string
          nullable: false
          example: PT00AD
        description:
          description: |
            Textual description of the product.
          type: string
          nullable: true
        owner:
          $ref: '#/components/schemas/CompanyRef'
        conditions:
          description: |
            The sales or after-sales conditions to help users understand the conditions of sale before purchase, for example, route permissions, cancelations, and ticket validity.
          type: array
          items:
            $ref: '#/components/schemas/Condition'
        flexibility:
          $ref: '#/components/schemas/Flexibility'
        serviceClass:
          $ref: '#/components/schemas/ServiceClass'
        travelClass:
          $ref: '#/components/schemas/TravelClass'
        fulfillmentOptions:
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentOption'
        isTrainBound:
          description: |
            Whether the product is restricted to a specific train or not.  If `true`, the product is tied to a particular train, 
            so the passenger must travel on that train.  This value often applies to products like seat reservations.  
            If `false`, the product is not tied to a particular train, and the passenger can travel on any train within the permitted time frame or routes.
          type: boolean
          nullable: true
          default: false
        isReturnProduct:
          description: |
            Indicates if the product is for a return journey.
          type: boolean
          nullable: true
          default: false
        serviceConstraintText:
          type: string
          nullable: true
        carrierConstraintText:
          type: string
          nullable: true
        descriptiveTexts:
          type: array
          items:
            $ref: '#/components/schemas/TextElement'
        tariff:
          description: |
            Mapping to Tariff 42 of IRS-90918-3 (Hermes/Hosa). Helps the H2O converter in mapping
            offers to a requested tariff. See legacyReservationParameter.tariff in OSDM offline.
          type: string
          nullable: true
        combinationTags:
          type: array
          items:
            $ref: '#/components/schemas/CombinationTag'
        productTags:
          description: product tags associated with the product that can be used to search for offers. This can be omitted outside of the product master data service
          type: array
          items:
            type: string
            nullable: true
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    ProductTag:
      description: |
        Known values:
        - SPLIT_RESERVATION
          Indicate for the provider that the distributor is prepared to get reservations including a split inside the reservation
          Without this indication the provider is not allowed to use the SplitSection Object inside a reservation. 
          The implementation of SPLIT_RESERVATION is not recommended. To provide reservations that do not cover a full leg 
          multiple reservations as offerParts should be created.
      type: string
      x-extensible-enum:
        - 'SPLIT_RESERVATION'

    ProductType:
      description: |
        General product types harmonized across fare providers. (More to be added, use null for types not listed yet.)
      type: string
      x-extensible-enum:
        - 'ADMISSION_PASS'
        - 'ADMISSION_MULTI_RIDE'
        - 'ADMISSION_POINT2POINT'
        - 'UPGRADE_PASS'
        - 'UPGRADE_MULTI_RIDE'
        - 'UPGRADE_POINT2POINT'
        - 'RESERVATION'
        - 'ANCILLARY_SERVICE'
        - 'ANCILLARY_ITEM'
        - 'REDUCTION_CARD'

    ProductCategory:
      type: object
      additionalProperties: false
      description: |
        Product category based on NeTEx/SIRI. A product category is a classification for vehicle journeys
        to express some common properties of journeys for marketing and fare products. Provided by OJP.
      required:
        - name
        - shortName
        - productCategoryRef
      properties:
        name:
          description: |
            Full name of this product category, e.g. 'TGV Lyria' in Switzerland and France.
            UIC: Long name of Service Brand
          type: string
          nullable: false
          example: TGV Lyria
        shortName:
          description: |
            Short name or acronym of the product category, likely to be published, e.g. 'TGV'
            UIC: Abbreviation of Service Brand
          type: string
          nullable: false
          example: TGV
        productCategoryRef:
          $ref: '#/components/schemas/ProductCategoryRef'

    ProductCategoryRef:
      type: string
      description: |
        Reference to a product category. Product categories should be defined once and used
        uniformly in all channels (e.g. NeTEx, SIRI, OJP). Provided by OJP.
      nullable: true
      example: urn:uic:sbc:76

    ProductCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        products:
          description: |
            The list of all products available to the caller.
          type: array
          items:
            $ref: '#/components/schemas/Product'

    ProductLegAssociation:
      type: object
      additionalProperties: false
      required:
        - productId
      properties:
        productId:
          description: |
            A unique product Id.
          type: string
          nullable: false
        legIds:
          description: |
            The Ids of the journey legs covered by the product.
          type: array
          items:
            type: string
          nullable: true

    ProductResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        product:
          $ref: '#/components/schemas/Product'

    ProductSummary:
      type: object
      additionalProperties: false
      description: |
        Summary of the booking's underlining products.
      required:
        - id
        - description
        - code
      properties:
        id:
          type: string
          nullable: false
        description:
          type: string
          nullable: false
        code:
          type: string
          nullable: false

    PromotionCode:
      type: object
      additionalProperties: false
      description: |
        The promotion code is a token that reduces the price at POST booking time OR
        Token that reduces the price at POST trip-offer-collection time OR
        Token to receive offers that are not available without it.
      required:
        - code
      properties:
        code:
          description: |
            The code issued by the issuer
          type: string
          nullable: false
          example: SPRING_DISCOUNT
        issuer:
          description: |
            The party that issues the promotion code, in case of a railway it's its RICS code
          type: string
          nullable: true

    Purchaser:
      type: object
      additionalProperties: false
      description: |
        purchaser information
      properties:
        externalRef:
          description: |
            A stable reference to a purchaser from other elements, or from caller system. When received in input of a request, it must be echoed back in the response.
          type: string
          nullable: true
        detail:
          $ref: '#/components/schemas/PersonDetail'
          nullable: true
        companyDetails:
          $ref: '#/components/schemas/CompanyDetail'
          nullable: true
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    PurchaserResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        purchaser:
          $ref: '#/components/schemas/Purchaser'

    PurchaserSearchRequest:
      type: object
      additionalProperties: false
      properties:
        companyName:
          type: string
          nullable: true
        companyRef:
          $ref: '#/components/schemas/CompanyRef'
        firstName:
          type: string
          nullable: true
        lastName:
          type: string
          nullable: true
        dateOfBirth:
          type: string
          format: date
          nullable: true
        phoneNumber:
          type: string
          nullable: true
        email:
          type: string
          nullable: true

    PurchaserSpecification:
      type: object
      additionalProperties: false
      description: |
        Minimal specification of a purchaser to request booking of an offer.
      required:
        - detail
      properties:
        externalRef:
          description: |
            A stable reference to a purchaser from other elements, or from caller system. When received in input of a request, it must be echoed back in the response.
          type: string
          nullable: true
        detail:
          $ref: '#/components/schemas/PersonDetail'
        companyDetails:
          $ref: '#/components/schemas/CompanyDetail'

    Quantity:
      type: object
      additionalProperties: false
      description: |
        Generic entity to model a quantity.
      required:
        - value
        - unit
      properties:
        value:
          type: number
          format: float
          nullable: false
        unit:
          type: string
          nullable: false
          example: kg

    ReductionCardAccount:
      allOf:
        - $ref: '#/components/schemas/AbstractTravelAccount'
        - type: object
          additionalProperties: false
          required:
            - type
          properties:
            type:
              $ref: '#/components/schemas/ReductionCardType'

    ReductionCardCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        reductionCardTypes:
          type: array
          items:
            $ref: '#/components/schemas/ReductionCardType'

    ReductionCardType:
      type: object
      additionalProperties: false
      required:
        - code
        - issuer
        - name
      properties:
        code:
          type: string
          nullable: false
        issuer:
          $ref: '#/components/schemas/CompanyRef'
        name:
          $ref: '#/components/schemas/Text'
        cardIdRequired:
          description: |
            the card id must be delivered in online sales
          type: boolean
          nullable: true
          default: false
        includedCardTypes:
          description: |
            This card includes the listed cards
          type: array
          items:
            $ref: '#/components/schemas/CardTypeReference'
        serviceClassTypes:
          description: |
            list of service classes where this card applies
          type: array
          items:
            $ref: '#/components/schemas/ServiceClassType'
          maxItems: 4
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    RefundOffer:
      type: object
      additionalProperties: false
      required:
        - id
        - createdOn
        - validFrom
        - validUntil
        - status
        - fulfillments
        - refundFee
        - refundableAmount
      properties:
        id:
          description: |
            id of the refund offer
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the refund offer.
          type: string
          nullable: true
          example: Refund Offer for Paris-Barcelona Andre Dupont 2022-07-23
        createdOn:
          type: string
          format: date-time
          nullable: false
        validFrom:
          type: string
          format: date-time
          nullable: false
        validUntil:
          description: |
            time until the offer can be used
          type: string
          format: date-time
          nullable: false
        confirmedOn:
          type: string
          format: date-time
          nullable: true
        status:
          $ref: '#/components/schemas/RefundStatus'
        reimbursementStatus:
          $ref: '#/components/schemas/ReimbursementStatus'
        reimbursementDateTime:
          type: string
          format: date-time
          nullable: true
        appliedOverruleCode:
          $ref: '#/components/schemas/OverruleCode'
        fulfillments:
          description: fulfillments to be refunded or replaced
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
          minItems: 1
        issuedFulfillments:
          description:
            new fulfillments issued with this offer (only available after
            confirmation)
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
        issuedVouchers:
          type: array
          items:
            $ref: '#/components/schemas/VoucherInformation'
        refundFee:
          $ref: '#/components/schemas/Price'
          description: |
            Amount kept by the carrier and/or distributor
        refundableAmount:
          $ref: '#/components/schemas/Price'
          description: |
            Amount refunded to the purchaser
        refundOfferBreakdown:
          $ref: '#/components/schemas/RefundOfferItem'
        reimbursementMethod:
          $ref: '#/components/schemas/ReimbursementMethod'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    RefundOfferItem:
      type: object
      required:
        - refundFee
        - refundableAmount
        - bookingParts
      properties:
        refundFee:
          description: Amount kept by the carrier and/or the distributor
          $ref: '#/components/schemas/Price'
        refundableAmount:
          description: Amount to be returned to the purchaser
          $ref: '#/components/schemas/Price'
        bookingParts:
          type: array
          items:
            $ref: '#/components/schemas/BookingPartReference'

    RefundOfferCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        refundOffers:
          type: array
          items:
            $ref: '#/components/schemas/RefundOffer'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    RefundOfferPatchRequest:
      type: object
      additionalProperties: false
      description: |
        Changes the refund offer to status CONFIRMED. De-confirmation by setting PROPOSED status is not supported by API.
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/RefundStatus'

    RefundOfferRequest:
      type: object
      additionalProperties: false
      description: |
        Request for a refund offer.
        Fulfillments can be provided in case the booking contains multiple individual fulfillments.
      required:
        - fulfillmentIds
      properties:
        fulfillmentIds:
          description: |
            ids of the fulfillments to refund
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        refundSpecifications:
          description: |
            To do a partial refund repeat the fulfillmentIds affected and point out what to refund
          type: array
          items:
            $ref: '#/components/schemas/RefundSpecification'
        overruleCode:
          $ref: '#/components/schemas/OverruleCode'
        refundDate:
          description: |
            Indicates for passes the date taken as reference to compute possible partial refund. It is also the date taken
            as reference to invalidate the pass partially refunded.
          type: string
          format: date-time
          nullable: true

    RefundOfferResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        refundOffer:
          $ref: '#/components/schemas/RefundOffer'

    RefundSpecification:
      type: object
      required:
        - fulfillmentId
      properties:
        fulfillmentId:
          description: |
            id of the fulfillment where some part(s) should be removed
          type: string
        bookingPartIds:
          description: ids of booking parts to be removed from the fulfillment
          type: array
          items:
            type: string
        passengerIds:
          type: array
          description: ids of passengers to be removed from the fulfillment
          items:
            type: string

    RefundStatus:
      type: string
      enum:
        - 'PROPOSED'
        - 'CONFIRMED'

    RefundType:
      description: Whether the offer is refundable and under what conditions.
      type: string
      enum:
        - "YES" # SilverRail change
        - "NO" # SilverRail change
        - 'WITH_CONDITION'

    RegionalConstraint:
      type: object
      additionalProperties: false
      description: |
        Regional constraint of the fare- offline bulk data use the id of connection points whereas  in an online environment the connection point is included
      required:
        - regionalValidities
      properties:
        entryConnectionPoint:
          $ref: '#/components/schemas/FareConnectionPoint'
        exitConnectionPoint:
          $ref: '#/components/schemas/FareConnectionPoint'
        regionalValidities:
          type: array
          items:
            $ref: '#/components/schemas/RegionalValidity'
          minItems: 1
        distance:
          description: |
            Distance in km for statistics
          type: integer
          format: int32
          minimum: 0
          nullable: true

    RegionalValidity:
      type: object
      additionalProperties: false
      description: |
        One of the elements Zone, ViaStation, TrainLink, Line, or Polygon defining the regional validity
      properties:
        seqNb:
          description: |
            order number of the list item
          type: integer
          format: int32
          nullable: true
        zone:
          $ref: '#/components/schemas/Zone'
        route:
          $ref: '#/components/schemas/Route'
        trainLink:
          $ref: '#/components/schemas/TrainLink'
        line:
          $ref: '#/components/schemas/Line'
        polygon:
          $ref: '#/components/schemas/Polygon'
        serviceConstraint:
          $ref: '#/components/schemas/ServiceConstraint'

    RegionalValiditySummary:
      description: |
        A summary of any geographical or temporal constraints that apply to the offer.
      type: object
      additionalProperties: false
      required:
        - description
        - shortDescription
      properties:
        description:
          description: |
            A description of the geographical or temporal constraint.
          type: string
          minLength: 1
          nullable: false
        shortDescription:
          description: |
            A shortened version of the description typically used for quick reference or where space is restricted, for example, on a mobile phone.  
            Limited to 64 characters.
          type: string
          minLength: 1
          nullable: true

    RegulatoryCondition:
      description: |
        General conditions applied to cover legal regulations within the area of validity. Allocators must
        reflect these terms and conditions in the conditions of combined offers and indicate them to the customer
        where required. Which indications are mandatory to be shown to the customer is defined in the SCICs.

        Valid Values:
        - CIV: terms and conditions according to COTIV regulation
        - MD: terms and conditions according to SMPS regulation
        - EU_PRR: terms and conditions according to EU-PRR regulation
      type: string
      enum:
        - 'CIV'
        - 'MD'
        - 'EU_PRR'

    Reimbursement:
      type: object
      additionalProperties: false
      description: |
        Customers who have bought a ticket which allows reimbursement and which have not traveled or traveled partially only can claim to be reimbursed. The customer must prove that he has not or only partially used the ticket. A partial use might be a only a part of the trip was traveled or only some of the travelers were traveling or a combination of both.
      required:
        - id
        - request
        - status
      properties:
        id:
          type: string
          nullable: false
        request:
          $ref: '#/components/schemas/ReimbursementRequest'
        status:
          $ref: '#/components/schemas/BackOfficeStatus'
        missingInformation:
          type: array
          items:
            type: string
          nullable: true
        decision:
          $ref: '#/components/schemas/ReimbursementDecision'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    ReimbursementDecision:
      type: object
      additionalProperties: false
      description: |
        Decision whether or not the reimbursement is granted. Contains information on the amount reimbursed.
      properties:
        amount:
          $ref: '#/components/schemas/Price'
        voucher:
          $ref: '#/components/schemas/FulfillmentDocument'
        explanation:
          $ref: '#/components/schemas/SupportingDocument'
        shortExplanation:
          type: string
          nullable: true

    ReimbursementMethod:
      description: |
        reimbursement methods to inform on restrictions of the payment method
      type: object
      properties:
        paymentMethod:
          $ref: '#/components/schemas/PaymentMethodEnum'

    PaymentMethodEnum:
      description: |
        payment method to be used for the reimbursement
      type: string
      x-extensible-enum:
        - 'VOUCHER'
        - 'ORIGINAL_ACCOUNT'
        - 'BRAIN_TREE'

    ReimbursementPatchRequest:
      type: object
      additionalProperties: false
      required:
        - additionalDocuments
        - updatedStatus
      properties:
        additionalDocuments:
          type: array
          items:
            $ref: '#/components/schemas/SupportingDocument'
          minItems: 1
        updatedStatus:
          $ref: '#/components/schemas/BackOfficeStatus'

    ReimbursementReason:
      description: |
        Reason why the booking should be reimbursed.
      type: string
      x-extensible-enum:
        - 'STRIKE'
        - 'TICKET_NOT_USED'

    ReimbursementRequest:
      type: object
      additionalProperties: false
      description: |
        The reimbursement process is used to support refunds which are not covered by the online refund/exchange processes, e.g.: Partial refund of a trip (Trip was Hamburg - Munich, but only Hamburg - Kassel was used), Refund outside of rules due to good will. This is a manual back office process within the provider's organization which is triggered by this request. State changes can be signaled by web hook events.
      required:
        - reimbursementReason
        - reimbursedPassengerIds
        - fulfillmentIds
      properties:
        reimbursementReason:
          $ref: '#/components/schemas/ReimbursementReason'
        reimbursementDate:
          type: string
          format: date-time
          nullable: true
        reimbursedPassengerIds:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        tripSections:
          type: array
          items:
            $ref: '#/components/schemas/TripSection'
        fulfillmentIds:
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        supportingDocuments:
          type: array
          items:
            $ref: '#/components/schemas/SupportingDocument'

    ReimbursementResponse:
      type: object
      additionalProperties: false
      description: |
        Response to a reimbursement request, containing a state and a decision.
      required:
        - reimbursement
      properties:
        reimbursement:
          $ref: '#/components/schemas/Reimbursement'
        warnings:
          $ref: '#/components/schemas/WarningCollection'

    ReimbursementStatus:
      type: string
      enum:
        - 'IMMEDIATE'
        - 'DELAYED'
      default: 'IMMEDIATE'

    ReleaseOffer:
      type: object
      additionalProperties: false
      required:
        - id
        - createdOn
        - validFrom
        - validUntil
        - status
        - fulfillments
      properties:
        id:
          description: |
            id of the refund offer
          type: string
          nullable: false
        summary:
          description: |
            A human-readable description of the refund offer.
          type: string
          nullable: true
        createdOn:
          type: string
          format: date-time
          nullable: false
        validFrom:
          type: string
          format: date-time
          nullable: false
        validUntil:
          description: |
            time until the offer can be used
          type: string
          format: date-time
          nullable: false
        confirmedOn:
          type: string
          format: date-time
          nullable: true
        status:
          $ref: '#/components/schemas/ReleaseStatus'
        fulfillments:
          type: array
          items:
            $ref: '#/components/schemas/Fulfillment'
          minItems: 1
        appliedOverruleCode:
          $ref: '#/components/schemas/OverruleCode'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    ReleaseOfferCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        releaseOffers:
          type: array
          items:
            $ref: '#/components/schemas/ReleaseOffer'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    ReleaseOfferPatchRequest:
      type: object
      additionalProperties: false
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/ReleaseStatus'

    ReleaseOfferRequest:
      type: object
      additionalProperties: false
      properties:
        fulfillmentIds:
          type: array
          items:
            $ref: '#/components/schemas/FulfillmentId'
        overruleCode:
          $ref: '#/components/schemas/OverruleCode'

    ReleaseOfferResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        releaseOffer:
          $ref: '#/components/schemas/ReleaseOffer'

    ReleaseStatus:
      type: string
      enum:
        - 'PROPOSED'
        - 'CONFIRMED'

    RemarkType:
      description: |
        Remark concerning the place (e.g. to be used in a tool tip).
        Code list according to UIC90918-1
        Known Values:
        - MIGHT_HAVE_TABLE: place might have a table
        - MIGHT_NOT_HAVE_TABLE: table at the place might be missing
        - MIGHT_HAVE_DIFFERENT_DIRECTION: place might have a different direction
        - MIGHT_HAVE_TABLE_AND_DIFFERENT_DIRECTION: place might have a table and a different direction
        - MIGHT_NOT_HAVE_TABLE_AND_MIGHT_HAVE_DIFFERENT_DIRECTION: table at the place might be missing and place might have a different direction
        - MIGHT_BE_IN_COMPARTMENT: place might be in compartment
        - MIGHT_BE_IN_OPEN_SPACE: place might be in open space
      type: string
      x-extensible-enum:
        - 'MIGHT_HAVE_TABLE'
        - 'MIGHT_NOT_HAVE_TABLE'
        - 'MIGHT_HAVE_DIFFERENT_DIRECTION'
        - 'MIGHT_HAVE_TABLE_AND_DIFFERENT_DIRECTION'
        - 'MIGHT_NOT_HAVE_TABLE_AND_MIGHT_HAVE_DIFFERENT_DIRECTION'
        - 'MIGHT_BE_IN_COMPARTMENT'
        - 'MIGHT_BE_IN_OPEN_SPACE'

    RequestedInformation:
      type: string
      description: |
        Boolean-expression indicating the data that must be set in the data model in order to proceed to next step of the process. 

        The syntax and semantics of the expression is described in detail in the OSDM documentation.
      nullable: true

    Reservation:
      description: |
        Reservation includes the reference to the in the initial offer.
        The offer needs to be updated with the selected places before the confirmation
        which creates the reservation details including the reserved places.
      allOf:
        - $ref: '#/components/schemas/AbstractBookingPart'
        - type: object
          additionalProperties: false
          properties:
            placeSelection:
              $ref: '#/components/schemas/PlaceSelection'
            placeAllocation:
              $ref: '#/components/schemas/PlaceAllocation'
            feeRefs:
              type: array
              items:
                $ref: '#/components/schemas/BookingPartReference'
            ancillaryRefs:
              type: array
              items:
                $ref: '#/components/schemas/BookingPartReference'

    ReservationGroup:
      type: object
      additionalProperties: false
      required:
        - id
        - name
        - reservationRefs
      properties:
        id:
          type: string
          nullable: false
        name:
          type: string
          nullable: false
        reservationRefs:
          type: array
          items:
            $ref: '#/components/schemas/OfferPartReference'
          minItems: 1

    ReservationOfferPart:
      description: |
        Reservation includes the reference to the in the initial offer.
        The offer needs to be updated with the selected places before the confirmation
        which creates the reservation details including the reserved places.
      allOf:
        - $ref: '#/components/schemas/AbstractOfferPart'
        - type: object
          additionalProperties: false
          properties:
            ancillaries:
              type: array
              items:
                $ref: '#/components/schemas/AncillaryRelation'
            availablePlaces:
              type: array
              items:
                $ref: '#/components/schemas/AvailablePlace'
            availablePlacePreferences:
              type: array
              items:
                $ref: '#/components/schemas/AvailablePlacePreferences'
            feeRefs:
              type: array
              items:
                $ref: '#/components/schemas/OfferPartReference'
            numberOfPrivateCompartments:
              type: integer
              format: int32
              nullable: true

    ReservationRelation:
      type: object
      additionalProperties: false
      required:
        - minGroupItemsToBeBooked
        - reservationGroup
      properties:
        minGroupItemsToBeBooked:
          type: integer
          format: int32
          nullable: false
        maxGroupItemsToBeBooked:
          type: integer
          format: int32
          nullable: true
        reservationGroup:
          $ref: '#/components/schemas/ReservationGroup'

    ReservationSelection:
      type: object
      additionalProperties: false
      required:
        - reservationId
      properties:
        reservationId:
          type: string
          nullable: false

    ReservedPlace:
      type: object
      additionalProperties: false
      description: |
        In distributor mode placeProperties are mandatory to be returned.
      required:
        - id
        - passengerIds
        - vehicleNumber
        - coachNumber
      properties:
        id:
          type: string
          nullable: false
        passengerIds:
          description: |
            Id of the passenger
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        vehicleNumber:
          description: |
            vehicle number (e.g. train number)
          type: string
          nullable: false
        coachNumber:
          type: string
          nullable: false
        placeDescription:
          description: |
            description of the places (e.g. 11-35,51)
          type: string
          nullable: true
        placeNumbers:
          description: |
            list of individual place numbers
          type: array
          items:
            type: string
          nullable: true
        placeProperties:
          description: |
            place properties to be indicated to the customer
          type: array
          items:
            $ref: '#/components/schemas/PlaceProperty'
        splitSection:
          description: |
            Indication of the covered part of a leg in case the reserved 
            place does not cover the whole leg. This element is allowed 
            on in case the offer was requested using the SPLIT_RESERVATION product tag
            an implementation is not recommended.
          $ref: '#/components/schemas/Section'

    Resource:
      type: object
      additionalProperties: false
      required:
        - title
        - href
      properties:
        title:
          type: string
          nullable: false
        href:
          type: string
          nullable: false

    ResourceType:
      type: string
      enum:
        - 'FARE'
        - 'RESERVATION'

    ReturnConstraint:
      type: object
      additionalProperties: false
      description: |
        a return trip with the same carrier must be sold in combination
      required:
        - latestReturn
        - earliestReturn
      properties:
        latestReturn:
          description: |
            number of days after departure or start of validity of the last return
          type: integer
          format: int32
          nullable: false
        earliestReturn:
          description: |
            number of days after departure or start of validity of the earliest return
          type: integer
          format: int32
          nullable: false
        excludedWeekdays:
          description: |
            weekdays (ISO day of the week, 1 = Monday) between travel and return where travel is not allowed
          type: array
          items:
            type: integer
            format: int32
          nullable: true

    ReturnSearchParameters:
      type: object
      additionalProperties: false
      description: |
        This property can be used in case a return trip is being requested.
        This will allow to benefit from return-specific fares or product

      properties:
        inwardReturnDate:
          description: |
            Needs to be provided when creating the collection of outward travels so that the
            provider knows when a return travel can be expected and propose appropriate
            product and fares.
          type: string
          pattern: '(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d)'
          nullable: true
        outwardOfferIds:
          description: |
            Needs to be provided when creating the collection of inward travels so that the provider
            can relate the inward travels to the outward travels context.
          type: array
          items:
            type: string
          nullable: true
        outwardOfferTag:
          description: |
            Needs to be provided when creating the collection of inward travels so that the provider
            knows what the outward offer is and and propose compatible offers only if desired or mandatory.
          type: string
          nullable: true

    Route:
      type: object
      additionalProperties: false
      description: |
        Route description including the first and last real stopPoint
      properties:
        routeItem:
          $ref: '#/components/schemas/RouteItem'
        routeItemList:
          description: |
            list of route items referenced within the list
          type: array
          items:
            $ref: '#/components/schemas/RouteItem'

    RouteItem:
      type: object
      additionalProperties: false
      description: |
        Items to compose routes (Note - replaced viaStation, content compliant with viaStation in IRS 90918-4 and 90918-9)
      properties:
        alternativeRouteItemIndices:
          description: |
            list of alternative route parts to be used on this travel path referenced by the index in the provided list of route items
          type: array
          items:
            type: integer
            format: int32
          nullable: true
        carrierConstraint:
          $ref: '#/components/schemas/CarrierConstraint'
        routeItemIndices:
          description: |
            sequence of route items along the travel path referenced by the index in the provided list of route items
          type: array
          items:
            type: integer
            format: int32
          nullable: true
        serviceConstraint:
          $ref: '#/components/schemas/ServiceConstraint'
        station:
          $ref: '#/components/schemas/StopPlace'
        fareReferenceStation:
          $ref: '#/components/schemas/FareReferenceStation'

    Section:
      type: object
      additionalProperties: false
      description: |
        Allows to indicate the sub-part of the trip. LegIds are only relevant if
        a trip can be referenced. When absent, the totality of the trip is priced.
      properties:
        startPlace:
          $ref: '#/components/schemas/PlaceRef'
        startLegId:
          type: string
          nullable: true
        endPlace:
          $ref: '#/components/schemas/PlaceRef'
        endLegId:
          type: string
          nullable: true
        externalTripRef:
          description: reference to a trip in case multiple trips are provided
          type: string
          nullable: true

    SecurityElementFormat:
      type: string
      enum:
        - 'PNG'
        - 'GIF'
        - 'SVG'
        - 'BINARY'
        - 'TEXT'

    SecurityElementType:
      type: string
      enum:
        - 'VISUAL_ELEMENT'
        - 'BAR_CODE'

    SecurityFeatureType:
      type: object
      additionalProperties: false
      description: |
        Defines type and format of the security feature.
      properties:
        type:
          $ref: '#/components/schemas/SecurityElementType'
        format:
          $ref: '#/components/schemas/SecurityElementFormat'
        symbology:
          $ref: '#/components/schemas/Symbology'

    SelectedAccommodation:
      type: object
      additionalProperties: false
      description: |
        Place selection of places for reservation linked to passengers.
      required:
        - passengerRefs
        - accommodationType
        - accommodationSubType
      properties:
        passengerRefs:
          description: |
            Id of the passenger
          type: array
          items:
            type: string
          minItems: 1
          nullable: false
        accommodationType:
          $ref: '#/components/schemas/AccommodationType'
        accommodationSubType:
          $ref: '#/components/schemas/AccommodationSubType'
        placeProperties:
          description: |
            Properties of places as defined in 90810-10.
            selection from the optional place properties provided in the offer
          type: array
          items:
            type: string
          nullable: true

    SelectedPlace:
      type: object
      additionalProperties: false
      description: |
        selected place in case of graphical booking
      required:
        - coachNumber
        - placeNumber
        - passengerRef
      properties:
        coachNumber:
          type: string
          nullable: false
        placeNumber:
          type: string
          nullable: false
        passengerRef:
          description: |
            Id of the passenger
          type: string
          nullable: false

    SelectedReferencePlace:
      type: object
      additionalProperties: false
      description: |
        reference place for an adjacent reservation
      required:
        - coachNumber
        - placeNumber
      properties:
        coachNumber:
          type: string
          nullable: false
        placeNumber:
          type: string
          nullable: false

    ServiceBrandCode:
      type: string
      description: |
        Service brand codes. For public transport, a code list can be found in OSDM's code list. E.g., '163' denotes TGV Lyria and '175' denotes Glacier Express.
      nullable: false
      example: 163, 175

    ServiceBrandFilter:
      type: object
      additionalProperties: false
      description: |
        Filter for in/exclusion of service brands. Provided by OJP.
      properties:
        exclude:
          description: |
            Whether service brand in list are to include or exclude from search.
          type: boolean
          nullable: true
          default: true
        serviceBrands:
          description: |
            Reference to service brand codes
          type: array
          items:
            $ref: '#/components/schemas/ServiceBrandCode'

    ServiceClass:
      type: object
      additionalProperties: false
      description: |
        Class of service.
      required:
        - type
        - name
      properties:
        type:
          $ref: '#/components/schemas/ServiceClassType'
        name:
          description: |
            The name the carrier itself uses to designate this specific service class.
          type: string
          nullable: false

    ServiceClassType:
      description: |
        Type of quality level, finer grained than the comfort class.
      type: string
      x-extensible-enum:
        - 'BEST'
        - 'HIGH'
        - 'STANDARD'
        - 'BASIC'
        - 'ANY_CLASS'

    ServiceConstraint:
      type: object
      additionalProperties: false
      description: |
        Either excluded or included service brands can be set.
      properties:
        restrictedToServiceBrands:
          type: array
          items:
            $ref: '#/components/schemas/ServiceBrandCode'
        excludedServiceBrands:
          type: array
          items:
            $ref: '#/components/schemas/ServiceBrandCode'
        description:
          $ref: '#/components/schemas/Text'

    ServiceDegradation:
      description: |
        enumeration of service degradations
      type: string
      x-extensible-enum:
        - 'RESERVED_PLACES_MISSING'
        - 'BOOKED_ACCOMMODATION_TYPE_MISSING'
        - 'BOOKED_CLASS_MISSING'
        - 'BOOKED_MEAL_MISSING'

    ServiceStatus:
      type: object
      additionalProperties: false
      description: |
        The current status of the service (e.g., ON_TIME).
      properties:
        unplanned:
          description: |
            Whether the service was not originally scheduled and therefore not planned.
          type: boolean
          nullable: true
          default: false
        cancelled:
          description: |
            Whether this trip is cancelled and will not be run.
          type: boolean
          nullable: true
          default: false
        deviation:
          description: |
            Whether the service deviates from the planned schedule, either physically (rerouted or skipping stops) or temporally (delayed or arriving early).
          type: boolean
          nullable: true
          default: false
        occupancy:
          description: |
            Passenger load status on vehicle. If omitted, not known.
            Equivalent to siri:OccupancyEnumeration which describes three values:
            'full', 'seatingAvailable' and 'standingAvailable'.
          type: string
          nullable: true

    ServiceTime:
      type: object
      additionalProperties: false
      description: |
        Details about the departure time for the service using OffsetDateTime in the ISO 8601 date/time format (e.g., 2023-12-03T10:15:30+01:00).  It is important that the offset from UTC is shown in the timetable. 
        Do not use the Coordinated Universal Time (UTC) format, i.e. 2023-12-03T10:15:30Z.
      required:
        - timetabledTime
      properties:
        timetabledTime:
          type: string
          description: |
            The scheduled departure time for the service in local date and time in the ISO 8601 date/time format.
          format: date-time
          nullable: false
          example: "2023-12-03T10:15:30+01:00" # SilverRail Change, openapi default example is in UTC format which the description says not to use.
        estimatedTime:
          type: string
          format: date-time
          nullable: true
          example: "2023-12-03T10:15:30+01:00" # SilverRail Change, openapi default example is in UTC format which the description says not to use.
        observedTime:
          type: string
          format: date-time
          nullable: true
          example: "2023-12-03T10:15:30+01:00" # SilverRail Change, openapi default example is in UTC format which the description says not to use.

    SiSType:
      type: string
      x-extensible-enum:
        - 'REGISTRY'
        - 'PEER_TO_PEER'

    SituationFullRef:
      type: string
      description: |
        A reference to a situational bulletin or disruption linked to the trip.  Bulletins can provide important information such as delays, cancellations, reroutes, strikes, or other operational issues affecting the journey.
      nullable: true

    SpecialCoachType:
      description: |
        indication of special coach
      type: string
      x-extensible-enum:
        - 'RESTAURANT_COACH'
        - 'BICYCLE_COACH'
        - 'LUGGAGE_COACH'
        - 'TRAIN_HEAD'

    StationSet:
      type: object
      additionalProperties: false
      required:
        - stations
      properties:
        stations:
          type: array
          items:
            $ref: '#/components/schemas/StopPlaceRef'
          minItems: 1

    StopBehavior:
      type: string
      enum:
        - 'ORIGIN_DESTINATION_ONLY'
        - 'REAL_BOARDING_ALIGHTING'

    StopCallStatus:
      type: object
      additionalProperties: false
      description: |
        Metadata about a stop, indicating its order in the journey, whether it’s a request stop, unplanned, or has boarding/alighting restrictions.
      properties:
        order:
          description: |
            The sequence number of the stop within the journey.  A value of `1` indicates the first stop in the journey.
          type: integer
          format: int32
          minimum: 0
          nullable: true
        requestStop:
          description: |
            Whether the stop is request-only. A value of `true` means the vehicle will stop here only if a passenger requests it in advance. 
            A value of `false` means the stop is scheduled as part of the regular route.
          type: boolean
          nullable: true
          default: false
        unplannedStop:
          description: |
            Whether the stop was added unexpectedly. A value of `true` means the stop was not part of the original schedule and was added due to factors like diversions or delays. 
            A value of `false` means the stop was planned.
          type: boolean
          nullable: true
          default: false
        notServicedStop:
          description: |
            Whether the stop will be skipped. A value of `true` means the vehicle will not stop here despite it being included in earlier planning. A value of `false` means the stop will be serviced as scheduled.
          type: boolean
          nullable: true
          default: false
        noBoardingAtStop:
          description: |
            Whether boarding is restricted at this stop. A value of `true` means passengers cannot board here, while `false` means boarding is allowed.
          type: boolean
          nullable: true
          default: false
        noAlightingAtStop:
          description: |
            Whether alighting is restricted at this stop. A value of `true` means passengers cannot exit here, while `false` means alighting is allowed.
          type: boolean
          nullable: true
          default: false

    StopPlace:
      description: |
        A place extended by accessibility limitation properties and some attributes of the associated equipment,
        comprising one or more places where vehicles may stop and where passengers may board or leave vehicles
        or prepare their trip, and which will usually have one or more wellknown names. Provided by OJP.
      allOf:
        - $ref: '#/components/schemas/Place'
        - type: object
          additionalProperties: false
          required:
            - name
          properties:
            ref:
              $ref: '#/components/schemas/StopPlaceRef'
            name:
              description: |
                Name of this stop place for use in passenger information.
              type: string
              nullable: false
              example: Zürich HB
            nameTranslations: # SilverRail change
              description: |
                Translations of the name. Only populated in bulk data transfer with '#/components/schemas/PlaceResponse',
                omitted elsewhere, e.g. in '#/components/schemas/TripResponse'.
              nullable: true
              type: array
              items:
                $ref: '#/components/schemas/Translation'
            modes: # SilverRail change
              description: |
                Modes that service this stop. Populated in bulk data transfer with '#/components/schemas/PlaceResponse'
                when the request does not ask for a specific language using
                '#/components/parameters/acceptLanguage'. Omitted otherwise, e.g. in '#/components/schemas/TripResponse'.
              nullable: true
              type: array
              items:
                $ref: '#/components/schemas/PTMode'

    StopPlaceRef:
      description: |
        A reference to the stop location (station, bus stop, etc).
      allOf:
        - $ref: '#/components/schemas/PlaceRef'
        - type: object
          additionalProperties: false
          required:
            - stopPlaceRef
          properties:
            stopPlaceRef:
              type: string
              pattern: '^urn:(uic|x_srt):stn:.+$' # SilverRail change
              x-pattern-message: 'Invalid StopPlaceRef, expected format urn:uic:stn:UICCODE or urn:x_srt:stn:PROPERIETARYCODE' #SilverRail change
              nullable: false
              example: urn:uic:stn:8503000

    SupportingDocument:
      type: object
      additionalProperties: false
      description: |
        The supporting document helps to supporting the claim.  Either downloadLink + downloadExpiry or content + format must be provided.
      required:
        - title
        - downloadExpiry
        - format
      properties:
        title:
          description: |
            title of the document
          type: string
          nullable: false
          example: delay confirmation written by staff on board
        downloadLink:
          type: string
          nullable: true
        downloadExpiry:
          type: string
          format: date-time
          nullable: false
        content:
          description: |
            base64 encoded binary of the actual document
          type: string
          format: byte
          nullable: true
        format:
          description: |
            Physical format of the document provided in the content field in Mime-Type format, e.g. 
            application/pdf, image/jpeg, etc. Must be filled if the 'content' field is present.
          type: string
          nullable: false
          example: application/pdf

    Symbology:
      description: |
        Symbology to use when rendering the binary data.
      type: string
      enum:
        - 'AZTEC'
        - 'Code128A'
        - 'Code128B'
        - 'Code128C'
        - 'QR'
        - 'TEXT'

    Text:
      type: object
      additionalProperties: false
      description: |
        Directly included text in case of online services. Text must be encoded in UTF-8 format.
      required:
        - id
        - text
      properties:
        id:
          type: string
          nullable: false
        translations:
          type: array
          items:
            $ref: '#/components/schemas/Translation'
        text:
          type: string
          nullable: false
        shortText:
          type: string
          nullable: true

    TextElement:
      type: object
      additionalProperties: false
      description: |
        Additional infortmation to display to the customer to clarify key product details.
      required:
        - description
        - shortDescription
        - textElementType
      properties:
        description:
          description: |
            A detailed explanation of the product or its associated feature (e.g., "Ticket is valid for a single day").
          type: string
          minLength: 1
          nullable: false
        shortDescription:
          description: |
            A shortened version of the description typically used for quick reference (e.g., "TicketType") or where space is restricted, for example, on a mobile phone.  Limited to 64 characters.
          type: string
          minLength: 1
          nullable: false
        # SilverRail change
        code:
          description: |
            An accompanying code.
          type: string
          nullable: true
        textElementType:
          $ref: '#/components/schemas/TextElementType'

    TextElementType:
      description: |
        The type of descriptive text.
        Valid Values:
        - INFORMATION: purely informational text (product/fulfillment).
        - MANDATORY: must be displayed to the customer prior to purchase (product).
        - MANDATORY_WITH_CONFIRMATION: must be confirmed, e.g., by checking a checkbox, prior to purchase (product).
        - TICKET_VALIDITY: any text on the ticket regarding its validity (other than information which can be retrieved from the booking).
      type: string
      enum:
        - 'INFORMATION'
        - 'MANDATORY'
        - 'MANDATORY_WITH_CONFIRMATION'
        - 'TICKET_VALIDITY'

    ThroughTicketTag:
      description: |
        Known Values:
        - RESPLUS: Through ticket tag according to Swedish Regulation.
      type: string
      x-extensible-enum:
        - 'RESPLUS'

    TimeUnit:
      type: string
      enum:
        - 'DAYS'
        - 'HOURS'
        - 'MINUTES'

    TimedLeg:
      type: object
      additionalProperties: false
      description: |
        A journey leg covered by a scheduled service that has fixed and specific departure and arrival times.
      required:
        - start
        - end
        - service
      properties:
        start:
          $ref: '#/components/schemas/Board'
        intermediates:
          type: array
          items:
            $ref: '#/components/schemas/Intermediate'
        end:
          $ref: '#/components/schemas/Alight'
        service:
          $ref: '#/components/schemas/DatedJourney'
        operatingDays:
          $ref: '#/components/schemas/OperatingDays'
        operatingDaysDescription:
          description: |
            Textual description of the operation days, e.g. 'Monday to Friday' or 'not on holidays'.
          type: string
          nullable: true
        duration:
          type: string
          description: |
            The duration of the timed leg in ISO 8601 format (e.g., “PT1H35M”), where "PT" stands for "period of time," "1H" for 1 hour, and "35M" for 35 minutes.
          format: duration
          nullable: true
        co2Emission:
          $ref: '#/components/schemas/Quantity'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/LegAttribute'

    TimedLegSpecification:
      type: object
      additionalProperties: false
      description: |
        A minimal timed leg specification.
      required:
        - start
        - end
        - service
      properties:
        start:
          $ref: '#/components/schemas/BoardSpecification'
        intermediates:
          type: array
          items:
            $ref: '#/components/schemas/IntermediateSpecification'
        end:
          $ref: '#/components/schemas/AlightSpecification'
        service:
          $ref: '#/components/schemas/DatedJourney'

    Trailer:
      type: object
      additionalProperties: false
      required:
        - weight
        - length
        - width
        - height
      properties:
        weight:
          description: |
            weight in kg
          type: integer
          format: int32
          minimum: 0
          nullable: false
        length:
          description: |
            length in cm
          type: integer
          format: int32
          minimum: 0
          nullable: false
        width:
          description: |
            width in cm
          type: integer
          format: int32
          minimum: 0
          nullable: false
        height:
          description: |
            height in cm
          type: integer
          format: int32
          minimum: 0
          nullable: false
        licensePlate:
          description: |
            The license plate is a personal data item and must not be provided in an offer request.
            It must be patched into the offer after the customer accepted the offer for booking
          type: string
          nullable: true

    TrainLink:
      type: object
      additionalProperties: false
      required:
        - fromStation
        - toStation
        - train
        - travelDate
        - serviceBrandCode
        - serviceBrandAbbreviation
      properties:
        fromStation:
          $ref: '#/components/schemas/StopPlaceRef'
        toStation:
          $ref: '#/components/schemas/StopPlaceRef'
        train:
          type: string
          nullable: false
        travelDate:
          description: |
            date and time of the departure where the train link starts
          type: string
          format: date-time
          nullable: false
        serviceBrandCode:
          $ref: '#/components/schemas/ServiceBrandCode'
        serviceBrandAbbreviation:
          description: |
            Abbreviation of the service brand, e.g. 'IC', 'TGV'. See the OSDM code list. Needs to match the service brand code.
          type: string
          nullable: false

    TrainValidity:
      type: object
      additionalProperties: false
      description: |
        Validity depending on boarding / leaving of a train. The ticket is valid
        from departure until the destination station in the train if departure or destination is within the from - until range.
        The validation can reference the departure or the destination time to decide the validity (e.g. Eurail/Interrail passes
        currently use the departure for the validity on night trains).
      required:
        - from
        - until
        - carriers
        - scope
      properties:
        from:
          type: string
          format: date-time
          nullable: false
        until:
          type: string
          format: date-time
          nullable: false
        carriers:
          type: array
          items:
            $ref: '#/components/schemas/CompanyRef'
          minItems: 1
        excludedServiceBrands:
          type: array
          items:
            $ref: '#/components/schemas/ServiceBrandCode'
        includedServiceBrands:
          type: array
          items:
            $ref: '#/components/schemas/ServiceBrandCode'
        scope:
          $ref: '#/components/schemas/TrainValidityScope'

    TrainValidityScope:
      type: string
      enum:
        - 'BOARDING'
        - 'ARRIVAL'

    TransferLeg:
      type: object
      additionalProperties: false
      description: |
        A non-travel portion of a trip where a passenger moves between services, typically at a transfer location (e.g., train station, bus stop). 
        A transfer leg represents the waiting or walking time between connecting legs in a multi-leg journey or the walking time from the point of 
        origin to the first transit stop.
      required:
        - start
        - end
      properties:
        continuousMode:
          $ref: '#/components/schemas/ContinuousMode'
        transferMode:
          $ref: '#/components/schemas/TransferMode'
        start:
          $ref: '#/components/schemas/PlaceRef'
        end:
          $ref: '#/components/schemas/PlaceRef'
        timeWindowStart:
          description: |
            Time at which window begins.
          type: string
          format: date-time
          nullable: true
        timeWindowEnd:
          description: |
            Time at which window ends.
          type: string
          format: date-time
          nullable: true
        duration:
          description: |
            The duration of the transfer leg in ISO 8601 format (e.g., “PT1H35M”), where "PT" stands for "period of time," "1H" for 1 hour, and "35M" for 35 minutes.
          type: string
          format: duration
          nullable: true
          example: PT20M
        situationFullRefs:
          type: array
          items:
            $ref: '#/components/schemas/SituationFullRef'
        co2Emission:
          $ref: '#/components/schemas/Quantity'

    TransferMode:
      description: |
        How a passenger moves between two legs of a journey during a transfer.
      type: string
      x-extensible-enum:
        - 'WALK'
        - 'PARK_AND_RIDE'
        - 'BIKE_AND_RIDE'
        - 'CAR_HIRE'
        - 'BIKE_HIRE'
        - 'PROTECTED_CONNECTION'
        - 'GUARANTEED_CONNECTION'
        - 'REMAIN_IN_VEHICLE'
        - 'CHANGE_WITHIN_VEHICLE'
        - 'CHECK_IN'
        - 'CHECK_OUT'

    Translation:
      type: object
      additionalProperties: false
      required:
        - language
        - text
      properties:
        language:
          description: |
            ISO-639-1 language code
          type: string
          nullable: false
        text:
          type: string
          nullable: false
        shortText:
          type: string
          nullable: true

    Transportable:
      type: object
      additionalProperties: false
      description: |
        Transportables which are handled similar to passengers like dogs, bicycles, car transport.
        These transportables might need a ticket or reservation.
      required:
        - type
      properties:
        type:
          $ref: '#/components/schemas/TransportableType'
        car:
          $ref: '#/components/schemas/Car'
        motorCycle:
          $ref: '#/components/schemas/MotorCycle'
        trailer:
          $ref: '#/components/schemas/Trailer'

    TransportableType:
      description: |
        Subset of the values from the passenger type code list IRS 90918-10
      type: string
      x-extensible-enum:
        - 'PET'
        - 'BICYCLE'
        - 'CAR'
        - 'MOTOR_CYCLE'
        - 'CAR_TRAILER'

    TravelAccountConsumption:
      type: object
      additionalProperties: false
      required:
        - consumedOn
        - bookingId
        - unit
        - bookingSummary
        - consumedAmount
      properties:
        consumedOn:
          type: string
          format: date-time
          nullable: false
        bookingId:
          type: string
          nullable: false
        unit:
          $ref: '#/components/schemas/AbstractTravelAccountUnit'
        bookingSummary:
          type: string
          nullable: false
        tripSummary:
          $ref: '#/components/schemas/TripSummary'
        consumedAmount:
          type: integer
          format: int32
          nullable: false

    TravelAccountResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        travelAccount:
          $ref: '#/components/schemas/AbstractTravelAccount'

    TravelAccountResponseContent:
      type: string
      enum:
        - 'ALL'
        - 'CONSUMPTIONS'
        - 'NONE'
      default: ALL

    TravelAccountType:
      type: string
      enum:
        - 'LOYALTY_CARD'
        - 'TRAVEL_PASS'
        - 'MULTI_RIDE'
        - 'REDUCTION_CARD'

    TravelClass:
      description: |
        Traditional first and second class.
      type: string
      x-extensible-enum:
        - 'FIRST'
        - 'SECOND'
        - 'ANY_CLASS'

    TravelDirectionType:
      description: |
        indication of the direction of travel standard direction is left
        to right with ascending coordinates in the layout values.
      type: string
      x-extensible-enum:
        - 'UNSPECIFIED'
        - 'IN_DIRECTION'
        - 'OPPOSITE_DIRECTION'
        - 'CHANGING'
        - 'STARING_IN_DIRECTION'

    TravelPassAccount:
      allOf:
        - $ref: '#/components/schemas/AbstractTravelAccount'
        - type: object
          additionalProperties: false
          required:
            - regionalConstraintSummary
          properties:
            regionalConstraintSummary:
              type: string
              nullable: false
            regionalConstraint:
              $ref: '#/components/schemas/RegionalConstraint'
            consumptions:
              type: array
              items:
                $ref: '#/components/schemas/TravelAccountConsumption'

    TravelValidity:
      type: object
      additionalProperties: false
      description: |
        In distributor mode, travel validity data is needed to create barcode and control data (IRS 90918-4) even in case they have been checked during the on-line sale.
      required:
        - validityRange
      properties:
        validTravelDates:
          $ref: '#/components/schemas/Calendar'
        validityRange:
          $ref: '#/components/schemas/ValidityRange'
        excludedTimeRanges:
          description: |
            time ranges excluded from the validity (e.g. off peak fulfillments)
          type: array
          items:
            $ref: '#/components/schemas/ExcludedTimeRange'
        numberOfTravelDays:
          description: |
            number of allowed travel days (e.g. 3 travel days within 2 weeks)
          type: integer
          format: int32
          nullable: true
        returnConstraint:
          $ref: '#/components/schemas/ReturnConstraint'
        trainValidity:
          $ref: '#/components/schemas/TrainValidity'
        validityType:
          $ref: '#/components/schemas/ValidityType'
        tripAllocationConstraint:
          $ref: '#/components/schemas/TripAllocationConstraint'
        tripInterruptionConstraint:
          $ref: '#/components/schemas/TripInterruptionConstraint'

    TravelValidityRange:
      type: object
      additionalProperties: false
      description: |
        Range for the start of validity to be used in a patch for available fulfillments (e.g. a 1 hour ticket not
        available during  peak hours in the morning and afternoon).
      required:
        - from
        - until
      properties:
        from:
          type: string
          format: date
          nullable: false
        until:
          type: string
          format: date
          nullable: false

    Trip:
      type: object
      description: |
        Each trip in the array represents a journey option referenced by an offer through its tripId.  
        A trip consists of an itinerary from an origin to a destination and includes departure/arrival times and legs.
      additionalProperties: false
      required:
        - id
        - duration
        - direction
        - startTime
        - endTime
        - transfers
        - legs
      properties:
        id:
          description: |
            A unique identifier for the trip.
          type: string
          nullable: false
        externalRef:
          description: |
            External reference of the trip for referencing purposes.
          type: string
          nullable: true
        summary:
          description: |
            A human-readable description of the trip.
          type: string
          nullable: true
        duration:
          description: |
            The total duration of the trip in ISO 8601 format (e.g., “PT1H35M”), where "PT" stands for "period of time," "1H" for 1 hour, and "35M" for 35 minutes.
          type: string
          format: duration
          nullable: false
          example: PT20M
        direction:
          $ref: '#/components/schemas/TripDirectionType'
        origin:
          $ref: '#/components/schemas/PlaceRef'
        destination:
          $ref: '#/components/schemas/PlaceRef'
        startTime:
          description: |
            An ISO 8601 formatted timestamp for the departure time at origin.
          type: string
          format: date-time
          nullable: false
        endTime:
          description: |
            An ISO 8601 formatted timestamp for the arrival time at destination.
          type: string
          format: date-time
          nullable: false
        transfers:
          description: |
            The number of transfers required in the journey.  A transfer is defined as a change from one transport service to 
            another within a single journey and occurs when a trip consists of multiple legs.
          type: integer
          format: int32
          minimum: 0
          nullable: false
        distance:
          description: |
            Distance in meters over the the complete trip, i.e., including transfer legs.
          type: integer
          format: int32
          minimum: 0
          nullable: true
        legs:
          description: |
            The individual segments that together form the trip.
          type: array
          items:
            $ref: '#/components/schemas/TripLeg'
          minItems: 1
        operatingDays:
          $ref: '#/components/schemas/OperatingDays'
        operatingDaysDescription:
          description: |
            Textual description of the operation days, e.g. 'Monday to Friday' or 'Not on holidays'.
          type: string
          nullable: true
        situationFullRefs:
          type: array
          items:
            $ref: '#/components/schemas/SituationFullRef'
        tripStatus:
          $ref: '#/components/schemas/TripStatus'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'
        # SilverRail change
        isOvertaken:
          description: Whether the trip has been overtaken by another transport method or change in the schedule.
          type: boolean

    TripAllocationConstraint:
      type: object
      additionalProperties: false
      description: |
        constraint on the use of a multiple trip ticket
      required:
        - allocationUnit
        - requiredProcesses
      properties:
        allocationUnit:
          $ref: '#/components/schemas/TripAllocationUnit'
        maxUnits:
          description: |
            maximum number of allowed units to be allocated
          type: integer
          format: int32
          nullable: true
        durationUnit:
          description: |
            multiples of this duration can be allocated
          type: string
          format: duration
          nullable: true
          example: PT30M
        requiredProcesses:
          description: |
            one of the listed processes is required for the allocation of a single trip
          type: array
          items:
            $ref: '#/components/schemas/TripAllocationProcess'
          minItems: 1

    TripAllocationProcess:
      description: |
        Known Values:
        - NONE: Individual trips don't need to be allocated.
        - MANUAL: The passenger has to allocate a trip manually (This should not be used as it is by design non-interoperable).
        - ACTIVATION: Individual trips can be allocated by activation of the ticket.
        - FULFILLMENT: A new fulfillment needs to be created to allocate a trip.
      type: string
      x-extensible-enum:
        - 'NONE'
        - 'MANUAL'
        - 'ACTIVATION'
        - 'FULFILLMENT'

    TripAllocationUnit:
      description: |
        Unit to allocate trips in a multi-trip fare. Trips can be allocated per day (e.g. Eurail) or per
        duration (e.g. multiples of a duration unit to cover the trip) or as single trips (e.g. carnet))
        Known Values:
        - NONE: Individual trips don't need to be allocated. 
        - TRIP: allocation per individual trip.
        - DAY: The allocation is per travel day.
        - DURATION: the allocation is per duration.
      type: string
      x-extensible-enum:
        - 'NONE'
        - 'TRIP'
        - 'DAY'
        - 'DURATION'

    TripCollectionResponse:
      type: object
      additionalProperties: false
      # SilverRail change
      description: |
        Trip response structure. Provided by OJP and SilverRail extensions added.
      required:
        - id
      properties:
        warning:
          $ref: '#/components/schemas/WarningCollection'
        id:
          type: string
          nullable: false
        trips:
          type: array
          items:
            $ref: '#/components/schemas/Trip'
        tripSummaries:
          type: array
          items:
            $ref: '#/components/schemas/TripSummary'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'
        # SilverRail change
        mapInfoParts:
          type: array
          items:
            $ref: '#/components/schemas/MapInfoPart'
        # SilverRail change
        tripContext:
          $ref: '#/components/schemas/TripContext'

    TripCoverage:
      type: object
      additionalProperties: false
      required:
        - coveredTripId
      properties:
        coveredTripId:
          description: |
            The Id of the trip covered by the product, ticket, or offer.
          type: string
          nullable: false
        sectionIndex:
          description: |
            Describes which part of the trip (in order) is covered by the OfferPart/BookingPart which contains this TripCoverage structure.

            This needs to be filled if either coveredLegIds or coveredSections is present.
          type: integer
          format: int32
          minimum: 1
          nullable: true
        coveredLegIds:
          description: |
            The Ids of the journey legs covered by the product, ticket, or offer.
          type: array
          items:
            type: string
          nullable: true
        coveredSections:
          type: array
          items:
            $ref: '#/components/schemas/Section'

    TripDataFilter:
      type: object
      additionalProperties: false
      # SilverRail change
      description: |
        Data to be included/excluded from search, f.e. modes, carriers. Provided by OJP and SilverRail extensions added.
      properties:
        ptModeFilter:
          $ref: '#/components/schemas/ModeFilter'
        carrierFilter:
          $ref: '#/components/schemas/CarrierFilter'
        serviceBrandFilter:
          $ref: '#/components/schemas/ServiceBrandFilter'
        vehicleFilter:
          $ref: '#/components/schemas/VehicleFilter'
        # SilverRail change
        mapFilter:
          $ref: '#/components/schemas/MapFilter'
        # SilverRail change
        tripContentFilter:
          $ref: '#/components/schemas/TripContentFilter'

    TripDirectionType:
      type: string
      description: |
        The direction of the trip (e.g., "OUT_BOUND").
      enum:
        - 'OUT_BOUND'
        - 'IN_BOUND'
      default: 'OUT_BOUND'

    TripInterruptionConstraint:
      type: object
      additionalProperties: false
      description: |
        rules on allowed trip interruptions. Interruptions due to a train change
        indicated by a trip search must not be included here.
      required:
        - maxInterruptions
        - requiredProcesses
      properties:
        maxInterruptions:
          description: |
            maximum number of allowed interruptions
          type: integer
          format: int32
          nullable: false
        maxDuration:
          description: |
            maximum duration of one interruption
          type: string
          format: duration
          nullable: true
          example: PT30M
        totalMaxDuration:
          description: |
            maximum duration of all interruptions on the route
          type: string
          format: duration
          nullable: true
          example: PT30M
        requiredProcesses:
          description: |
            one of the listed processes is required for the interruption of the trip
          type: array
          items:
            $ref: '#/components/schemas/TripInterruptionProcess'
          minItems: 1

    TripInterruptionProcess:
      description: |
        Known Values:
        - NONE
        - MANUAL: passenger needs manual confirmation of train or station staff to interrupt the trip.
        - ACTIVATION: Passenger needs to deactivate the ticket during the interruption (via an app).
      type: string
      x-extensible-enum:
        - 'NONE'
        - 'MANUAL'
        - 'ACTIVATION'

    TripLeg:
      type: object
      additionalProperties: false
      description: |
        A single stage of a trip that is made without change of mode or service.
      required:
        - id
      properties:
        id:
          description: |
            Id of this trip leg. Unique within trip result. A seqId is a leg id.
          type: string
          nullable: false
        externalRef:
          description: |
            External reference of this tripLeg for referencing purposes.
          type: string
          nullable: true
        timedLeg:
          $ref: '#/components/schemas/TimedLeg'
        transferLeg:
          $ref: '#/components/schemas/TransferLeg'
        continuousLeg:
          $ref: '#/components/schemas/ContinuousLeg'
        # SilverRail change
        mapInfoPartIds:
          type: array
          items:
            type: string

    TripLegCoverage:
      type: object
      additionalProperties: false
      required:
        - tripId
        - legId
      properties:
        tripId:
          type: string
          nullable: false
        legId:
          type: string
          nullable: false

    TripLegSpecification:
      type: object
      additionalProperties: false
      description: |
        A minimal trip leg specification.
      properties:
        externalRef:
          description: |
            External reference of this trip leg. Unique within trip result.
          type: string
          nullable: true
        timedLeg:
          $ref: '#/components/schemas/TimedLegSpecification'
        transferLeg:
          $ref: '#/components/schemas/TransferLeg'

    TripMobilityFilter:
      type: object
      additionalProperties: false
      description: |
        Base mobility options to be applied for both public and individual transport.
        Provided by OJP.
      properties:
        bikeTransport:
          description: |
            The user wants to carry a bike on public transport.
          type: boolean
          nullable: true
          default: false
        walkSpeed:
          description: |
            Deviation from average walk speed in percent. 100% percent is average speed.
          type: integer
          format: int32
          nullable: true
          default: 100
        # SilverRail change
        maxWalkDistanceForConnection:
          description: |
            Specifies the maximum distance the passenger wishes to walk to make a single connection. Very large values will be truncated. Default value: 2000 meters (1.25 miles)
          type: integer
          format: int32
          nullable: true
          default: 2000
        additionalTransferTime:
          description: |
            Additional time added to all transfers (also to transfers between individual to
            public transport).
          type: string
          format: duration
          nullable: true
          example: 0S
        # SilverRail change
        transferTimeMultiplier:
          description: |
            A multiplication as a percentage to apply to all transfers. 
            100% keeps standard transfer times
          type: integer
          format: int32
          nullable: true
          default: 100
        minimalTransferTime:
          type: string
          format: duration
          nullable: true

    TripParameters:
      type: object
      additionalProperties: false
      description: |
        Options to control the search behavior and response contents.
        number of results > either number of results or number of results before and after are used.
        Provided by OJP.
      properties:
        dataFilter:
          $ref: '#/components/schemas/TripDataFilter'
        policyFilter:
          $ref: '#/components/schemas/BaseTripPolicyFilter'
        mobilityFilter:
          $ref: '#/components/schemas/TripMobilityFilter'
        numberOfResults:
          type: integer
          format: int32
          nullable: true
          default: 5
        numberOfResultsBefore:
          type: integer
          format: int32
          nullable: true
          default: 0
        numberOfResultsAfter:
          type: integer
          format: int32
          nullable: true
          default: 5
        ignoreRealtimeData:
          type: boolean
          nullable: true
          default: false
        # SilverRail change
        # note: we can't use a $ref here as with the current version of openapitools ("7.0.1") the default value for the enum will not be generated if the schema uses a $ref
        # TODO: change this to use a $ref once the openapitools version is bumped
        realTimeMode:
          type: string
          nullable: true
          default: 'ANNOTATE'
          enum:
            - 'ANNOTATE'
            - 'FULL'
        transferLimit:
          description: |
            The maximum number of interchanges the user will accept per trip.
          type: integer
          format: int32
          minimum: 0
          nullable: true
        itModesToCover:
          $ref: '#/components/schemas/IndividualMode'

    TripResponse:
      type: object
      additionalProperties: false
      required:
        - trip
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        trip:
          $ref: '#/components/schemas/Trip'

    TripResponseContent:
      type: string
      enum:
        - 'ALL'
        - 'PLACES'
        - 'NONE'
      default: 'ALL'

    TripSearchCriteria:
      type: object
      additionalProperties: false
      description: |
        The trip details to use to filter offers.  At its most basic level, the information contained in this object defines the origin, destination,
        and times for a trip.  A one-way trip will contain a single `tripSearchCriteria` object.
      required:
        - origin
        - destination
      properties:
        departureTime:
          description: |
            The departure time for the desired trip in the local date and time in ISO 8601 format, for example, 2024-10-29T14:17:00.  
            Must be omitted if an arrival time is used.
          type: string
          pattern: '(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d)'
          nullable: true
        arrivalTime:
          description: |
            The arrival time for the desired trip in the local date and time in ISO 8601 format, for example, 2024-10-29T14:17:00.  
            Must be omitted if a departure time is used.
          type: string
          pattern: '(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d)'
          nullable: true
        origin:
          $ref: '#/components/schemas/PlaceRef'
        destination:
          $ref: '#/components/schemas/PlaceRef'
        vias:
          description: |
            Ordered series of points where the journey must pass through. If more
            than one via point is given all of them must be obeyed - in the correct
            order. The server is allowed to replace a via stop by equivalent stops.

            TripDataFilters given at a via apply to the section after that via. To apply a TripDataFilter for 
            the first section (between the origin and the first via), use the TripDataFilter within the
            TripParameters structure.
          type: array
          items:
            $ref: '#/components/schemas/TripVia'
        parameters:
          $ref: '#/components/schemas/TripParameters'
        embed:
          description: |
            Influences whether referenced resources are returned in full or as references only. Proposed default ALL
          type: array
          items:
            $ref: '#/components/schemas/TripsCollectionResponseContent'
        returnSearchParameters:
          $ref: '#/components/schemas/ReturnSearchParameters'
        notVias:
          type: array
          items:
            $ref: '#/components/schemas/NotVia'

    TripSection:
      type: object
      additionalProperties: false
      description: |
        Part of a trip.
      required:
        - tripId
      properties:
        tripId:
          type: string
          nullable: false
        coveredSection:
          $ref: '#/components/schemas/Section'

    TripSpecification:
      type: object
      additionalProperties: false
      description: |
        A minimal trip specification from origin to destination.
      required:
        - legs
      properties:
        externalRef:
          description: |
            External reference of this trip for referencing purposes. Unique within a trip specification.
          type: string
          nullable: true
        legs:
          type: array
          items:
            $ref: '#/components/schemas/TripLegSpecification'
          minItems: 1
        isPartOfInternationalTrip:
          type: boolean
          nullable: true
          default: false
        returnSearchParameters:
          $ref: '#/components/schemas/ReturnSearchParameters'

    TripStatus:
      type: object
      description: |
        Details about current operational status of a trip, providing important information about any irregularities or changes that might affect the journey.
      additionalProperties: false
      properties:
        unplanned:
          description: |
            Whether the trip is unexpected or unplanned.  If `true`, the trip was not part of the original itinerary.
          type: boolean
          nullable: true
          default: false
        cancelled:
          description: |
            Whether the trip was cancelled.  If `true`, the trip has been canceled.
          type: boolean
          nullable: true
          default: false
        deviation:
          description: |
            Whether the trip deviates from its original route or schedule, such as rerouting or unscheduled stops.
          type: boolean
          nullable: true
          default: false
        delayed:
          description: |
            Whether the trip is running behind its scheduled time. If `true`, there is a delay.
          type: boolean
          nullable: true
          default: false
        infeasible:
          description: |
            Whether this trip cannot be used, due to operational delays and impossible transfers.
          type: boolean
          nullable: true
          default: false

    TripSummary:
      type: object
      additionalProperties: false
      description: |
        Trip summary. Provided by OJP.
      required:
        - id
      properties:
        id:
          description: |
            Id of this trip for referencing purposes. Unique within trip response.
          type: string
          nullable: false
        summary:
          type: string
          nullable: true
        externalRef:
          type: string
          nullable: true
        startTime:
          description: |
            Departure time at origin.
          type: string
          format: date-time
          nullable: true
        endTime:
          description: |
            Arrival time at destination.
          type: string
          format: date-time
          nullable: true
        duration:
          description: |
            Overall duration of the trip.
          type: string
          format: duration
          nullable: true
        origin:
          $ref: '#/components/schemas/PlaceRef'
        destination:
          $ref: '#/components/schemas/PlaceRef'
        transfers:
          type: integer
          format: int32
          nullable: true
        distance:
          type: integer
          format: int32
          nullable: true

    TripTravelAccountUnit:
      allOf:
        - $ref: '#/components/schemas/AbstractTravelAccountUnit'
        - type: object
          additionalProperties: false

    TripVia:
      type: object
      additionalProperties: false
      description: |
        Via restrictions for a trip. Provided by OJP.

        A TripDataFilter given applies to the section after this via.
      required:
        - viaPlace
      properties:
        viaPlace:
          $ref: '#/components/schemas/PlaceRef'
        dwellTime:
          description: |
            desired dwell time at the via place
          type: string
          format: duration
          nullable: true
          example: 30M
        dataFilter:
          $ref: '#/components/schemas/TripDataFilter'

    TripsCollectionResponseContent:
      type: string
      enum:
        - 'ALL'
        - 'TRIPS'
        - 'TRIPSUMMARIES'
        - 'NONE'
      default: ALL

    VAT:
      type: object
      additionalProperties: false
      description: |
        Details of any Value Added Tax (VAT) applied to the price.  Each object describes a different VAT component, including attributes like rate, amount, and type, helping to clarify the tax portion of the price.
      required:
        - countryCode
        - amount
      properties:
        countryCode:
          $ref: '#/components/schemas/CountryCode'
        amount:
          description: |
            The total VAT amount included in the price.
          type: integer
          format: int32
          nullable: false
        scale:
          description: |
            The number of decimal places used to display the amount.
          type: integer
          format: int32
          nullable: true
          default: 2
        percentage:
          description: The VAT rate used in the calculation of the price.
          type: number
          format: float
          nullable: true
        taxId:
          description: |
            A unique identifier for the tax, typically assigned by a tax authority.
          type: string
          nullable: true
          example: DE-MWST-7
        scope:
          $ref: '#/components/schemas/VatScope'

    ValidityRange:
      type: object
      additionalProperties: false
      required:
        - timeUnit
        - value
      properties:
        timeUnit:
          $ref: '#/components/schemas/TimeUnit'
        value:
          type: integer
          format: int32
          nullable: false
        hoursAfterMidnight:
          description: |
            validity extended after midnight
          type: integer
          format: int32
          nullable: true

    ValidityType:
      description: |
        Type of usage, either as single trip or multiple trips. Unrestricted fares might be combined 
        into single trips if appropriate.
      type: string
      x-extensible-enum:
        - 'SINGLE_TRIP'
        - 'MULTIPLE_TRIPS'
        - 'UNRESTRICTED'

    VatScope:
      description: |
        scope where the VAT applies
      type: string
      x-extensible-enum:
        - 'INTERNATIONAL'
        - 'NATIONAL'
        - 'SHORT_DISTANCE'
        - 'LONG_DISTANCE'

    Vehicle:
      type: object
      additionalProperties: false
      description: |
        List of the coaches in a vehicle run ordered according to the physical
        ordering of the vehicle.
      required:
        - coaches
      properties:
        coaches:
          type: array
          items:
            $ref: '#/components/schemas/Coach'
          minItems: 1
        reservationRefs:
          description: |
            References to the reservations/fares for which all places in the vehicle can be selected. 
            The reservation ids must be part of the list of reservation ids for which the consumer has
            requested the available places and the consumer must accept a list by setting 
            singleSelectionMapsRequired to false
          type: array
          items:
            type: string
    VehicleFilter:
      type: object
      additionalProperties: false
      description: |
        Filter for in/exclusion of vehicle based on vehicle number or line number.
      properties:
        exclude:
          description: |
            Whether vehicle in list is to include or exclude from search.
          type: boolean
          nullable: true
          default: true
        vehicleNumbers:
          type: array
          items:
            $ref: '#/components/schemas/VehicleNumber'
        lineNumbers:
          type: array
          items:
            $ref: '#/components/schemas/LineNumber'

    VehicleNumber:
      type: string
      description: |
        The vehicle number(s) of the vehicle that runs this timed leg. On some legs a vehicle can
        have multiple vehicle numbers, e.g. if it is split into two trains at a intermediate stop.
        E.g. the vehicle numbers of 'TGV 9218', 'TH 9340' or 'ICE 15'.
        For backwards compatibility with Hermes/Hosa the length of the string must be restricted
        to 5 characters. If's longer, it is not possible to use the H2O converter.
      nullable: true
      example: 9218, 9340, 15

    VoucherInformation:
      type: object
      additionalProperties: false
      required:
        - issuer
        - code
      properties:
        issuer:
          $ref: '#/components/schemas/CompanyRef'
        code:
          description: |
            voucher code provided by the issuer
          type: string
          nullable: false

    Warning:
      type: object
      additionalProperties: false
      required:
        - code
      properties:
        code:
          type: string
          nullable: false
        type:
          description: |
            An absolute URI that identifies the warning type. When dereferenced,
            it SHOULD provide human-readable documentation for the problem type
            (e.g., using HTML).
          type: string
          format: uri
          nullable: true
          default: 'about:blank'
          example: https://example.com/warns/price-updated
        detail:
          description: |
            A human readable explanation specific to this occurrence of the
            warning.
          type: string
          nullable: true
          example:
            The price of the given offer part has been updated during the
            booking operation
        instance:
          description: |
            An absolute URI that identifies the specific occurrence of the warning.
          type: string
          format: uri
          nullable: true
          example: offers/offer1234

    WarningCollection:
      type: object
      additionalProperties: false
      required:
        - warnings
      properties:
        warnings:
          type: array
          description: |
            Details of any issues that may not prevent the processing of a request but may affect the results.
            Warnings therefore provides information about non-critical issues that occurred during the booking process, such as a price difference with the initially offered price at booking time.
          items:
            $ref: '#/components/schemas/Warning'
          minItems: 1

    Zone:
      type: object
      additionalProperties: false
      description: |
        carrier - carrier responsible for the transport.
        entryStation - Station to enter the zone in case the product requires to enter the zone via a specific station
        (e.g. local zone ticket to start from the main rail station).
        terminalStation - Terminal station in case the product requires a destination within the zone (e.g. local ticket
        to go to the main rail station).
      required:
        - carrier
      properties:
        binaryZoneId:
          description: |
            Id to support local traffic standards (e.g. VDV,...)
          type: string
          format: byte
          nullable: true
        carrier:
          $ref: '#/components/schemas/CompanyRef'
        name:
          type: string
          nullable: true
        entryStation:
          $ref: '#/components/schemas/StopPlace'
        terminalStation:
          $ref: '#/components/schemas/StopPlace'
        zoneIds:
          description: |
            to be used in bar codes
          type: array
          items:
            type: integer
            format: int32
          nullable: true
        nutsCode:
          description: |
            Nomenclature des units territoriales statistiques COMMISSION REGULATION (EU) No 31/2011
          type: string
          nullable: true

    ZoneCollectionResponse:
      type: object
      additionalProperties: false
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        zones:
          type: array
          items:
            $ref: '#/components/schemas/ZoneDefinition'

    ZoneDefinition:
      type: object
      additionalProperties: false
      description: |
        Definition of a zone used to define regional validity. The area of the zone can be defined via a
        geo-polygon, a complete list of all contained stations or area codes from the NUTS code list.
        The carrier is either the carrier or transport authority responsible for the definition.
      required:
        - id
        - carrier
      properties:
        id:
          type: string
          nullable: false
        name:
          type: string
          nullable: true
        carrier:
          $ref: '#/components/schemas/CompanyRef'
        polygon:
          $ref: '#/components/schemas/Polygon'
        nutsCodes:
          type: array
          items:
            type: string
          nullable: true
        places:
          type: array
          items:
            $ref: '#/components/schemas/PlaceRef'

    # ------------- SilverRail Schemas -------------
    MapInfoPart:
      type: object
      additionalProperties: false
      description: |
        Map information for a part of the path.
      properties:
        id:
          type: string
          nullable: false
        polyline:
          description: A list of semi-colon delimited geo-coordinates (latitude, longitude) that can be used to map the path.
          type: string

    MapFilter:
      type: object
      additionalProperties: false
      description: |
        Map data to be included/excluded from search.
      properties:
        includePolyline:
          description: Include polylines for trip legs
          type: boolean
        includeDirections:
          description: Include turn by turn directions
          type: boolean

    TripContentFilter:
      type: object
      additionalProperties: false
      description: |
        Additional trip data to be included/excluded from search.
      properties:
        includeIntermediateStops:
          description: |
            Specifies whether the result should include intermediate stops (between the passenger's board and alight stops).
            Provided by OJP.
          type: boolean

    TripContext:
      type: object
      additionalProperties: false
      description: |
        Details of service-related events, such as alerts, disruptions, and station conditions linked to one or more offers.
      properties:
        situations:
          type: array
          items:
            $ref: '#/components/schemas/Situation'

    RouteReference:
      type: object
      additionalProperties: false
      description: |
        Contains reference information about a route
      required:
        - id
      properties:
        id:
          allOf:
            - $ref: '#/components/schemas/RouteRef'
            - nullable: false # The referenced RouteRef is nullable, this overrides that
          description: |
            Id of the route
        name:
          description: |
            A name for the route, as known by the public.
          type: string
        nameTranslations:
          description: |
            Translations of the name.
          type: array
          items:
            $ref: '#/components/schemas/Translation'
        code:
          description: |
            A short code for the route.
          type: string
        transportMode:
          $ref: '#/components/schemas/PTMode'
        carrier:
          $ref: '#/components/schemas/NamedCompany'

    RouteRef:
      description: |
        Reference to a Route. This can be used in the '/routes/{routeId}'
        endpoint to retrieve further information about the route,
        or in `/service-info` endpoint to request trips on the route.
      type: string

    RouteReferenceRequest:
      type: object
      additionalProperties: false
      description: |
        Parameters for restricting the route reference request.
      properties:
        searchTerm:
          type: string
          description: |
            Search term for which to find matching routes.
        ptModes:
          $ref: '#/components/schemas/ModeFilter'

    RouteReferenceResponse:
      type: object
      additionalProperties: false
      required:
        - routes
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        routes:
          type: array
          items:
            $ref: '#/components/schemas/RouteReference'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    StopEventRequest:
      type: object
      additionalProperties: false
      description: |
        Contains the request parameters that control the compilation of the departure and arrival events. Provided by OJP
      required:
        - place
        - startTime
      properties:
        place:
          $ref: '#/components/schemas/PlaceContext'
        stopEventDataFilter:
          $ref: '#/components/schemas/StopEventDataFilter'
        stopEventPolicy:
          $ref: '#/components/schemas/StopEventPolicy'
        stopEventContentFilter:
          $ref: '#/components/schemas/StopEventContentFilter'

    PlaceContext:
      type: object
      additionalProperties: false
      description: |
        A location and access to it by individual transport options. Provided by OJP.
      properties:
        placeRef:
          description: |
            Place for which to obtain stop event information. Provided by OJP.
          $ref: '#/components/schemas/PlaceRef'
        depArrTime:
          description: |
            Target departure or arrival time at the location. Needs to be in local time. Provided by OJP.
          type: string

    StopEventDataFilter:
      type: object
      description: |
        Controls the specific data content within each event, such as transport modes, making 
        it useful for refining data at a more detailed level within the scope set by. Provided by OJP.
      properties:
        ptModeFilter:
          description: |
            Modes to be considered in stop events. Provided by OJP.
          $ref: '#/components/schemas/ModeFilter'

    StopEventPolicy:
      type: object
      description: |
        Used to filter stop events. Sets boundaries on the amount, timing, and type of events (arrival or departure) 
        that are returned. Provided by OJP.
      properties:
        numberOfResults:
          description: |
            Maximum number of events to be returned. Provided by OJP.
          type: integer
          format: int32
          minimum: 0
          default: 40
          nullable: true
        timeWindow:
          description: |
            Duration from the provided start time to consider stop events. Provided by OJP.
            Note: numberOfResults may truncate results.
            Note: Time units more granular than minutes will be ignored.
          type: string
          format: duration
          nullable: true
          default: PT2H
        stopEventType:
          description : |
            Defines if only departures or arrivals or both should be returned. Provided by OJP.
          enum:
            - 'DEPARTURE'
            - 'ARRIVAL'
            - 'BOTH'
          default: 'BOTH'

    StopEventContentFilter:
      type: object
      description: |
        Includes or excludes specific optional data layers within each event (e.g., real-time updates), 
        affecting the level of detail but not the scope or core content. Provided by OJP.
      properties:
        includeRealtimeData:
          description : |
            Whether realtime information for each stop event should be included in the response. Provided by OJP.
          type: boolean
          default: false

    StopEventResponse:
      type: object
      additionalProperties: false
      description: |
        Response structure for departure and arrival events at stops. Provided by OJP.
      required:
        - place
        - stopEvents
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        place:
          $ref: '#/components/schemas/PlaceRef'
        stopEvents:
          type: array
          items:
            $ref: '#/components/schemas/StopEvent'
        _links:
          description: |
            
            Java Property Name: 'links'
          type: array
          items:
            $ref: '#/components/schemas/Link'

    StopEvent:
      type: object
      description: |
        Details of a single departure/arrival event. Provided by OJP.
      required:
        - service
        - thisCall
      properties:
        service:
          $ref: '#/components/schemas/DatedJourney'
        thisCall:
          $ref: '#/components/schemas/CallAtNearStop'

    CallAtNearStop:
      type: object
      description: |
        A departure/arrival event at a nearby stop. Provided by OJP.
        Note: currently all stops returned will be at the requested location, walkDistance and 
        walkDuration will be empty.
      required:
        - callAtStop
      properties:
        callAtStop:
          $ref: '#/components/schemas/CallAtStop'
        walkDistance:
          description: |
            Distance from request location to this stop in metres.
          type: integer
          format: int32
          minimum: 0
          nullable: true
        walkDuration:
          description: |
            Distance from request location to this stop in ISO 8601 duration format.
          type: string
          format: duration
          nullable: true
          example: PT30M

    CallAtStop:
      type: object
      description: |
        The meeting of a vehicle journey with a specific scheduled stop point. Provided by OJP.
      required:
        - stopPoint
      properties:
        serviceArrival:
          $ref: '#/components/schemas/ServiceTime'
        serviceDeparture:
          $ref: '#/components/schemas/ServiceTime'
        status:
          $ref: '#/components/schemas/StopCallStatus'
        stopPoint:
          $ref: '#/components/schemas/StopPoint'

    StopPoint:
      type: object
      description: |
        A departure/arrival event at a nearby stop. Provided by OJP.
        Note: Nearby stops is not supported, all stops returned will be at the requested location.
      required:
        - stopPlaceRef
        - stopPlaceName
      properties:
        stopPlaceRef:
          $ref: '#/components/schemas/StopPlaceRef'
        stopPlaceName:
          type: string
          description: A human-readable name for the stop.
          nullable: false
          example: Luzern

    ServiceInfoRequest:
      type: object
      additionalProperties: false
      description: |
        Used to request further information about service(s). Either 'journeyRef' or 'routeRef'
        must be provided. A valid JourneyRef can be obtained from '/trips',
        '/trips-collection' or '/stop-events' response. A valid routeRef can be obtained
        from the '/routes' response.
        The operatingDayRef can be obtained from the same response, or created in
        ISO format for the desired day.
        Based on OJP TripInfoRequest
      required:
        - operatingDayRef
      oneOf:
        - required:
            - journeyRef
        - required:
            - routeRef
      properties:
        journeyRef:
          $ref: '#/components/schemas/JourneyRef'
        routeRef:
          $ref: '#/components/schemas/RouteRef'
        operatingDayRef:
          $ref: '#/components/schemas/OperationDayRef'
        serviceInfoPolicy:
          $ref: '#/components/schemas/ServiceInfoPolicy'

    ServiceInfoPolicy:
      type: object
      additionalProperties: false
      properties:
        UseTimetabledDataOnly:
          description: |
            Realtime data is not returned when set to True. Default is false.
          type: boolean

    ServiceInfoResponse:
      type: object
      additionalProperties: false
      description: |
        Response for a ServiceInfoRequest. Based on OJP TripInfoResponse.
      required:
        - serviceInfoResult
      properties:
        warnings:
          $ref: '#/components/schemas/WarningCollection'
        serviceInfoResult:
          type: array
          items:
            $ref: '#/components/schemas/ServiceInfoResult'

    ServiceInfoResult:
      type: object
      additionalProperties: false
      description: |
        Wrapper element for a service information query result. Based on OJP TripInfoResult.
      properties:
        service:
          $ref: '#/components/schemas/DatedJourney'
        calls:
          type: array
          description: |
            Ordered list of stops the service calls at.
          items:
            $ref: '#/components/schemas/CallAtStop'

    # ------------- Schemas provided by SIRI (http://www.normes-donnees-tc.org/wp-content/uploads/2015/04/SIRI-part5.pdf) -------------
    Situation:
      type: object
      additionalProperties: false
      description: |
        Information about a specific service-related event.
      required:
        - situationFullRef
        - description
      properties:
        situationFullRef:
          description: |
            A unique reference Id for the event.
          type: string
        description:
          description: |
            A brief summary of the event.
          type: string
        infoLink:
          description: |
            A URL to a resource with detailed information about the situation.
          type: array
          items:
            $ref: '#/components/schemas/InfoLink'
        severity:
          description: |
            The severity of the event.
          type: string
          default: 'NORMAL'
          enum:
            - 'PTI26_0'
            - 'UNKNOWN'
            - 'PTI26_1'
            - 'VERY_SLIGHT'
            - 'PTI26_2'
            - 'SLIGHT'
            - 'PTI26_3'
            - 'NORMAL'
            - 'PTI26_4'
            - 'SEVERE'
            - 'PTI26_5'
            - 'VERY_SEVERE'
            - 'PTI26_6'
            - 'NO_IMPACT'
            - 'PTI26_255'
            - 'UNDEFINED'
        detail:
          description: |
            Additional information about the event that can be used by automatic speech generation systems so a user
            can hear a situation over the telephone without the need for a human operator.
          type: string
        progress:
          description: |
            The status of the event.
          type: string
          enum:
            - 'DRAFT'
            - 'PENDING_APPROVAL'
            - 'APPROVED_DRAFT'
            - 'OPEN'
            - 'PUBLISHED'
            - 'CLOSING'
            - 'CLOSED'
        SituationExtensions:
          $ref: '#/components/schemas/SituationExtensions'

    InfoLink:
      type: object
      properties:
        uri:
          description: |
            Link url
          type: string
        label:
          description: |
            label for link
          type: string
        image:
          description: |
            Image associated with link
          type: string
        linkContent:
          $ref: '#/components/schemas/LinkContent'

    LinkContent:
      nullable: true
      enum:
        - 'OTHER'
        - 'TIMETABLE'
        - 'RELATED_SITE'
        - 'DETAILS'

    SituationExtensions:
      type: object
      description: Metadata about an event that helps with event categorization.
      properties:
        isDisruption:
          description: |
            Whether the event caused a disruption to the service.
          type: boolean
        isAlert:
          description: |
            Whether the event is an alert.
          type: boolean
        category:
          description: |
            The event category.
          type: string