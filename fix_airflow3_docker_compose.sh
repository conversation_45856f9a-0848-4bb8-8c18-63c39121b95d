#!/bin/bash
# Fix for Airflow 3.x docker-compose configuration

set -e

echo "🔧 FIXING AIRFLOW 3.x DOCKER COMPOSE"
echo "===================================="

# Check if we're in the right directory
if [ ! -f "airflow/docker-compose.yaml" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

PROJECT_ROOT=$(pwd)
echo "Project root: $PROJECT_ROOT"

cd airflow/

echo ""
echo "🛑 Stopping current services..."
docker-compose down --volumes

echo ""
echo "📝 Creating Airflow 3.x compatible docker-compose.yaml..."

# Backup current file
cp docker-compose.yaml docker-compose.yaml.backup.$(date +%s)

# Create Airflow 3.x compatible docker-compose.yaml
cat > docker-compose.yaml << EOF
# Airflow 3.x compatible configuration
---
x-airflow-common:
  &airflow-common
  image: \${AIRFLOW_IMAGE_NAME:-apache/airflow:3.0.2}
  environment:
    &airflow-common-env
    AIRFLOW__CORE__EXECUTOR: LocalExecutor
    AIRFLOW__CORE__AUTH_MANAGER: airflow.providers.fab.auth_manager.fab_auth_manager.FabAuthManager
    AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
    AIRFLOW__CORE__FERNET_KEY: ''
    AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true'
    AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
    AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK: 'true'
    _PIP_ADDITIONAL_REQUIREMENTS: \${_PIP_ADDITIONAL_REQUIREMENTS:-}
  volumes:
    - \${AIRFLOW_PROJ_DIR:-.}/dags:/opt/airflow/dags
    - \${AIRFLOW_PROJ_DIR:-.}/logs:/opt/airflow/logs
    - \${AIRFLOW_PROJ_DIR:-.}/config:/opt/airflow/config
    - \${AIRFLOW_PROJ_DIR:-.}/plugins:/opt/airflow/plugins
    - $PROJECT_ROOT/shared:/opt/airflow/shared
    - $PROJECT_ROOT/data:/opt/airflow/data
  user: "\${AIRFLOW_UID:-50000}:0"
  depends_on:
    &airflow-common-depends-on
    postgres:
      condition: service_healthy

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 10s
      retries: 5
      start_period: 5s
    restart: always

  airflow-webserver:
    <<: *airflow-common
    command: standalone
    ports:
      - "8080:8080"
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: always
    depends_on:
      <<: *airflow-common-depends-on
      airflow-init:
        condition: service_completed_successfully

  airflow-init:
    <<: *airflow-common
    entrypoint: /bin/bash
    command:
      - -c
      - |
        mkdir -p /opt/airflow/{logs,dags,plugins,config}
        chown -R "\${AIRFLOW_UID:-50000}:0" /opt/airflow/{logs,dags,plugins,config}
        exec /entrypoint airflow version
    environment:
      <<: *airflow-common-env
      _AIRFLOW_DB_MIGRATE: 'true'
      _AIRFLOW_WWW_USER_CREATE: 'true'
      _AIRFLOW_WWW_USER_USERNAME: \${_AIRFLOW_WWW_USER_USERNAME:-airflow}
      _AIRFLOW_WWW_USER_PASSWORD: \${_AIRFLOW_WWW_USER_PASSWORD:-airflow}
    user: "0:0"

volumes:
  postgres-db-volume:
EOF

echo "✅ Created Airflow 3.x compatible docker-compose.yaml"

echo ""
echo "🚀 Starting Airflow services..."
docker-compose up -d

echo ""
echo "⏳ Waiting for services to start..."
sleep 30

echo ""
echo "📊 Checking service status..."
docker-compose ps

echo ""
echo "🧪 Testing shared directory mount..."
if docker-compose exec -T airflow-webserver test -d /opt/airflow/shared 2>/dev/null; then
    echo "✅ /opt/airflow/shared directory exists!"
    
    echo ""
    echo "Testing Python import..."
    docker-compose exec -T airflow-webserver python -c "
import sys
sys.path.insert(0, '/opt/airflow/shared')
try:
    from shared.common import constants
    print('✅ Successfully imported shared.common.constants')
except Exception as e:
    print(f'❌ Import failed: {e}')
" 2>/dev/null || echo "⚠️  Container may still be starting..."

else
    echo "❌ /opt/airflow/shared not found"
    echo "Container contents:"
    docker-compose exec -T airflow-webserver ls -la /opt/airflow/ 2>/dev/null || echo "Cannot access container"
fi

echo ""
echo "🏁 AIRFLOW 3.x FIX COMPLETE"
echo "=========================="
echo ""
echo "🌐 Access Airflow UI:"
echo "   URL: http://localhost:8080"
echo "   Username: airflow"
echo "   Password: airflow"
echo ""
echo "📋 Key changes for Airflow 3.x:"
echo "   - Using 'standalone' command instead of 'webserver'"
echo "   - Simplified architecture with LocalExecutor"
echo "   - Removed Celery/Redis components"
echo ""
echo "🔍 To check logs:"
echo "   docker-compose logs airflow-webserver"

cd ..  # Return to project root
EOF
