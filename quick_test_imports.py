#!/usr/bin/env python3
"""
Quick test to verify the current import fix status.
"""

import os
import sys
from pathlib import Path

def test_local_imports():
    """Test shared module imports locally."""
    print("🧪 Testing local shared module imports...")
    
    # Add shared to path
    project_root = Path(__file__).parent
    shared_path = project_root / "shared"
    
    if not shared_path.exists():
        print(f"❌ Shared directory not found: {shared_path}")
        return False
    
    sys.path.insert(0, str(shared_path))
    
    try:
        from shared.common import constants
        print("✅ constants imported successfully")
    except Exception as e:
        print(f"❌ constants import failed: {e}")
        return False
    
    try:
        from shared.common import data_parsers
        print("✅ data_parsers imported successfully")
    except Exception as e:
        print(f"❌ data_parsers import failed: {e}")
        return False
    
    try:
        from shared.common import validation_utils
        print("✅ validation_utils imported successfully")
    except Exception as e:
        print(f"❌ validation_utils import failed: {e}")
        return False
    
    print("🎉 All local imports successful!")
    return True

def check_docker_setup():
    """Check if Docker setup looks correct."""
    print("\n🐳 Checking Docker setup...")
    
    docker_compose_path = Path("airflow/docker-compose.yaml")
    
    if not docker_compose_path.exists():
        print("❌ docker-compose.yaml not found")
        return False
    
    with open(docker_compose_path, 'r') as f:
        content = f.read()
    
    if "/opt/airflow/shared" in content:
        print("✅ Shared volume mount found in docker-compose.yaml")
        return True
    else:
        print("❌ Shared volume mount NOT found in docker-compose.yaml")
        return False

def check_dag_files():
    """Check if DAG files have correct import logic."""
    print("\n📝 Checking DAG files...")
    
    dags_dir = Path("airflow/dags")
    dag_files = list(dags_dir.glob("*_dag.py"))
    
    if not dag_files:
        print("❌ No DAG files found")
        return False
    
    print(f"Found {len(dag_files)} DAG files")
    
    issues = []
    
    for dag_file in dag_files:
        with open(dag_file, 'r') as f:
            content = f.read()
        
        # Check for correct import pattern
        if "sys.path.insert(0, shared_path)" in content:
            print(f"✅ {dag_file.name} has correct import logic")
        elif "from shared.common" in content:
            print(f"⚠️  {dag_file.name} imports shared modules but may have old logic")
            issues.append(dag_file.name)
        else:
            print(f"ℹ️  {dag_file.name} doesn't import shared modules")
    
    return len(issues) == 0

def main():
    """Run all tests."""
    print("🔍 QUICK IMPORT TEST")
    print("=" * 30)
    
    # Test 1: Local imports
    local_ok = test_local_imports()
    
    # Test 2: Docker setup
    docker_ok = check_docker_setup()
    
    # Test 3: DAG files
    dag_ok = check_dag_files()
    
    print("\n" + "=" * 30)
    print("📊 SUMMARY")
    print("=" * 30)
    
    print(f"Local imports: {'✅ OK' if local_ok else '❌ FAIL'}")
    print(f"Docker setup:  {'✅ OK' if docker_ok else '❌ FAIL'}")
    print(f"DAG files:     {'✅ OK' if dag_ok else '❌ ISSUES'}")
    
    if local_ok and docker_ok and dag_ok:
        print("\n🎉 Everything looks good!")
        print("\nNext steps:")
        print("1. Run: ./fix_airflow_imports.sh")
        print("2. Or manually: cd airflow && docker-compose up -d")
        return 0
    else:
        print("\n❌ Issues found. Recommended actions:")
        if not local_ok:
            print("- Check that shared/ directory exists and contains common/ subdirectory")
        if not docker_ok:
            print("- Run: ./fix_airflow_imports.sh to fix Docker configuration")
        if not dag_ok:
            print("- DAG files may need import logic updates")
        return 1

if __name__ == "__main__":
    exit(main())
