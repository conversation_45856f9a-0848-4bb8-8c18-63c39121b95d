#!/usr/bin/env python3
"""
Test script to verify that Airflow DAGs can import shared modules correctly.

This script simulates the import behavior that happens in the Airflow container
and verifies that all DAGs can successfully import their required shared modules.
"""

import os
import sys
import tempfile
import subprocess
from pathlib import Path

def test_dag_imports():
    """Test that all DAG files can import shared modules without errors."""
    print("Testing DAG imports...")
    
    # Get the project root directory
    project_root = Path(__file__).parent
    airflow_dags_dir = project_root / "airflow" / "dags"
    shared_dir = project_root / "shared"
    
    print(f"Project root: {project_root}")
    print(f"Airflow DAGs directory: {airflow_dags_dir}")
    print(f"Shared directory: {shared_dir}")
    
    # Check that directories exist
    if not airflow_dags_dir.exists():
        print(f"❌ Airflow DAGs directory not found: {airflow_dags_dir}")
        return False
    
    if not shared_dir.exists():
        print(f"❌ Shared directory not found: {shared_dir}")
        return False
    
    # Find all DAG files
    dag_files = list(airflow_dags_dir.glob("*_dag.py"))
    print(f"Found {len(dag_files)} DAG files")
    
    if not dag_files:
        print("❌ No DAG files found")
        return False
    
    # Test each DAG file
    success_count = 0
    for dag_file in dag_files:
        print(f"\nTesting {dag_file.name}...")
        
        # Create a test script that simulates the Docker environment
        test_script = f"""
import sys
import os

# Simulate Docker container environment
sys.path.insert(0, '/opt/airflow/shared')
sys.path.insert(0, '{shared_dir}')

# Try to import the DAG
try:
    # Read the DAG file and extract imports
    with open('{dag_file}', 'r') as f:
        dag_content = f.read()
    
    # Check if it has shared imports
    if 'from shared.common' in dag_content:
        print("✓ DAG has shared imports")
        
        # Try importing shared modules that the DAG uses
        if 'constants' in dag_content:
            from shared.common import constants
            print("✓ constants imported successfully")
        
        if 'data_parsers' in dag_content:
            from shared.common import data_parsers
            print("✓ data_parsers imported successfully")
        
        if 'validation_utils' in dag_content:
            from shared.common import validation_utils
            print("✓ validation_utils imported successfully")
        
        if 'type_definition_parser' in dag_content:
            from shared.common import type_definition_parser
            print("✓ type_definition_parser imported successfully")
        
        if 'definition_store' in dag_content:
            from shared.common.definition_store import DefinitionStore
            print("✓ DefinitionStore imported successfully")
        
        print("✅ All shared imports successful for {dag_file.name}")
    else:
        print("ℹ️  DAG does not use shared imports")
    
except Exception as e:
    print(f"❌ Import failed for {dag_file.name}: {{e}}")
    sys.exit(1)
"""
        
        # Write test script to temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(test_script)
            temp_script = f.name
        
        try:
            # Run the test script
            result = subprocess.run([sys.executable, temp_script], 
                                  capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"✅ {dag_file.name} imports successful")
                success_count += 1
            else:
                print(f"❌ {dag_file.name} import failed:")
                print(f"   stdout: {result.stdout}")
                print(f"   stderr: {result.stderr}")
        
        except subprocess.TimeoutExpired:
            print(f"❌ {dag_file.name} import test timed out")
        
        except Exception as e:
            print(f"❌ {dag_file.name} test error: {e}")
        
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_script)
            except:
                pass
    
    print(f"\n=== SUMMARY ===")
    print(f"Total DAGs tested: {len(dag_files)}")
    print(f"Successful imports: {success_count}")
    print(f"Failed imports: {len(dag_files) - success_count}")
    
    if success_count == len(dag_files):
        print("🎉 All DAG imports successful!")
        return True
    else:
        print("❌ Some DAG imports failed")
        return False

def test_shared_modules():
    """Test that shared modules can be imported directly."""
    print("\nTesting shared modules...")
    
    project_root = Path(__file__).parent
    shared_dir = project_root / "shared"
    
    # Add shared directory to path
    sys.path.insert(0, str(shared_dir))
    
    try:
        # Test core shared modules
        from shared.common import constants
        print("✅ constants imported")
        
        from shared.common import data_parsers
        print("✅ data_parsers imported")
        
        from shared.common import validation_utils
        print("✅ validation_utils imported")
        
        from shared.common import type_definition_parser
        print("✅ type_definition_parser imported")
        
        from shared.common.definition_store import DefinitionStore
        print("✅ DefinitionStore imported")
        
        print("🎉 All shared modules imported successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Shared module import failed: {e}")
        return False

def main():
    """Run all import tests."""
    print("🧪 Testing Airflow DAG imports...")
    print("=" * 50)
    
    # Test shared modules first
    shared_success = test_shared_modules()
    
    # Test DAG imports
    dag_success = test_dag_imports()
    
    print("\n" + "=" * 50)
    if shared_success and dag_success:
        print("🎉 All tests passed! DAGs should work in Airflow.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
