#!/bin/bash
# Quick fix for Airflow 3.x compatibility

set -e

echo "🚀 QUICK AIRFLOW 3.x FIX"
echo "========================"

if [ ! -f "airflow/docker-compose.yaml" ]; then
    echo "❌ Please run from project root directory"
    exit 1
fi

PROJECT_ROOT=$(pwd)
cd airflow/

echo "🛑 Stopping services..."
docker-compose down

echo "📝 Applying Airflow 3.x fixes..."

# Backup
cp docker-compose.yaml docker-compose.yaml.backup.$(date +%s)

# Fix the webserver command for Airflow 3.x
sed -i.tmp 's/command: webserver/command: standalone/' docker-compose.yaml

# Remove Celery/Redis dependencies that cause issues in Airflow 3.x
sed -i.tmp '/redis:/d' docker-compose.yaml
sed -i.tmp '/CELERY/d' docker-compose.yaml
sed -i.tmp '/CeleryExecutor/c\    AIRFLOW__CORE__EXECUTOR: LocalExecutor' docker-compose.yaml

# Remove temp file
rm -f docker-compose.yaml.tmp

echo "✅ Applied Airflow 3.x compatibility fixes"

echo "🚀 Starting services..."
docker-compose up -d

echo "⏳ Waiting for startup (60 seconds)..."
sleep 60

echo "📊 Checking services..."
docker-compose ps

echo ""
echo "🧪 Testing shared directory..."
if docker-compose exec -T airflow-webserver test -d /opt/airflow/shared 2>/dev/null; then
    echo "✅ Shared directory mounted successfully!"
    
    echo "Testing import..."
    docker-compose exec -T airflow-webserver python -c "
import sys
sys.path.insert(0, '/opt/airflow/shared')
from shared.common import constants
print('✅ Import successful!')
" 2>/dev/null || echo "⚠️  Import test failed - may need more time to start"

else
    echo "❌ Shared directory not found"
    echo "Checking container status..."
    docker-compose logs --tail=10 airflow-webserver
fi

echo ""
echo "🎉 QUICK FIX COMPLETE!"
echo "====================="
echo ""
echo "🌐 Access Airflow: http://localhost:8080 (airflow/airflow)"
echo ""
echo "If issues persist, try:"
echo "  docker-compose logs airflow-webserver"

cd ..
