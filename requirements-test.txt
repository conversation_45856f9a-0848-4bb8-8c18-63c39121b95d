# Testing dependencies for LLM Service Mapper project

# Core testing framework
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-xdist>=3.0.0  # Parallel test execution
pytest-html>=3.1.0   # HTML test reports
pytest-json-report>=1.5.0  # JSON test reports

# Test utilities
factory-boy>=3.2.0   # Test data factories
faker>=18.0.0        # Fake data generation
freezegun>=1.2.0     # Time mocking
responses>=0.23.0    # HTTP mocking

# Code quality
flake8>=6.0.0        # Linting
black>=23.0.0        # Code formatting
isort>=5.12.0        # Import sorting
mypy>=1.0.0          # Type checking
bandit>=1.7.0        # Security linting

# Coverage and reporting
coverage>=7.0.0
pytest-benchmark>=4.0.0  # Performance benchmarking

# Optional: Airflow testing (if Airflow is available)
# apache-airflow>=3.0.0
# apache-airflow-providers-common-sql

# Shared dependencies (from main project)
lxml>=4.9.0
xmlschema>=2.0.0
pyyaml>=6.0
requests>=2.28.0
pendulum>=2.1.0

# Development utilities
pre-commit>=3.0.0    # Git hooks
tox>=4.0.0          # Testing across environments
