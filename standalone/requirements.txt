# Requirements for Standalone Service Definition Parser
# This file contains the dependencies needed to run the standalone version
# of the service definition ingestion and parsing functionality.

# Core dependencies
requests>=2.28.0
lxml>=4.9.0
xmlschema>=2.0.0
PyYAML>=6.0

# Optional dependencies for enhanced functionality
# Uncomment if needed for your specific use case

# For advanced XML processing
# beautifulsoup4>=4.11.0
# xmltodict>=0.13.0

# For enhanced logging and monitoring
# structlog>=22.0.0
# colorlog>=6.7.0

# For configuration management
# python-dotenv>=0.19.0
# configparser>=5.3.0

# For data validation
# jsonschema>=4.17.0
# cerberus>=1.3.4

# For performance improvements
# ujson>=5.6.0  # Faster JSON parsing
# orjson>=3.8.0  # Alternative fast JSON library

# Development and testing dependencies (optional)
# pytest>=7.2.0
# pytest-cov>=4.0.0
# black>=22.0.0
# flake8>=5.0.0
# mypy>=0.991
