# Standalone Parser - Quick Reference Card

## 🚀 Installation (2 minutes)

```bash
# 1. Install dependencies
pip install -r requirements_standalone.txt

# 2. Test installation
python test_standalone_parser.py

# 3. Ready to use!
```

## 📋 Basic Usage

### Minimal Command
```bash
python standalone_ingest_parser.py \
  --service-name my_service \
  --wsdl-uri path/to/service.wsdl \
  --openapi-uri path/to/api.yaml
```

### With All Options
```bash
python standalone_ingest_parser.py \
  --service-name production_service \
  --wsdl-uri https://api.example.com/service.wsdl \
  --xsd-uri https://api.example.com/schema.xsd \
  --openapi-uri https://api.example.com/openapi.yaml \
  --output-base-path /data/parsed \
  --catalog-path /data/catalog.json \
  --log-level DEBUG \
  --log-file parser.log
```

### Shell Script (Easier)
```bash
# Basic
./run_parser.sh -s my_service -w service.wsdl -o api.yaml

# Advanced
./run_parser.sh -s prod_service \
                -w https://api.com/service.wsdl \
                -x https://api.com/schema.xsd \
                -o https://api.com/api.yaml \
                -p /data/output \
                -l DEBUG \
                -f prod.log
```

## 📁 Output Structure

```
output-base-path/
├── service_name/
│   ├── v{timestamp}/           # Versioned output
│   │   ├── parsed_wsdl.json         # ✅ WSDL data
│   │   ├── parsed_wsdl_types.json   # 🔧 WSDL type definitions
│   │   ├── parsed_xsd.json          # ✅ XSD data (optional)
│   │   ├── parsed_xsd_types.json    # 🔧 XSD type definitions (optional)
│   │   ├── parsed_openapi.json      # ✅ OpenAPI data
│   │   ├── parsed_openapi_types.json # 🔧 OpenAPI type definitions
│   │   └── manifest.json            # ✅ Metadata
│   └── latest/                 # ➡️ Latest version
└── service_catalog.json        # 📋 Service registry
```

## 🔧 Command Line Arguments

| Argument | Required | Default | Description |
|----------|----------|---------|-------------|
| `--service-name` | ✅ | - | Service identifier |
| `--wsdl-uri` | ✅ | - | WSDL file path/URL |
| `--openapi-uri` | ✅ | - | OpenAPI file path/URL |
| `--xsd-uri` | ❌ | - | XSD file path/URL |
| `--output-base-path` | ❌ | `data/parsed_definitions` | Output directory |
| `--catalog-path` | ❌ | `service_catalog.json` | Catalog file |
| `--log-level` | ❌ | `INFO` | DEBUG/INFO/WARNING/ERROR |
| `--log-file` | ❌ | - | Log file path |

## 🎯 Common Use Cases

### Development Testing
```bash
python standalone_ingest_parser.py \
  --service-name dev_test \
  --wsdl-uri include/sample_data/wsdl_input/test.wsdl \
  --openapi-uri include/sample_data/osdmp_target/test.yaml \
  --log-level DEBUG
```

### CI/CD Pipeline
```bash
# In your CI script
python standalone_ingest_parser.py \
  --service-name $SERVICE_NAME \
  --wsdl-uri $WSDL_PATH \
  --openapi-uri $OPENAPI_PATH \
  --output-base-path artifacts/parsed \
  --log-level INFO
```

### Batch Processing
```bash
# Process multiple services
for service in service1 service2 service3; do
  python standalone_ingest_parser.py \
    --service-name $service \
    --wsdl-uri data/$service.wsdl \
    --openapi-uri data/$service.yaml
done
```

### Remote URLs
```bash
python standalone_ingest_parser.py \
  --service-name remote_api \
  --wsdl-uri https://api.example.com/service.wsdl \
  --openapi-uri https://api.example.com/openapi.yaml
```

## ✅ Success Indicators

### Successful Run
```
✅ All parameters validated successfully
✅ WSDL successfully parsed and stored
✅ OpenAPI successfully parsed and stored
✅ Created manifest file
✅ Registered service in catalog
✅ Processing completed successfully!
```

### Exit Codes
- `0` = Success
- `1` = Error (check logs)
- `130` = Interrupted (Ctrl+C)

## 🔍 Troubleshooting

### Quick Fixes
```bash
# Test installation
python test_standalone_parser.py

# Check dependencies
pip list | grep -E "(requests|lxml|xmlschema|PyYAML)"

# Verbose logging
python standalone_ingest_parser.py ... --log-level DEBUG

# Check file permissions
ls -la path/to/your/files
```

### Common Issues
| Issue | Solution |
|-------|----------|
| Import errors | `pip install -r requirements_standalone.txt` |
| File not found | Check file paths and permissions |
| Network timeout | Check URL accessibility |
| Parse errors | Validate XML/YAML syntax |
| Permission denied | Check write permissions for output directory |

## 📊 vs Airflow DAG

| Feature | Standalone | Airflow DAG |
|---------|------------|-------------|
| **Setup Time** | 2 minutes | 30+ minutes |
| **Dependencies** | 4 packages | Full Airflow |
| **Memory Usage** | ~50MB | ~500MB+ |
| **Execution** | Immediate | Scheduled |
| **Monitoring** | Log files | Airflow UI |
| **Best For** | Dev, CI/CD, Edge | Production pipelines |

## 🚀 Integration Examples

### GitHub Actions
```yaml
- name: Parse Service Definitions
  run: |
    pip install -r requirements_standalone.txt
    python standalone_ingest_parser.py \
      --service-name ${{ matrix.service }} \
      --wsdl-uri services/${{ matrix.service }}.wsdl \
      --openapi-uri apis/${{ matrix.service }}.yaml
```

### Docker
```dockerfile
FROM python:3.11-slim
COPY requirements_standalone.txt .
RUN pip install -r requirements_standalone.txt
COPY . .
CMD ["python", "standalone_ingest_parser.py", "--help"]
```

### Cron Job
```bash
# Daily processing at 2 AM
0 2 * * * cd /path/to/parser && python standalone_ingest_parser.py --service-name daily_sync --wsdl-uri /data/service.wsdl --openapi-uri /data/api.yaml
```

## 📚 Documentation Links

- **[README_standalone.md](README_standalone.md)**: Complete guide
- **[STANDALONE_IMPLEMENTATION_SUMMARY.md](STANDALONE_IMPLEMENTATION_SUMMARY.md)**: Technical details
- **[CHANGELOG_STANDALONE.md](CHANGELOG_STANDALONE.md)**: Version history

## 🆘 Getting Help

```bash
# Built-in help
python standalone_ingest_parser.py --help
./run_parser.sh --help

# Test suite
python test_standalone_parser.py

# Version info
python standalone_ingest_parser.py --version
```

---

**🎯 TL;DR**: Install deps → Run test → Use with your data → Done!
