#!/bin/bash
# Docker usage examples for the standalone parser

set -e

echo "🐳 Standalone Parser - Docker Usage Examples"
echo "============================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Change to standalone directory
cd "$(dirname "$0")/.."

echo -e "${BLUE}Example 1: Build Docker Image${NC}"
docker build -t standalone-parser:latest .
echo -e "${GREEN}✅ Docker image built${NC}"
echo ""

echo -e "${BLUE}Example 2: Run with Help${NC}"
docker run --rm standalone-parser:latest python src/standalone_ingest_parser.py --help
echo ""

echo -e "${BLUE}Example 3: Process Sample Data with Volume Mounts${NC}"
# Create local directories
mkdir -p ./data ./output ./logs

# Copy sample data to local directory
cp -r ../shared/sample_data/* ./data/ 2>/dev/null || echo "Sample data not found, using Docker volumes"

# Run with volume mounts
docker run --rm \
  -v "$(pwd)/data:/app/data:ro" \
  -v "$(pwd)/output:/app/output:rw" \
  -v "$(pwd)/logs:/app/logs:rw" \
  standalone-parser:latest \
  python src/standalone_ingest_parser.py \
    --service-name docker_example \
    --wsdl-uri /app/data/wsdl_input/test.wsdl \
    --openapi-uri /app/data/osdmp_target/test.yaml \
    --output-base-path /app/output \
    --log-file /app/logs/docker_parser.log \
    --log-level INFO

echo -e "${GREEN}✅ Docker processing completed${NC}"
echo ""

echo -e "${BLUE}Example 4: Using Docker Compose${NC}"
docker-compose up --build parser-sample
echo -e "${GREEN}✅ Docker Compose processing completed${NC}"
echo ""

echo -e "${BLUE}Example 5: Interactive Docker Shell${NC}"
echo -e "${YELLOW}Starting interactive shell (type 'exit' to quit)${NC}"
docker run -it --rm \
  -v "$(pwd)/data:/app/data:ro" \
  -v "$(pwd)/output:/app/output:rw" \
  standalone-parser:latest bash

echo -e "${BLUE}Example 6: Batch Processing with Docker${NC}"
docker run --rm \
  -v "$(pwd)/data:/app/data:ro" \
  -v "$(pwd)/output:/app/output:rw" \
  standalone-parser:latest \
  bash -c "
    echo 'Processing multiple services...'
    for service in service1 service2 service3; do
      echo \"Processing \$service...\"
      python src/standalone_ingest_parser.py \
        --service-name \$service \
        --wsdl-uri /app/data/wsdl_input/test.wsdl \
        --openapi-uri /app/data/osdmp_target/test.yaml \
        --output-base-path /app/output \
        --log-level INFO || echo \"Failed to process \$service\"
    done
    echo 'Batch processing completed'
  "

echo -e "${GREEN}✅ Batch processing completed${NC}"
echo ""

echo -e "${BLUE}Example 7: Check Output${NC}"
echo "Output files created:"
find ./output -name "*.json" | head -10
echo ""

echo -e "${GREEN}🎉 All Docker examples completed!${NC}"
echo ""
echo -e "${YELLOW}Useful Docker Commands:${NC}"
echo "• Build: docker build -t standalone-parser:latest ."
echo "• Run: docker run --rm -v \$(pwd)/data:/app/data standalone-parser:latest ..."
echo "• Shell: docker run -it --rm standalone-parser:latest bash"
echo "• Compose: docker-compose run parser ..."
echo "• Clean: docker system prune -f"
