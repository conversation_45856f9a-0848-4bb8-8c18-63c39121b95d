#!/bin/bash
# Basic usage examples for the standalone parser

set -e

echo "🚀 Standalone Parser - Basic Usage Examples"
echo "============================================"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Change to standalone directory
cd "$(dirname "$0")/.."

echo -e "${BLUE}Example 1: Help and Version${NC}"
python src/standalone_ingest_parser.py --help
echo ""
python src/standalone_ingest_parser.py --version
echo ""

echo -e "${BLUE}Example 2: Basic Processing with Sample Data${NC}"
python src/standalone_ingest_parser.py \
  --service-name example_basic \
  --wsdl-uri ../shared/sample_data/wsdl_input/test.wsdl \
  --openapi-uri ../shared/sample_data/osdmp_target/test.yaml \
  --output-base-path ./output \
  --log-level INFO

echo -e "${GREEN}✅ Basic processing completed${NC}"
echo ""

echo -e "${BLUE}Example 3: Using the Shell Script${NC}"
./run_parser.sh -s example_shell \
                -w ../shared/sample_data/wsdl_input/test.wsdl \
                -o ../shared/sample_data/osdmp_target/test.yaml \
                -p ./output \
                -l DEBUG

echo -e "${GREEN}✅ Shell script processing completed${NC}"
echo ""

echo -e "${BLUE}Example 4: With Custom Output and Logging${NC}"
mkdir -p ./output ./logs

python src/standalone_ingest_parser.py \
  --service-name example_custom \
  --wsdl-uri ../shared/sample_data/wsdl_input/test.wsdl \
  --openapi-uri ../shared/sample_data/osdmp_target/test.yaml \
  --output-base-path ./output \
  --catalog-path ./output/custom_catalog.json \
  --log-level DEBUG \
  --log-file ./logs/parser.log

echo -e "${GREEN}✅ Custom processing completed${NC}"
echo ""

echo -e "${BLUE}Example 5: Check Output Structure${NC}"
echo "Output directory structure:"
find ./output -type f -name "*.json" | head -10
echo ""

echo -e "${GREEN}🎉 All examples completed successfully!${NC}"
echo "Check the ./output directory for results"
