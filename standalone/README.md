# Standalone Service Definition Parser

A **production-ready** standalone Python executable that provides **100% functional equivalence** to the Airflow DAG `01_ingest_and_parse_definitions_dag.py` but without requiring Airflow infrastructure.

## 🚀 Quick Start

### Local Installation (2 minutes)
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Test installation
python tests/test_standalone_parser.py

# 3. Run with sample data
python src/standalone_ingest_parser.py \
  --service-name sample_service \
  --wsdl-uri ../shared/sample_data/wsdl_input/test.wsdl \
  --openapi-uri ../shared/sample_data/osdmp_target/test.yaml
```

### Docker Deployment (1 minute)
```bash
# 1. Build image
docker build -t standalone-parser:latest .

# 2. Run with volume mounts
docker run --rm \
  -v $(pwd)/data:/app/data:ro \
  -v $(pwd)/output:/app/output:rw \
  standalone-parser:latest \
  python src/standalone_ingest_parser.py \
    --service-name my_service \
    --wsdl-uri /app/data/service.wsdl \
    --openapi-uri /app/data/api.yaml \
    --output-base-path /app/output
```

## 📁 Directory Structure

```
standalone/
├── src/                              # Source code
│   ├── standalone_ingest_parser.py   # Main executable
│   ├── constants.py                  # Configuration
│   ├── data_parsers.py               # WSDL/OpenAPI parsers
│   └── validation_utils.py           # Data validation
├── tests/                            # Test suite
│   └── test_standalone_parser.py     # Comprehensive tests
├── docs/                             # Documentation
│   ├── README_standalone.md          # Complete guide
│   ├── QUICK_REFERENCE.md            # Quick reference
│   └── CHANGELOG_STANDALONE.md       # Version history
├── examples/                         # Usage examples
│   ├── basic_usage.sh                # Local examples
│   └── docker_usage.sh               # Docker examples
├── Dockerfile                        # Docker deployment
├── docker-compose.yaml               # Docker Compose setup
├── requirements.txt                  # Dependencies
└── run_parser.sh                     # Convenience script
```

## 🔧 Usage Examples

### Basic Command Line
```bash
# Minimal usage
python src/standalone_ingest_parser.py \
  --service-name my_service \
  --wsdl-uri path/to/service.wsdl \
  --openapi-uri path/to/api.yaml

# With all options
python src/standalone_ingest_parser.py \
  --service-name production_service \
  --wsdl-uri https://api.example.com/service.wsdl \
  --xsd-uri https://api.example.com/schema.xsd \
  --openapi-uri https://api.example.com/openapi.yaml \
  --output-base-path /data/parsed \
  --log-level DEBUG \
  --log-file parser.log
```

### Convenience Shell Script
```bash
# Basic usage
./run_parser.sh -s my_service -w service.wsdl -o api.yaml

# Advanced usage
./run_parser.sh -s prod_service \
                -w https://api.com/service.wsdl \
                -o https://api.com/api.yaml \
                -p /data/output \
                -l DEBUG \
                -f prod.log
```

### Docker Usage
```bash
# Basic Docker run
docker run --rm \
  -v /host/input:/app/data:ro \
  -v /host/output:/app/output:rw \
  standalone-parser:latest \
  python src/standalone_ingest_parser.py \
    --service-name docker_service \
    --wsdl-uri /app/data/service.wsdl \
    --openapi-uri /app/data/api.yaml \
    --output-base-path /app/output

# Using docker-compose
docker-compose run parser python src/standalone_ingest_parser.py --help
docker-compose run parser-sample  # Process sample data
```

## 📊 Output Structure

```
output/
├── service_name/
│   ├── v{timestamp}/           # Versioned output
│   │   ├── parsed_wsdl.json         # ✅ WSDL data
│   │   ├── parsed_wsdl_types.json   # 🔧 WSDL type definitions
│   │   ├── parsed_xsd.json          # ✅ XSD data (optional)
│   │   ├── parsed_xsd_types.json    # 🔧 XSD type definitions (optional)
│   │   ├── parsed_openapi.json      # ✅ OpenAPI data
│   │   ├── parsed_openapi_types.json # 🔧 OpenAPI type definitions
│   │   └── manifest.json            # ✅ Metadata
│   └── latest/                 # ➡️ Latest version
└── service_catalog.json        # 📋 Service registry
```

## 🧪 Testing

```bash
# Run comprehensive test suite
python tests/test_standalone_parser.py

# Run example scripts
./examples/basic_usage.sh
./examples/docker_usage.sh

# Test Docker deployment
docker build -t standalone-parser:test .
docker run --rm standalone-parser:test python tests/test_standalone_parser.py
```

## 🔧 Command Line Arguments

| Argument | Required | Default | Description |
|----------|----------|---------|-------------|
| `--service-name` | ✅ | - | Service identifier |
| `--wsdl-uri` | ✅ | - | WSDL file path/URL |
| `--openapi-uri` | ✅ | - | OpenAPI file path/URL |
| `--xsd-uri` | ❌ | - | XSD file path/URL |
| `--output-base-path` | ❌ | `data/parsed_definitions` | Output directory |
| `--catalog-path` | ❌ | `service_catalog.json` | Catalog file |
| `--log-level` | ❌ | `INFO` | DEBUG/INFO/WARNING/ERROR |
| `--log-file` | ❌ | - | Log file path |

## 🐳 Docker Deployment

### Volume Mounts
- **`/app/data`**: Input files (read-only)
- **`/app/output`**: Output files (read-write)
- **`/app/logs`**: Log files (read-write)

### Environment Variables
- **`PYTHONPATH`**: Set to `/app/src`
- **`LOG_LEVEL`**: Default logging level

### Resource Requirements
- **Memory**: 256MB minimum, 512MB recommended
- **CPU**: 0.5 cores minimum, 1.0 core recommended
- **Storage**: 100MB for application, additional for data

## 🔍 Troubleshooting

### Common Issues
```bash
# Test installation
python tests/test_standalone_parser.py

# Check dependencies
pip list | grep -E "(requests|lxml|xmlschema|PyYAML)"

# Verbose logging
python src/standalone_ingest_parser.py ... --log-level DEBUG

# Docker issues
docker run -it --rm standalone-parser:latest bash
```

### Exit Codes
- `0`: Success
- `1`: Error (check logs)
- `130`: Interrupted (Ctrl+C)

## 📚 Documentation

- **[docs/README_standalone.md](docs/README_standalone.md)**: Complete usage guide
- **[docs/QUICK_REFERENCE.md](docs/QUICK_REFERENCE.md)**: Quick reference card
- **[docs/CHANGELOG_STANDALONE.md](docs/CHANGELOG_STANDALONE.md)**: Version history
- **[examples/](examples/)**: Usage examples

## 🚀 Integration

### CI/CD Pipeline
```yaml
# GitHub Actions example
- name: Parse Service Definitions
  run: |
    cd standalone/
    pip install -r requirements.txt
    python src/standalone_ingest_parser.py \
      --service-name ${{ matrix.service }} \
      --wsdl-uri data/${{ matrix.service }}.wsdl \
      --openapi-uri data/${{ matrix.service }}.yaml
```

### Kubernetes Job
```yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: service-parser
spec:
  template:
    spec:
      containers:
      - name: parser
        image: standalone-parser:latest
        command: ["python", "src/standalone_ingest_parser.py"]
        args: ["--service-name", "k8s-service", "--wsdl-uri", "/data/service.wsdl", "--openapi-uri", "/data/api.yaml"]
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: output-volume
          mountPath: /app/output
      restartPolicy: Never
```

## 🆚 vs Airflow DAG

| Feature | Standalone | Airflow DAG |
|---------|------------|-------------|
| **Setup** | 2 minutes | 30+ minutes |
| **Memory** | ~50MB | ~500MB+ |
| **Dependencies** | 4 packages | Full Airflow |
| **Execution** | Immediate | Scheduled |
| **Monitoring** | Log files | Airflow UI |
| **Best For** | Dev, CI/CD, Edge | Production pipelines |

## 🤝 Contributing

1. Make changes in `src/` directory
2. Update tests in `tests/`
3. Test locally: `python tests/test_standalone_parser.py`
4. Test Docker: `docker build -t test . && docker run --rm test python tests/test_standalone_parser.py`
5. Update documentation in `docs/`

---

**🎯 Ready to parse your service definitions!**

Choose your preferred method:
- **Local**: `pip install -r requirements.txt && python src/standalone_ingest_parser.py --help`
- **Docker**: `docker build -t standalone-parser . && docker run --rm standalone-parser --help`
