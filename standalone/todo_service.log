2025-07-10 11:49:33,072 - __main__ - INFO - Starting Standalone Service Definition Ingestion and Parser
2025-07-10 11:49:33,072 - __main__ - INFO - Processing service: todo_service
2025-07-10 11:49:33,072 - __main__ - INFO - Validating parameters for service: todo_service
2025-07-10 11:49:33,073 - __main__ - ERROR - Failed to process service 'todo_service': Local file not found: openapi_uri=../shared/sample_data/todo_api.yaml
2025-07-10 11:49:33,073 - __main__ - ERROR - Processing failed: Local file not found: openapi_uri=../shared/sample_data/todo_api.yaml
2025-07-10 11:50:10,698 - __main__ - INFO - Starting Standalone Service Definition Ingestion and Parser
2025-07-10 11:50:10,698 - __main__ - INFO - Processing service: todo_service
2025-07-10 11:50:10,698 - __main__ - INFO - Validating parameters for service: todo_service
2025-07-10 11:50:10,698 - __main__ - ERROR - Failed to process service 'todo_service': Local file not found: openapi_uri=../shared/sample_data/osdmp_target/todo_api.yaml
2025-07-10 11:50:10,699 - __main__ - ERROR - Processing failed: Local file not found: openapi_uri=../shared/sample_data/osdmp_target/todo_api.yaml
2025-07-10 11:50:53,191 - __main__ - INFO - Starting Standalone Service Definition Ingestion and Parser
2025-07-10 11:50:53,191 - __main__ - INFO - Processing service: todo_service
2025-07-10 11:50:53,191 - __main__ - INFO - Validating parameters for service: todo_service
2025-07-10 11:50:53,191 - __main__ - ERROR - Failed to process service 'todo_service': Local XSD file not found: ../shared/sample_data/xsd_input/todo_types.xsd
2025-07-10 11:50:53,191 - __main__ - ERROR - Processing failed: Local XSD file not found: ../shared/sample_data/xsd_input/todo_types.xsd
2025-07-10 11:51:13,403 - __main__ - INFO - Starting Standalone Service Definition Ingestion and Parser
2025-07-10 11:51:13,403 - __main__ - INFO - Processing service: todo_service
2025-07-10 11:51:13,403 - __main__ - INFO - Validating parameters for service: todo_service
2025-07-10 11:51:13,403 - __main__ - INFO - All parameters validated successfully
2025-07-10 11:51:13,404 - __main__ - INFO - Starting definition parsing for service: todo_service
2025-07-10 11:51:13,404 - __main__ - INFO - Created output directory: ../data/parsed_definitions//todo_service/v1752141073
2025-07-10 11:51:13,404 - __main__ - INFO - Processing WSDL from: ../shared/sample_data/wsdl_input/todo-service.wsdl
2025-07-10 11:51:13,407 - __main__ - INFO - WSDL successfully parsed and stored at: ../data/parsed_definitions//todo_service/v1752141073/parsed_wsdl.json
2025-07-10 11:51:13,407 - __main__ - INFO - WSDL type definitions extracted and stored at: ../data/parsed_definitions//todo_service/v1752141073/parsed_wsdl_types.json
2025-07-10 11:51:13,407 - __main__ - INFO - Extracted 0 type definitions from WSDL
2025-07-10 11:51:13,407 - __main__ - INFO - Processing XSD from: ../shared/sample_data/xsd_input/todo-types.xsd
2025-07-10 11:51:13,504 - __main__ - INFO - XSD successfully parsed and stored at: ../data/parsed_definitions//todo_service/v1752141073/parsed_xsd.json
2025-07-10 11:51:13,505 - __main__ - INFO - XSD type definitions extracted and stored at: ../data/parsed_definitions//todo_service/v1752141073/parsed_xsd_types.json
2025-07-10 11:51:13,505 - __main__ - INFO - Extracted 0 type definitions from XSD
2025-07-10 11:51:13,505 - __main__ - INFO - Processing OpenAPI from: ../shared/sample_data/osdmp_target/todo-api.yaml
2025-07-10 11:51:13,548 - __main__ - INFO - OpenAPI successfully parsed and stored at: ../data/parsed_definitions//todo_service/v1752141073/parsed_openapi.json
2025-07-10 11:51:13,592 - __main__ - INFO - OpenAPI type definitions extracted and stored at: ../data/parsed_definitions//todo_service/v1752141073/parsed_openapi_types.json
2025-07-10 11:51:13,592 - __main__ - INFO - Extracted 11 type definitions from OpenAPI
2025-07-10 11:51:13,592 - __main__ - INFO - Created manifest file at: ../data/parsed_definitions//todo_service/v1752141073/manifest.json
2025-07-10 11:51:13,593 - __main__ - INFO - Registering service 'todo_service' in catalog
2025-07-10 11:51:13,596 - __main__ - INFO - Registered service 'todo_service' in catalog with version 1752141073
2025-07-10 11:51:13,596 - __main__ - INFO - Created 'latest' directory at ../data/parsed_definitions//todo_service/latest
2025-07-10 11:51:13,596 - __main__ - INFO - Successfully processed service 'todo_service'
2025-07-10 11:51:13,596 - __main__ - INFO - Processing completed successfully!
2025-07-10 11:51:13,596 - __main__ - INFO - Output directory: ../data/parsed_definitions//todo_service/v1752141073
2025-07-10 11:51:13,596 - __main__ - INFO - Files processed: ['wsdl', 'wsdl_types', 'xsd', 'xsd_types', 'openapi', 'openapi_types', 'manifest']
2025-07-10 14:46:42,224 - __main__ - INFO - Starting Standalone Service Definition Ingestion and Parser
2025-07-10 14:46:42,224 - __main__ - INFO - Processing service: todo_service
2025-07-10 14:46:42,224 - __main__ - INFO - Validating parameters for service: todo_service
2025-07-10 14:46:42,224 - __main__ - INFO - All parameters validated successfully
2025-07-10 14:46:42,224 - __main__ - INFO - Starting definition parsing for service: todo_service
2025-07-10 14:46:42,224 - __main__ - INFO - Created output directory: ../data/parsed_definitions//todo_service/v1752151602
2025-07-10 14:46:42,224 - __main__ - INFO - Processing WSDL from: ../shared/sample_data/wsdl_input/todo-service.wsdl
2025-07-10 14:46:42,228 - __main__ - INFO - WSDL successfully parsed and stored at: ../data/parsed_definitions//todo_service/v1752151602/parsed_wsdl.json
2025-07-10 14:46:42,230 - __main__ - INFO - WSDL type definitions extracted and stored at: ../data/parsed_definitions//todo_service/v1752151602/parsed_wsdl_types.json
2025-07-10 14:46:42,230 - __main__ - INFO - Extracted 0 type definitions from WSDL
2025-07-10 14:46:42,230 - __main__ - INFO - Processing XSD from: ../shared/sample_data/xsd_input/todo-types.xsd
2025-07-10 14:46:42,355 - __main__ - INFO - XSD successfully parsed and stored at: ../data/parsed_definitions//todo_service/v1752151602/parsed_xsd.json
2025-07-10 14:46:42,356 - __main__ - INFO - XSD type definitions extracted and stored at: ../data/parsed_definitions//todo_service/v1752151602/parsed_xsd_types.json
2025-07-10 14:46:42,356 - __main__ - INFO - Extracted 0 type definitions from XSD
2025-07-10 14:46:42,356 - __main__ - INFO - Processing OpenAPI from: ../shared/sample_data/osdmp_target/todo-api.yaml
2025-07-10 14:46:42,411 - __main__ - INFO - OpenAPI successfully parsed and stored at: ../data/parsed_definitions//todo_service/v1752151602/parsed_openapi.json
2025-07-10 14:46:42,463 - __main__ - INFO - OpenAPI type definitions extracted and stored at: ../data/parsed_definitions//todo_service/v1752151602/parsed_openapi_types.json
2025-07-10 14:46:42,463 - __main__ - INFO - Extracted 11 type definitions from OpenAPI
2025-07-10 14:46:42,463 - __main__ - INFO - Created manifest file at: ../data/parsed_definitions//todo_service/v1752151602/manifest.json
2025-07-10 14:46:42,464 - __main__ - INFO - Registering service 'todo_service' in catalog
2025-07-10 14:46:42,470 - __main__ - INFO - Registered service 'todo_service' in catalog with version 1752151602
2025-07-10 14:46:42,470 - __main__ - INFO - Created 'latest' directory at ../data/parsed_definitions//todo_service/latest
2025-07-10 14:46:42,470 - __main__ - INFO - Successfully processed service 'todo_service'
2025-07-10 14:46:42,470 - __main__ - INFO - Processing completed successfully!
2025-07-10 14:46:42,470 - __main__ - INFO - Output directory: ../data/parsed_definitions//todo_service/v1752151602
2025-07-10 14:46:42,470 - __main__ - INFO - Files processed: ['wsdl', 'wsdl_types', 'xsd', 'xsd_types', 'openapi', 'openapi_types', 'manifest']
