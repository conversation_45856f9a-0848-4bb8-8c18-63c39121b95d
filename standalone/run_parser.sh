#!/bin/bash
# Convenience script for running the standalone service definition parser

set -e  # Exit on any error

# Default values
SERVICE_NAME=""
WSDL_URI=""
XSD_URI=""
OPENAPI_URI=""
OUTPUT_PATH="data/parsed_definitions"
LOG_LEVEL="INFO"
LOG_FILE=""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Standalone Service Definition Parser - Convenience Script

OPTIONS:
    -s, --service-name NAME     Service name (required)
    -w, --wsdl-uri URI         WSDL file path or URL (required)
    -x, --xsd-uri URI          XSD file path or URL (optional)
    -o, --openapi-uri URI      OpenAPI file path or URL (required)
    -p, --output-path PATH     Output base path (default: data/parsed_definitions)
    -l, --log-level LEVEL      Log level: DEBUG, INFO, WARNING, ERROR (default: INFO)
    -f, --log-file FILE        Log file path (optional)
    -h, --help                 Show this help message

EXAMPLES:
    # Basic usage with local files
    $0 -s example_service \\
       -w include/sample_data/wsdl_input/example_service.wsdl \\
       -o include/sample_data/openapi_target/example_api.yaml

    # With all options
    $0 -s my_service \\
       -w path/to/service.wsdl \\
       -x path/to/schema.xsd \\
       -o path/to/api.yaml \\
       -p /custom/output \\
       -l DEBUG \\
       -f parser.log

    # With remote URLs
    $0 -s remote_service \\
       -w https://example.com/service.wsdl \\
       -o https://example.com/api.yaml \\
       -l INFO

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--service-name)
            SERVICE_NAME="$2"
            shift 2
            ;;
        -w|--wsdl-uri)
            WSDL_URI="$2"
            shift 2
            ;;
        -x|--xsd-uri)
            XSD_URI="$2"
            shift 2
            ;;
        -o|--openapi-uri)
            OPENAPI_URI="$2"
            shift 2
            ;;
        -p|--output-path)
            OUTPUT_PATH="$2"
            shift 2
            ;;
        -l|--log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        -f|--log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate required arguments
if [[ -z "$SERVICE_NAME" ]]; then
    print_error "Service name is required (-s/--service-name)"
    show_usage
    exit 1
fi

if [[ -z "$WSDL_URI" ]]; then
    print_error "WSDL URI is required (-w/--wsdl-uri)"
    show_usage
    exit 1
fi

if [[ -z "$OPENAPI_URI" ]]; then
    print_error "OpenAPI URI is required (-o/--openapi-uri)"
    show_usage
    exit 1
fi

# Check if Python script exists
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PARSER_SCRIPT="$SCRIPT_DIR/src/standalone_ingest_parser.py"

if [[ ! -f "$PARSER_SCRIPT" ]]; then
    print_error "Parser script not found: $PARSER_SCRIPT"
    exit 1
fi

# Check Python version
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is required but not found"
    exit 1
fi

PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
print_info "Using Python $PYTHON_VERSION"

# Check if dependencies are installed
print_info "Checking dependencies..."
if ! python3 -c "import requests, lxml, xmlschema, yaml" 2>/dev/null; then
    print_warning "Some dependencies may be missing. Install with:"
    print_warning "pip install -r requirements_standalone.txt"
fi

# Build command
CMD_ARGS=(
    "--service-name" "$SERVICE_NAME"
    "--wsdl-uri" "$WSDL_URI"
    "--openapi-uri" "$OPENAPI_URI"
    "--output-base-path" "$OUTPUT_PATH"
    "--log-level" "$LOG_LEVEL"
)

if [[ -n "$XSD_URI" ]]; then
    CMD_ARGS+=("--xsd-uri" "$XSD_URI")
fi

if [[ -n "$LOG_FILE" ]]; then
    CMD_ARGS+=("--log-file" "$LOG_FILE")
fi

# Print configuration
print_info "Configuration:"
echo "  Service Name: $SERVICE_NAME"
echo "  WSDL URI: $WSDL_URI"
echo "  XSD URI: ${XSD_URI:-"(not provided)"}"
echo "  OpenAPI URI: $OPENAPI_URI"
echo "  Output Path: $OUTPUT_PATH"
echo "  Log Level: $LOG_LEVEL"
echo "  Log File: ${LOG_FILE:-"(console only)"}"
echo

# Run the parser
print_info "Starting service definition parser..."
if python3 "$PARSER_SCRIPT" "${CMD_ARGS[@]}"; then
    print_success "Parser completed successfully!"
    print_info "Check output directory: $OUTPUT_PATH/$SERVICE_NAME"
    
    # Show latest files if they exist
    LATEST_DIR="$OUTPUT_PATH/$SERVICE_NAME/latest"
    if [[ -d "$LATEST_DIR" ]]; then
        print_info "Latest parsed files:"
        ls -la "$LATEST_DIR"
    fi
else
    print_error "Parser failed with exit code $?"
    exit 1
fi
