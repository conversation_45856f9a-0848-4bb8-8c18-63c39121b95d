# Standalone Service Definition Parser - Docker Compose
#
# This docker-compose file provides easy deployment of the standalone parser
# with volume mounting and environment configuration.
#
# Usage:
#   docker-compose up --build
#   docker-compose run parser --help
#   docker-compose run parser python src/standalone_ingest_parser.py --service-name test --wsdl-uri /app/data/test.wsdl --openapi-uri /app/data/test.yaml

version: '3.8'

services:
  parser:
    build:
      context: .
      dockerfile: Dockerfile
    image: standalone-parser:latest
    container_name: standalone-parser
    
    # Environment variables
    environment:
      - PYTHONPATH=/app/src
      - LOG_LEVEL=INFO
    
    # Volume mounts
    volumes:
      # Input data directory
      - ./data:/app/data:ro
      # Output directory
      - ./output:/app/output:rw
      # Logs directory
      - ./logs:/app/logs:rw
      # Optional: Mount sample data from shared directory
      - ../shared/sample_data:/app/sample_data:ro
    
    # Working directory
    working_dir: /app
    
    # Default command (can be overridden)
    command: ["python", "src/standalone_ingest_parser.py", "--help"]
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    
    # Restart policy
    restart: "no"
    
    # Network mode
    network_mode: bridge

  # Optional: Parser with sample data processing
  parser-sample:
    extends: parser
    container_name: standalone-parser-sample
    command: [
      "python", "src/standalone_ingest_parser.py",
      "--service-name", "sample_hello_service",
      "--wsdl-uri", "/app/sample_data/wsdl_input/test.wsdl",
      "--openapi-uri", "/app/sample_data/osdmp_target/test.yaml",
      "--output-base-path", "/app/output",
      "--log-level", "INFO"
    ]

  # Optional: Interactive shell for debugging
  parser-shell:
    extends: parser
    container_name: standalone-parser-shell
    command: ["bash"]
    stdin_open: true
    tty: true

# Named volumes for persistent data
volumes:
  parser_data:
    driver: local
  parser_output:
    driver: local
  parser_logs:
    driver: local

# Networks
networks:
  default:
    name: standalone-parser-network

# Usage Examples:
#
# 1. Build and run with help:
#    docker-compose up --build parser
#
# 2. Process sample data:
#    docker-compose run parser-sample
#
# 3. Interactive shell:
#    docker-compose run parser-shell
#
# 4. Custom processing:
#    docker-compose run parser python src/standalone_ingest_parser.py \
#      --service-name my_service \
#      --wsdl-uri /app/data/my_service.wsdl \
#      --openapi-uri /app/data/my_api.yaml \
#      --output-base-path /app/output \
#      --log-level DEBUG
#
# 5. Using the shell script:
#    docker-compose run parser ./run_parser.sh \
#      -s my_service \
#      -w /app/data/service.wsdl \
#      -o /app/data/api.yaml \
#      -p /app/output \
#      -l INFO
#
# 6. Batch processing multiple services:
#    docker-compose run parser bash -c "
#      for service in service1 service2 service3; do
#        python src/standalone_ingest_parser.py \
#          --service-name \$service \
#          --wsdl-uri /app/data/\$service.wsdl \
#          --openapi-uri /app/data/\$service.yaml \
#          --output-base-path /app/output
#      done
#    "
#
# 7. With persistent volumes:
#    docker-compose run -v parser_data:/app/data \
#                       -v parser_output:/app/output \
#                       -v parser_logs:/app/logs \
#                       parser python src/standalone_ingest_parser.py ...
#
# Directory structure for volume mounting:
# ./data/           # Input WSDL/OpenAPI files
# ./output/         # Parsed output files
# ./logs/           # Log files
# ./sample_data/    # Sample data (read-only)
