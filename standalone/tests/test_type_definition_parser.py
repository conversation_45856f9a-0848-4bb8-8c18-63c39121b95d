#!/usr/bin/env python3
"""
Test suite for type definition parser functionality.

Tests the extraction and normalization of type definitions from WSDL, XSD, and OpenAPI files.
"""

import unittest
import json
import os
import sys
from pathlib import Path

# Add src directory to path for imports
current_dir = Path(__file__).parent
src_dir = current_dir.parent / "src"
sys.path.insert(0, str(src_dir))

import type_definition_parser


class TestTypeDefinitionParser(unittest.TestCase):
    """Test cases for type definition extraction functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.sample_wsdl = """<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:tns="http://www.examples.com/wsdl/HelloService.wsdl"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             targetNamespace="http://www.examples.com/wsdl/HelloService.wsdl">

    <types>
        <xsd:schema targetNamespace="http://www.examples.com/wsdl/HelloService.wsdl">
            <xsd:complexType name="PersonType">
                <xsd:sequence>
                    <xsd:element name="firstName" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="lastName" type="xsd:string" minOccurs="1" maxOccurs="1"/>
                    <xsd:element name="age" type="xsd:int" minOccurs="0" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:complexType>
            
            <xsd:simpleType name="StatusType">
                <xsd:restriction base="xsd:string">
                    <xsd:enumeration value="active"/>
                    <xsd:enumeration value="inactive"/>
                    <xsd:enumeration value="pending"/>
                </xsd:restriction>
            </xsd:simpleType>
            
            <xsd:element name="Person" type="tns:PersonType"/>
        </xsd:schema>
    </types>

    <message name="SayHelloRequest">
        <part name="person" element="tns:Person"/>
    </message>

    <message name="SayHelloResponse">
        <part name="greeting" type="xsd:string"/>
    </message>

    <portType name="Hello_PortType">
        <operation name="sayHello">
            <input message="tns:SayHelloRequest"/>
            <output message="tns:SayHelloResponse"/>
        </operation>
    </portType>

    <binding name="Hello_Binding" type="tns:Hello_PortType">
        <soap:binding style="rpc" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="sayHello">
            <soap:operation soapAction="sayHello"/>
            <input><soap:body use="literal"/></input>
            <output><soap:body use="literal"/></output>
        </operation>
    </binding>

    <service name="Hello_Service">
        <port name="Hello_Port" binding="tns:Hello_Binding">
            <soap:address location="http://www.examples.com/SayHello/"/>
        </port>
    </service>

</definitions>"""

        self.sample_xsd = """<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://example.com/types"
           xmlns:tns="http://example.com/types">

    <xs:complexType name="AddressType">
        <xs:sequence>
            <xs:element name="street" type="xs:string"/>
            <xs:element name="city" type="xs:string"/>
            <xs:element name="zipCode" type="tns:ZipCodeType"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="ZipCodeType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{5}(-[0-9]{4})?"/>
            <xs:minLength value="5"/>
            <xs:maxLength value="10"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="Address" type="tns:AddressType"/>

</xs:schema>"""

        self.sample_openapi = """{
  "openapi": "3.0.0",
  "info": {
    "title": "Test API",
    "version": "1.0.0",
    "description": "A test API for type definition extraction"
  },
  "paths": {
    "/users": {
      "get": {
        "summary": "Get users",
        "responses": {
          "200": {
            "description": "List of users",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/User"
                  }
                }
              }
            }
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "User": {
        "type": "object",
        "required": ["id", "name"],
        "properties": {
          "id": {
            "type": "integer",
            "format": "int64",
            "description": "User ID"
          },
          "name": {
            "type": "string",
            "minLength": 1,
            "maxLength": 100,
            "description": "User name"
          },
          "email": {
            "type": "string",
            "format": "email",
            "description": "User email address"
          },
          "status": {
            "type": "string",
            "enum": ["active", "inactive", "pending"],
            "description": "User status"
          }
        }
      },
      "Error": {
        "type": "object",
        "required": ["code", "message"],
        "properties": {
          "code": {
            "type": "integer",
            "minimum": 100,
            "maximum": 599
          },
          "message": {
            "type": "string"
          }
        }
      }
    },
    "parameters": {
      "UserIdParam": {
        "name": "userId",
        "in": "path",
        "required": true,
        "description": "User identifier",
        "schema": {
          "type": "integer",
          "format": "int64"
        }
      }
    }
  }
}"""

    def test_extract_wsdl_types(self):
        """Test WSDL type definition extraction."""
        result = type_definition_parser.extract_wsdl_types(self.sample_wsdl)
        
        # Check basic structure
        self.assertEqual(result["source_format"], "wsdl")
        self.assertEqual(result["target_namespace"], "http://www.examples.com/wsdl/HelloService.wsdl")
        self.assertIn("types", result)
        self.assertIn("metadata", result)
        
        # Check metadata
        self.assertIn("extraction_timestamp", result["metadata"])
        self.assertIn("total_types", result["metadata"])
        self.assertIn("namespaces", result["metadata"])
        
        # Check extracted types
        types = result["types"]
        self.assertGreater(len(types), 0)
        
        # Find specific types
        person_type = next((t for t in types if t["name"] == "PersonType"), None)
        self.assertIsNotNone(person_type)
        self.assertEqual(person_type["category"], "complex")
        self.assertGreater(len(person_type["properties"]), 0)
        
        status_type = next((t for t in types if t["name"] == "StatusType"), None)
        self.assertIsNotNone(status_type)
        self.assertEqual(status_type["category"], "simple")
        self.assertGreater(len(status_type["restrictions"]), 0)

    def test_extract_xsd_types(self):
        """Test XSD type definition extraction."""
        result = type_definition_parser.extract_xsd_types(self.sample_xsd)
        
        # Check basic structure
        self.assertEqual(result["source_format"], "xsd")
        self.assertEqual(result["target_namespace"], "http://example.com/types")
        self.assertIn("types", result)
        self.assertIn("metadata", result)
        
        # Check extracted types
        types = result["types"]
        self.assertGreater(len(types), 0)
        
        # Find specific types
        address_type = next((t for t in types if t["name"] == "AddressType"), None)
        self.assertIsNotNone(address_type)
        self.assertEqual(address_type["category"], "complex")
        
        zipcode_type = next((t for t in types if t["name"] == "ZipCodeType"), None)
        self.assertIsNotNone(zipcode_type)
        self.assertEqual(zipcode_type["category"], "simple")

    def test_extract_openapi_types(self):
        """Test OpenAPI type definition extraction."""
        result = type_definition_parser.extract_openapi_types(self.sample_openapi)
        
        # Check basic structure
        self.assertEqual(result["source_format"], "openapi")
        self.assertEqual(result["target_namespace"], "Test API")
        self.assertIn("types", result)
        self.assertIn("metadata", result)
        
        # Check metadata
        self.assertEqual(result["metadata"]["openapi_version"], "3.0.0")
        self.assertIn("api_info", result["metadata"])
        
        # Check extracted types
        types = result["types"]
        self.assertGreater(len(types), 0)
        
        # Find specific types
        user_type = next((t for t in types if t["name"] == "User"), None)
        self.assertIsNotNone(user_type)
        self.assertEqual(user_type["category"], "schema")
        self.assertGreater(len(user_type["properties"]), 0)
        
        # Check required fields
        required_props = [p for p in user_type["properties"] if p["required"]]
        self.assertGreater(len(required_props), 0)
        
        # Find parameter type
        param_type = next((t for t in types if t["name"] == "UserIdParam"), None)
        self.assertIsNotNone(param_type)
        self.assertEqual(param_type["category"], "parameter")

    def test_error_handling(self):
        """Test error handling for invalid inputs."""
        # Test invalid WSDL
        result = type_definition_parser.extract_wsdl_types("invalid xml")
        self.assertEqual(result["source_format"], "wsdl")
        self.assertEqual(len(result["types"]), 0)
        self.assertIn("error", result["metadata"])
        
        # Test invalid XSD
        result = type_definition_parser.extract_xsd_types("invalid xml")
        self.assertEqual(result["source_format"], "xsd")
        self.assertEqual(len(result["types"]), 0)
        self.assertIn("error", result["metadata"])
        
        # Test invalid OpenAPI
        result = type_definition_parser.extract_openapi_types("invalid json")
        self.assertEqual(result["source_format"], "openapi")
        self.assertEqual(len(result["types"]), 0)
        self.assertIn("error", result["metadata"])


if __name__ == "__main__":
    unittest.main()
