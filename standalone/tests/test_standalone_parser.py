#!/usr/bin/env python3
"""
Test script for the standalone service definition parser.

This script tests the basic functionality of the standalone parser
using sample data if available, or creates minimal test data.

Usage:
    python test_standalone_parser.py
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Sample WSDL content for testing
SAMPLE_WSDL = '''<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:tns="http://example.com/test"
             targetNamespace="http://example.com/test">
    
    <types>
        <schema xmlns="http://www.w3.org/2001/XMLSchema"
                targetNamespace="http://example.com/test">
            <element name="TestRequest">
                <complexType>
                    <sequence>
                        <element name="message" type="string"/>
                    </sequence>
                </complexType>
            </element>
            <element name="TestResponse">
                <complexType>
                    <sequence>
                        <element name="result" type="string"/>
                    </sequence>
                </complexType>
            </element>
        </schema>
    </types>
    
    <message name="TestRequestMessage">
        <part name="parameters" element="tns:TestRequest"/>
    </message>
    
    <message name="TestResponseMessage">
        <part name="parameters" element="tns:TestResponse"/>
    </message>
    
    <portType name="TestPortType">
        <operation name="TestOperation">
            <input message="tns:TestRequestMessage"/>
            <output message="tns:TestResponseMessage"/>
        </operation>
    </portType>
    
    <binding name="TestBinding" type="tns:TestPortType">
        <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="TestOperation">
            <soap:operation soapAction="test"/>
            <input><soap:body use="literal"/></input>
            <output><soap:body use="literal"/></output>
        </operation>
    </binding>
    
    <service name="TestService">
        <port name="TestPort" binding="tns:TestBinding">
            <soap:address location="http://example.com/test"/>
        </port>
    </service>
</definitions>'''

# Sample OpenAPI content for testing
SAMPLE_OPENAPI = '''openapi: 3.0.0
info:
  title: Test API
  version: 1.0.0
  description: A simple test API for the standalone parser
servers:
  - url: https://api.example.com/v1
paths:
  /test:
    get:
      summary: Get test data
      operationId: getTest
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  timestamp:
                    type: string
                    format: date-time
    post:
      summary: Create test data
      operationId: createTest
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
              required:
                - message
      responses:
        '201':
          description: Created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  message:
                    type: string
components:
  schemas:
    TestObject:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        active:
          type: boolean
      required:
        - id
        - name'''

def create_test_files(temp_dir: str) -> tuple:
    """Create temporary test files"""
    wsdl_path = os.path.join(temp_dir, "test_service.wsdl")
    openapi_path = os.path.join(temp_dir, "test_api.yaml")
    
    with open(wsdl_path, 'w', encoding='utf-8') as f:
        f.write(SAMPLE_WSDL)
    
    with open(openapi_path, 'w', encoding='utf-8') as f:
        f.write(SAMPLE_OPENAPI)
    
    return wsdl_path, openapi_path

def test_standalone_parser():
    """Test the standalone parser with sample data"""
    print("Testing Standalone Service Definition Parser")
    print("=" * 50)
    
    # Create temporary directory for test files
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"Using temporary directory: {temp_dir}")
        
        # Create test files
        wsdl_path, openapi_path = create_test_files(temp_dir)
        print(f"Created test WSDL: {wsdl_path}")
        print(f"Created test OpenAPI: {openapi_path}")
        
        # Create output directory
        output_dir = os.path.join(temp_dir, "output")
        os.makedirs(output_dir, exist_ok=True)
        
        # Import and test the parser
        try:
            # Add src directory to path
            src_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'src')
            sys.path.insert(0, src_dir)
            from standalone_ingest_parser import StandaloneIngestParser
            
            # Create parser instance
            catalog_path = os.path.join(temp_dir, "test_catalog.json")
            parser = StandaloneIngestParser(catalog_path)
            
            # Test the parser
            print("\nTesting parser functionality...")
            result = parser.process_service(
                service_name="test_service",
                wsdl_uri=wsdl_path,
                xsd_uri=None,
                openapi_uri=openapi_path,
                output_base_path=output_dir
            )
            
            if result["success"]:
                print("✅ Parser test PASSED!")
                print(f"Output directory: {result['parsed_outputs']['version_dir']}")
                print(f"Files created: {list(result['parsed_outputs']['files'].keys())}")
                
                # Check if files were actually created
                version_dir = result['parsed_outputs']['version_dir']
                expected_files = ['parsed_wsdl.json', 'parsed_openapi.json', 'manifest.json']
                
                for filename in expected_files:
                    filepath = os.path.join(version_dir, filename)
                    if os.path.exists(filepath):
                        print(f"✅ {filename} created successfully")
                    else:
                        print(f"❌ {filename} NOT found")
                
                # Check catalog
                if os.path.exists(catalog_path):
                    print("✅ Service catalog created successfully")
                else:
                    print("❌ Service catalog NOT found")
                
                return True
            else:
                print(f"❌ Parser test FAILED: {result['error']}")
                return False
                
        except ImportError as e:
            print(f"❌ Import error: {e}")
            print("Make sure all dependencies are installed:")
            print("pip install -r requirements_standalone.txt")
            return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False

def test_command_line():
    """Test the command line interface"""
    print("\nTesting Command Line Interface")
    print("=" * 50)
    
    # Create temporary directory for test files
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test files
        wsdl_path, openapi_path = create_test_files(temp_dir)
        output_dir = os.path.join(temp_dir, "cli_output")
        
        # Build command
        script_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "src", "standalone_ingest_parser.py")
        cmd = [
            sys.executable, script_path,
            "--service-name", "cli_test_service",
            "--wsdl-uri", wsdl_path,
            "--openapi-uri", openapi_path,
            "--output-base-path", output_dir,
            "--log-level", "INFO"
        ]
        
        print(f"Running command: {' '.join(cmd)}")
        
        try:
            import subprocess
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ Command line test PASSED!")
                print("STDOUT:", result.stdout[-500:] if len(result.stdout) > 500 else result.stdout)
                return True
            else:
                print(f"❌ Command line test FAILED (exit code: {result.returncode})")
                print("STDERR:", result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Command line test TIMED OUT")
            return False
        except Exception as e:
            print(f"❌ Command line test ERROR: {e}")
            return False

def main():
    """Main test function"""
    print("Standalone Service Definition Parser - Test Suite")
    print("=" * 60)
    
    # Test 1: Direct parser functionality
    test1_passed = test_standalone_parser()
    
    # Test 2: Command line interface
    test2_passed = test_command_line()
    
    # Summary
    print("\nTest Summary")
    print("=" * 20)
    print(f"Parser functionality: {'PASSED' if test1_passed else 'FAILED'}")
    print(f"Command line interface: {'PASSED' if test2_passed else 'FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests PASSED! The standalone parser is working correctly.")
        return 0
    else:
        print("\n❌ Some tests FAILED. Please check the error messages above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
