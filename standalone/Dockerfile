# Standalone Service Definition Parser - Docker Image
# 
# This Dockerfile creates a lightweight container for the standalone parser
# with volume mounting support for data processing.
#
# Build: docker build -t standalone-parser:latest .
# Run:   docker run -v /host/data:/app/data standalone-parser:latest

FROM python:3.11-slim

# Set metadata
LABEL maintainer="Airflow LLM Service Mapper Team"
LABEL description="Standalone Service Definition Parser"
LABEL version="1.0.0"

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app/src

# Create app user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copy source code
COPY src/ ./src/
COPY run_parser.sh ./
COPY examples/ ./examples/

# Create necessary directories with proper permissions
RUN mkdir -p /app/data /app/logs /app/output && \
    chown -R appuser:appuser /app && \
    chmod +x /app/run_parser.sh

# Create volume mount points
VOLUME ["/app/data", "/app/output", "/app/logs"]

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python src/standalone_ingest_parser.py --help > /dev/null || exit 1

# Default command
CMD ["python", "src/standalone_ingest_parser.py", "--help"]

# Usage examples (as comments):
#
# 1. Basic usage with volume mounts:
#    docker run -v /host/input:/app/data \
#               -v /host/output:/app/output \
#               standalone-parser:latest \
#               python src/standalone_ingest_parser.py \
#               --service-name my_service \
#               --wsdl-uri /app/data/service.wsdl \
#               --openapi-uri /app/data/api.yaml \
#               --output-base-path /app/output
#
# 2. Interactive mode:
#    docker run -it -v /host/data:/app/data standalone-parser:latest bash
#
# 3. Using the convenience script:
#    docker run -v /host/data:/app/data \
#               -v /host/output:/app/output \
#               standalone-parser:latest \
#               ./run_parser.sh -s my_service \
#                              -w /app/data/service.wsdl \
#                              -o /app/data/api.yaml \
#                              -p /app/output
#
# 4. With logging:
#    docker run -v /host/input:/app/data \
#               -v /host/output:/app/output \
#               -v /host/logs:/app/logs \
#               standalone-parser:latest \
#               python src/standalone_ingest_parser.py \
#               --service-name my_service \
#               --wsdl-uri /app/data/service.wsdl \
#               --openapi-uri /app/data/api.yaml \
#               --output-base-path /app/output \
#               --log-file /app/logs/parser.log \
#               --log-level DEBUG
