# standalone_common/validation_utils.py
import json
from typing import Dict, Any, List, Optional

def validate_parsed_data_schema(data: dict, expected_schema_type: str) -> bool:
    """
    Validates if parsed data conforms to an expected basic structure.

    Args:
        data: The parsed data to validate
        expected_schema_type: Type of schema to validate against ("wsdl_parsed", "xsd_parsed", "openapi_parsed")

    Returns:
        bool: True if validation passes, False otherwise
    """
    print(f"Validating schema for type: {expected_schema_type}...")
    
    if not isinstance(data, dict):
        print(f"Error: Expected dict, got {type(data)}")
        return False
    
    if expected_schema_type == "wsdl_parsed":
        # Check for required WSDL structure
        required_keys = ["services", "operations", "messages", "types"]
        for key in required_keys:
            if key not in data:
                print(f"Error: Missing required key '{key}' in WSDL data")
                return False
        
        # Check that operations is a list and has at least one operation
        if not isinstance(data["operations"], list):
            print("Error: 'operations' should be a list")
            return False
        
        if len(data["operations"]) == 0:
            print("Warning: No operations found in WSDL")
        
        print(f"WSDL validation passed. Found {len(data['operations'])} operations")
        return True
    
    elif expected_schema_type == "xsd_parsed":
        # Check for required XSD structure
        required_keys = ["elements", "complex_types", "simple_types"]
        for key in required_keys:
            if key not in data:
                print(f"Error: Missing required key '{key}' in XSD data")
                return False
        
        print(f"XSD validation passed. Found {len(data['elements'])} elements, {len(data['complex_types'])} complex types")
        return True
    
    elif expected_schema_type == "openapi_parsed":
        # Check for required OpenAPI structure
        if "extracted" not in data:
            print("Error: Missing 'extracted' section in OpenAPI data")
            return False
        
        extracted = data["extracted"]
        if "paths" not in extracted:
            print("Error: Missing 'paths' in extracted OpenAPI data")
            return False
        
        if not isinstance(extracted["paths"], dict):
            print("Error: 'paths' should be a dict")
            return False
        
        path_count = len(extracted["paths"])
        print(f"OpenAPI validation passed. Found {path_count} paths")
        return True
    
    else:
        print(f"Error: Unknown schema type '{expected_schema_type}'")
        return False

def validate_service_definition_completeness(parsed_data: dict) -> dict:
    """
    Validates the completeness of parsed service definitions.

    Args:
        parsed_data: Dictionary containing parsed service definitions

    Returns:
        dict: Validation report with completeness metrics
    """
    report = {
        "completeness_score": 0.0,
        "missing_elements": [],
        "warnings": [],
        "recommendations": []
    }
    
    total_checks = 0
    passed_checks = 0
    
    # Check for WSDL data
    if "wsdl" in parsed_data:
        total_checks += 1
        wsdl_data = parsed_data["wsdl"]
        if isinstance(wsdl_data, dict) and "operations" in wsdl_data:
            passed_checks += 1
        else:
            report["missing_elements"].append("Valid WSDL operations")
    
    # Check for OpenAPI data
    if "openapi" in parsed_data:
        total_checks += 1
        openapi_data = parsed_data["openapi"]
        if isinstance(openapi_data, dict) and "extracted" in openapi_data:
            passed_checks += 1
        else:
            report["missing_elements"].append("Valid OpenAPI specification")
    
    # Check for XSD data (optional)
    if "xsd" in parsed_data:
        total_checks += 1
        xsd_data = parsed_data["xsd"]
        if isinstance(xsd_data, dict) and "elements" in xsd_data:
            passed_checks += 1
        else:
            report["warnings"].append("XSD data present but invalid")
    
    # Calculate completeness score
    if total_checks > 0:
        report["completeness_score"] = passed_checks / total_checks
    
    # Add recommendations based on completeness
    if report["completeness_score"] < 0.5:
        report["recommendations"].append("Consider providing more complete service definitions")
    
    if len(report["missing_elements"]) > 0:
        report["recommendations"].append("Ensure all required definition files are valid and accessible")
    
    return report

def detect_mapping_deviations(mapped_output: dict, golden_dataset_stats_path: str = None) -> dict:
    """
    Detects deviations in the final mapped output compared to expected patterns or a golden dataset.

    Args:
        mapped_output: The mapping output to validate
        golden_dataset_stats_path: Optional path to golden dataset statistics for comparison

    Returns:
        dict: Report of deviations found
    """
    deviations_report = {"issues_found": 0, "warnings": [], "errors": []}
    print("Detecting deviations in mapped output...")

    # Check for required sections in mapped output
    if not mapped_output.get("final_openapi_spec"):
        deviations_report["errors"].append("Final OpenAPI spec is missing.")
        deviations_report["issues_found"] += 1

    if not mapped_output.get("mapping_metadata"):
        deviations_report["warnings"].append("Mapping metadata is missing.")
        deviations_report["issues_found"] += 1

    # Check for empty or minimal mappings
    final_spec = mapped_output.get("final_openapi_spec", {})
    if isinstance(final_spec, dict):
        paths = final_spec.get("paths", {})
        if len(paths) == 0:
            deviations_report["errors"].append("No paths found in final OpenAPI spec.")
            deviations_report["issues_found"] += 1
        elif len(paths) < 3:
            deviations_report["warnings"].append(f"Only {len(paths)} paths found - this seems low.")

    # If golden dataset stats are provided, compare against them
    if golden_dataset_stats_path and os.path.exists(golden_dataset_stats_path):
        try:
            with open(golden_dataset_stats_path, 'r') as f:
                golden_stats = json.load(f)
            
            # Compare key metrics
            current_path_count = len(final_spec.get("paths", {}))
            expected_path_count = golden_stats.get("average_path_count", 0)
            
            if current_path_count < expected_path_count * 0.5:
                deviations_report["warnings"].append(
                    f"Path count ({current_path_count}) is significantly lower than expected ({expected_path_count})"
                )
        except Exception as e:
            deviations_report["warnings"].append(f"Could not load golden dataset stats: {e}")

    print(f"Deviation detection complete. Found {deviations_report['issues_found']} issues.")
    return deviations_report

def validate_training_data_quality(training_data: list) -> dict:
    """
    Validates the quality of generated training data.

    Args:
        training_data: List of training examples

    Returns:
        dict: Quality assessment report
    """
    report = {
        "total_examples": len(training_data),
        "quality_score": 0.0,
        "issues": [],
        "recommendations": []
    }
    
    if not training_data:
        report["issues"].append("No training data provided")
        return report
    
    # Check for required fields in each example
    required_fields = ["input", "output", "metadata"]
    valid_examples = 0
    
    for i, example in enumerate(training_data):
        if not isinstance(example, dict):
            report["issues"].append(f"Example {i} is not a dictionary")
            continue
        
        missing_fields = [field for field in required_fields if field not in example]
        if missing_fields:
            report["issues"].append(f"Example {i} missing fields: {missing_fields}")
            continue
        
        # Check for empty values
        if not example.get("input") or not example.get("output"):
            report["issues"].append(f"Example {i} has empty input or output")
            continue
        
        valid_examples += 1
    
    # Calculate quality score
    if len(training_data) > 0:
        report["quality_score"] = valid_examples / len(training_data)
    
    # Add recommendations
    if report["quality_score"] < 0.8:
        report["recommendations"].append("Consider improving training data quality")
    
    if len(training_data) < 50:
        report["recommendations"].append("Consider generating more training examples for better model performance")
    
    return report
