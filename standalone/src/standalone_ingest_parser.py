#!/usr/bin/env python3
"""
Standalone Service Definition Ingestion and Parser

This script provides the same functionality as the Airflow DAG 01_ingest_and_parse_definitions_dag.py
but as a standalone Python executable. It fetches service definitions from various sources,
parses them into structured formats, and stores the results.

Usage:
    python standalone_ingest_parser.py --service-name example_service \
                                      --wsdl-uri path/to/service.wsdl \
                                      --openapi-uri path/to/api.yaml \
                                      --output-base-path data/parsed_definitions

Author: Airflow LLM Service Mapper Team (Standalone Version)
Version: 1.0.0
"""

import argparse
import json
import logging
import os
import re
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import requests

# Import standalone common modules
# Add current directory to path for local imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import local modules
import constants
import data_parsers
import validation_utils
import type_definition_parser

# Import Java source code parser
try:
    import source_code_parser
except ImportError:
    source_code_parser = None


class StandaloneServiceCatalog:
    """Manages service catalog using JSON file instead of Airflow Variables"""
    
    def __init__(self, catalog_path: str = "service_catalog.json"):
        self.catalog_path = catalog_path
    
    def get_catalog(self) -> Dict[str, Any]:
        """Get the service catalog from JSON file"""
        if os.path.exists(self.catalog_path):
            try:
                with open(self.catalog_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                logging.warning(f"Error reading catalog file {self.catalog_path}: {e}")
        
        return {"services": {}, "last_updated": None}
    
    def save_catalog(self, catalog: Dict[str, Any]) -> None:
        """Save the service catalog to JSON file"""
        try:
            with open(self.catalog_path, 'w', encoding='utf-8') as f:
                json.dump(catalog, f, indent=2, ensure_ascii=False)
        except IOError as e:
            logging.error(f"Error saving catalog file {self.catalog_path}: {e}")
            raise


class StandaloneIngestParser:
    """Main class for standalone service definition ingestion and parsing"""
    
    def __init__(self, catalog_path: str = "service_catalog.json"):
        self.catalog = StandaloneServiceCatalog(catalog_path)
        self.logger = logging.getLogger(__name__)
    
    def validate_parameters(self, service_name: str, wsdl_uri: str,
                          xsd_uri: Optional[str], openapi_uri: str,
                          output_base_path: str, java_source_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Validate all input parameters before processing.

        Args:
            service_name: Unique service identifier
            wsdl_uri: Path or URL to WSDL file
            xsd_uri: Optional path or URL to XSD file
            openapi_uri: Path or URL to OpenAPI file
            output_base_path: Base directory for outputs
            java_source_dir: Optional path to directory containing Java source files

        Returns:
            Dict containing validated parameters

        Raises:
            ValueError: If any parameter validation fails
        """
        self.logger.info(f"Validating parameters for service: {service_name}")
        
        # Validate service name format
        if not re.match(r"^[a-zA-Z0-9_-]+$", service_name):
            raise ValueError(
                f"Invalid service_name '{service_name}'. Must contain only alphanumeric characters, underscores, and hyphens."
            )
        
        # Validate required URIs are not empty
        if not wsdl_uri.strip():
            raise ValueError("wsdl_uri cannot be empty")
        if not openapi_uri.strip():
            raise ValueError("openapi_uri cannot be empty")
        if not output_base_path.strip():
            raise ValueError("output_base_path cannot be empty")
        
        # Check if files exist for local paths
        for uri_name, uri_value in [("wsdl_uri", wsdl_uri), ("openapi_uri", openapi_uri)]:
            if uri_value and not uri_value.startswith(("http://", "https://", "s3://", "gs://")):
                if not os.path.exists(uri_value):
                    raise ValueError(f"Local file not found: {uri_name}={uri_value}")
        
        if xsd_uri and not xsd_uri.startswith(("http://", "https://", "s3://", "gs://")):
            if not os.path.exists(xsd_uri):
                raise ValueError(f"Local XSD file not found: {xsd_uri}")

        if java_source_dir and not os.path.isdir(java_source_dir):
            raise ValueError(f"Java source directory not found: {java_source_dir}")

        self.logger.info("All parameters validated successfully")
        return {
            "service_name": service_name,
            "wsdl_uri": wsdl_uri,
            "xsd_uri": xsd_uri,
            "openapi_uri": openapi_uri,
            "output_base_path": output_base_path,
            "java_source_dir": java_source_dir,
            "validation_timestamp": datetime.now().isoformat()
        }
    
    def fetch_and_parse_definitions(self, validated_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fetch all service definitions, parse them, and store the results.
        
        Args:
            validated_params: Dictionary containing validated input parameters
        
        Returns:
            Dict containing metadata about parsed files and their locations
        
        Raises:
            Exception: If any parsing or storage operation fails
        """
        service_name = validated_params["service_name"]
        wsdl_uri = validated_params["wsdl_uri"]
        xsd_uri = validated_params["xsd_uri"]
        openapi_uri = validated_params["openapi_uri"]
        output_base_path = validated_params["output_base_path"]
        java_source_dir = validated_params.get("java_source_dir")
        
        self.logger.info(f"Starting definition parsing for service: {service_name}")
        
        # Create timestamp for versioning
        timestamp = int(time.time())
        version_dir = f"{output_base_path}/{service_name}/v{timestamp}"
        
        try:
            os.makedirs(version_dir, exist_ok=True)
            self.logger.info(f"Created output directory: {version_dir}")
        except OSError as e:
            raise Exception(f"Failed to create output directory {version_dir}: {str(e)}")
        
        parsed_outputs = {
            "service_name": service_name,
            "timestamp": timestamp,
            "version_dir": version_dir,
            "files": {},
            "processing_start": datetime.now().isoformat()
        }
        
        # WSDL Processing
        try:
            self.logger.info(f"Processing WSDL from: {wsdl_uri}")
            
            # Read WSDL content (supports local files and URLs)
            if wsdl_uri.startswith(("http://", "https://")):
                response = requests.get(wsdl_uri, timeout=30)
                response.raise_for_status()
                wsdl_content = response.text
            else:
                with open(wsdl_uri, 'r', encoding='utf-8') as f:
                    wsdl_content = f.read()
            
            # Parse WSDL content
            parsed_wsdl = data_parsers.parse_wsdl_content(wsdl_content)

            # Validate parsed data
            if not validation_utils.validate_parsed_data_schema(parsed_wsdl, "wsdl_parsed"):
                raise Exception("Parsed WSDL data does not conform to expected schema")

            # Store parsed WSDL
            wsdl_output_path = f"{version_dir}/parsed_wsdl.json"
            with open(wsdl_output_path, 'w', encoding='utf-8') as f:
                json.dump(parsed_wsdl, f, indent=2, ensure_ascii=False)

            parsed_outputs["files"]["wsdl"] = wsdl_output_path
            self.logger.info(f"WSDL successfully parsed and stored at: {wsdl_output_path}")

            # Extract and store WSDL type definitions
            try:
                wsdl_types = type_definition_parser.extract_wsdl_types(wsdl_content)
                wsdl_types_path = f"{version_dir}/parsed_wsdl_types.json"
                with open(wsdl_types_path, 'w', encoding='utf-8') as f:
                    json.dump(wsdl_types, f, indent=2, ensure_ascii=False)

                parsed_outputs["files"]["wsdl_types"] = wsdl_types_path
                self.logger.info(f"WSDL type definitions extracted and stored at: {wsdl_types_path}")
                self.logger.info(f"Extracted {wsdl_types['metadata']['total_types']} type definitions from WSDL")
            except Exception as e:
                self.logger.warning(f"Failed to extract WSDL type definitions: {str(e)}")
                parsed_outputs["files"]["wsdl_types_error"] = str(e)
            
        except Exception as e:
            error_msg = f"Error processing WSDL from {wsdl_uri}: {str(e)}"
            self.logger.error(error_msg)
            raise Exception(error_msg)
        
        # XSD Processing (if provided)
        if xsd_uri:
            try:
                self.logger.info(f"Processing XSD from: {xsd_uri}")

                if xsd_uri.startswith(("http://", "https://")):
                    response = requests.get(xsd_uri, timeout=30)
                    response.raise_for_status()
                    xsd_content = response.text
                else:
                    with open(xsd_uri, 'r', encoding='utf-8') as f:
                        xsd_content = f.read()

                parsed_xsd = data_parsers.parse_xsd_content(xsd_content)

                # Store parsed XSD
                xsd_output_path = f"{version_dir}/parsed_xsd.json"
                with open(xsd_output_path, 'w', encoding='utf-8') as f:
                    json.dump(parsed_xsd, f, indent=2, ensure_ascii=False)

                parsed_outputs["files"]["xsd"] = xsd_output_path
                self.logger.info(f"XSD successfully parsed and stored at: {xsd_output_path}")

                # Extract and store XSD type definitions
                try:
                    xsd_types = type_definition_parser.extract_xsd_types(xsd_content)
                    xsd_types_path = f"{version_dir}/parsed_xsd_types.json"
                    with open(xsd_types_path, 'w', encoding='utf-8') as f:
                        json.dump(xsd_types, f, indent=2, ensure_ascii=False)

                    parsed_outputs["files"]["xsd_types"] = xsd_types_path
                    self.logger.info(f"XSD type definitions extracted and stored at: {xsd_types_path}")
                    self.logger.info(f"Extracted {xsd_types['metadata']['total_types']} type definitions from XSD")
                except Exception as e:
                    self.logger.warning(f"Failed to extract XSD type definitions: {str(e)}")
                    parsed_outputs["files"]["xsd_types_error"] = str(e)

            except Exception as e:
                error_msg = f"Error processing XSD from {xsd_uri}: {str(e)}"
                self.logger.warning(error_msg)
                parsed_outputs["files"]["xsd_error"] = str(e)

        # OpenAPI Processing
        try:
            self.logger.info(f"Processing OpenAPI from: {openapi_uri}")

            if openapi_uri.startswith(("http://", "https://")):
                response = requests.get(openapi_uri, timeout=30)
                response.raise_for_status()
                openapi_content = response.text
            else:
                with open(openapi_uri, 'r', encoding='utf-8') as f:
                    openapi_content = f.read()

            parsed_openapi = data_parsers.parse_openapi_content(openapi_content)

            # Validate parsed data
            if not validation_utils.validate_parsed_data_schema(parsed_openapi, "openapi_parsed"):
                raise Exception("Parsed OpenAPI data does not conform to expected schema")

            # Store parsed OpenAPI
            openapi_output_path = f"{version_dir}/parsed_openapi.json"
            with open(openapi_output_path, 'w', encoding='utf-8') as f:
                json.dump(parsed_openapi, f, indent=2, ensure_ascii=False)

            parsed_outputs["files"]["openapi"] = openapi_output_path
            self.logger.info(f"OpenAPI successfully parsed and stored at: {openapi_output_path}")

            # Extract and store OpenAPI type definitions
            try:
                openapi_types = type_definition_parser.extract_openapi_types(openapi_content)
                openapi_types_path = f"{version_dir}/parsed_openapi_types.json"
                with open(openapi_types_path, 'w', encoding='utf-8') as f:
                    json.dump(openapi_types, f, indent=2, ensure_ascii=False)

                parsed_outputs["files"]["openapi_types"] = openapi_types_path
                self.logger.info(f"OpenAPI type definitions extracted and stored at: {openapi_types_path}")
                self.logger.info(f"Extracted {openapi_types['metadata']['total_types']} type definitions from OpenAPI")
            except Exception as e:
                self.logger.warning(f"Failed to extract OpenAPI type definitions: {str(e)}")
                parsed_outputs["files"]["openapi_types_error"] = str(e)

        except Exception as e:
            error_msg = f"Error processing OpenAPI from {openapi_uri}: {str(e)}"
            self.logger.error(error_msg)
            parsed_outputs["files"]["openapi_error"] = str(e)

        # Java Source Processing (if provided)
        if java_source_dir:
            if source_code_parser is None:
                self.logger.warning("Java source processing requested but source_code_parser not available")
                parsed_outputs["files"]["java_source_error"] = "source_code_parser module not available"
            else:
                try:
                    self.logger.info(f"Processing Java source files from: {java_source_dir}")

                    # Find all .java files in the directory
                    java_files = []
                    for root, dirs, files in os.walk(java_source_dir):
                        for file in files:
                            if file.endswith('.java'):
                                java_files.append(os.path.join(root, file))

                    if not java_files:
                        self.logger.warning(f"No Java files found in directory: {java_source_dir}")
                    else:
                        # Parse all Java files and combine results
                        combined_java_result = {
                            "source_type": "java",
                            "files_processed": [],
                            "web_services": [],
                            "operations": [],
                            "data_types": []
                        }

                        for java_file in java_files:
                            try:
                                with open(java_file, 'r', encoding='utf-8') as f:
                                    java_content = f.read()

                                # Parse individual Java file
                                java_result = source_code_parser.parse_java_source(java_content)

                                # Add file info
                                file_info = {
                                    "file_path": java_file,
                                    "relative_path": os.path.relpath(java_file, java_source_dir),
                                    "web_services_count": len(java_result["web_services"]),
                                    "operations_count": len(java_result["operations"]),
                                    "data_types_count": len(java_result["data_types"])
                                }
                                combined_java_result["files_processed"].append(file_info)

                                # Merge results
                                combined_java_result["web_services"].extend(java_result["web_services"])
                                combined_java_result["operations"].extend(java_result["operations"])
                                combined_java_result["data_types"].extend(java_result["data_types"])

                                self.logger.info(f"Processed Java file: {os.path.basename(java_file)} "
                                               f"({len(java_result['web_services'])} services, "
                                               f"{len(java_result['operations'])} operations, "
                                               f"{len(java_result['data_types'])} data types)")

                            except Exception as e:
                                self.logger.warning(f"Failed to parse Java file {java_file}: {str(e)}")
                                continue

                        # Store parsed Java source
                        java_output_path = f"{version_dir}/parsed_java_source.json"
                        with open(java_output_path, 'w', encoding='utf-8') as f:
                            json.dump(combined_java_result, f, indent=2, ensure_ascii=False)

                        parsed_outputs["files"]["java_source"] = java_output_path
                        self.logger.info(f"Java source successfully parsed and stored at: {java_output_path}")
                        self.logger.info(f"Processed {len(combined_java_result['files_processed'])} Java files: "
                                       f"{len(combined_java_result['web_services'])} services, "
                                       f"{len(combined_java_result['operations'])} operations, "
                                       f"{len(combined_java_result['data_types'])} data types")

                except Exception as e:
                    error_msg = f"Error processing Java source from {java_source_dir}: {str(e)}"
                    self.logger.warning(error_msg)
                    parsed_outputs["files"]["java_source_error"] = str(e)

        # Ensure at least one definition was successfully parsed
        if (not parsed_outputs["files"].get("wsdl") and
            not parsed_outputs["files"].get("openapi") and
            not parsed_outputs["files"].get("java_source")):
            raise Exception("Failed to parse WSDL, OpenAPI, and Java source definitions.")

        # Create manifest file
        manifest_path = f"{version_dir}/manifest.json"
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(parsed_outputs, f, indent=2, ensure_ascii=False)

        parsed_outputs["files"]["manifest"] = manifest_path
        self.logger.info(f"Created manifest file at: {manifest_path}")

        return parsed_outputs

    def register_in_catalog(self, parsed_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """
        Register the parsed definitions in a central catalog for reuse by other processes.

        Args:
            parsed_outputs: Dictionary containing parsed file metadata

        Returns:
            Dict containing catalog entry information
        """
        service_name = parsed_outputs["service_name"]
        timestamp = parsed_outputs["timestamp"]

        self.logger.info(f"Registering service '{service_name}' in catalog")

        # Create a catalog entry with metadata
        catalog_entry = {
            "service_name": service_name,
            "version": timestamp,
            "last_updated": datetime.now().isoformat(),
            "files": parsed_outputs["files"],
            "status": "available"
        }

        # Get existing catalog or create new one
        service_catalog = self.catalog.get_catalog()

        # Update the catalog with this service
        service_catalog["services"][service_name] = catalog_entry
        service_catalog["last_updated"] = datetime.now().isoformat()

        # Save back to catalog file
        self.catalog.save_catalog(service_catalog)

        # Create a "latest" directory for easy access
        version_dir = parsed_outputs["version_dir"]
        latest_dir = f"{os.path.dirname(version_dir)}/latest"

        # Remove existing latest directory if it exists
        if os.path.exists(latest_dir) and os.path.isdir(latest_dir):
            import shutil
            shutil.rmtree(latest_dir)

        # Create latest directory and copy files
        os.makedirs(latest_dir, exist_ok=True)

        # Copy all files from version directory to latest
        import shutil
        for filename in os.listdir(version_dir):
            src_file = f"{version_dir}/{filename}"
            dst_file = f"{latest_dir}/{filename}"
            if os.path.isfile(src_file):
                shutil.copy2(src_file, dst_file)

        self.logger.info(f"Registered service '{service_name}' in catalog with version {timestamp}")
        self.logger.info(f"Created 'latest' directory at {latest_dir}")

        return catalog_entry

    def process_service(self, service_name: str, wsdl_uri: str,
                       xsd_uri: Optional[str], openapi_uri: str,
                       output_base_path: str, java_source_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Main processing method that orchestrates the entire workflow.

        Args:
            service_name: Unique service identifier
            wsdl_uri: Path or URL to WSDL file
            xsd_uri: Optional path or URL to XSD file
            openapi_uri: Path or URL to OpenAPI file
            output_base_path: Base directory for outputs
            java_source_dir: Optional path to directory containing Java source files

        Returns:
            Dict containing processing results and catalog entry
        """
        try:
            # Step 1: Validate parameters
            validated_params = self.validate_parameters(
                service_name, wsdl_uri, xsd_uri, openapi_uri, output_base_path, java_source_dir
            )

            # Step 2: Fetch, parse, and store definitions
            parsed_outputs = self.fetch_and_parse_definitions(validated_params)

            # Step 3: Register in catalog
            catalog_entry = self.register_in_catalog(parsed_outputs)

            self.logger.info(f"Successfully processed service '{service_name}'")

            return {
                "success": True,
                "service_name": service_name,
                "parsed_outputs": parsed_outputs,
                "catalog_entry": catalog_entry
            }

        except Exception as e:
            self.logger.error(f"Failed to process service '{service_name}': {str(e)}")
            return {
                "success": False,
                "service_name": service_name,
                "error": str(e)
            }


def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> None:
    """Setup logging configuration"""
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    # Add file handler if specified
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter(log_format))
        logging.getLogger().addHandler(file_handler)


def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Standalone Service Definition Ingestion and Parser",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage with local files
  python standalone_ingest_parser.py \\
    --service-name example_service \\
    --wsdl-uri include/sample_data/wsdl_input/example_service.wsdl \\
    --openapi-uri include/sample_data/openapi_target/example_api.yaml

  # With custom output path and XSD
  python standalone_ingest_parser.py \\
    --service-name my_service \\
    --wsdl-uri path/to/service.wsdl \\
    --xsd-uri path/to/schema.xsd \\
    --openapi-uri path/to/api.yaml \\
    --output-base-path /custom/output/path

  # With remote URLs
  python standalone_ingest_parser.py \\
    --service-name remote_service \\
    --wsdl-uri https://example.com/service.wsdl \\
    --openapi-uri https://example.com/api.yaml \\
    --log-level DEBUG
        """
    )

    parser.add_argument(
        "--service-name",
        required=True,
        help="Unique name for the service being processed. Must be alphanumeric with underscores/hyphens only."
    )

    parser.add_argument(
        "--wsdl-uri",
        required=True,
        help="URI (local path or URL) to the WSDL file."
    )

    parser.add_argument(
        "--xsd-uri",
        help="Optional URI to a separate XSD file."
    )

    parser.add_argument(
        "--openapi-uri",
        required=True,
        help="URI to the target OpenAPI definition file."
    )

    parser.add_argument(
        "--java-source-dir",
        help="Optional path to directory containing Java source files (.java)."
    )

    parser.add_argument(
        "--output-base-path",
        default="data/parsed_definitions",
        help="Base path to store parsed outputs. (default: data/parsed_definitions)"
    )

    parser.add_argument(
        "--catalog-path",
        default="service_catalog.json",
        help="Path to the service catalog JSON file. (default: service_catalog.json)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Set the logging level. (default: INFO)"
    )

    parser.add_argument(
        "--log-file",
        help="Optional path to log file. If not specified, logs only to console."
    )

    parser.add_argument(
        "--version",
        action="version",
        version="Standalone Service Definition Parser v1.0.0"
    )

    return parser.parse_args()


def main() -> int:
    """Main entry point"""
    try:
        # Parse command line arguments
        args = parse_arguments()

        # Setup logging
        setup_logging(args.log_level, args.log_file)

        logger = logging.getLogger(__name__)
        logger.info("Starting Standalone Service Definition Ingestion and Parser")
        logger.info(f"Processing service: {args.service_name}")

        # Create parser instance
        parser = StandaloneIngestParser(args.catalog_path)

        # Process the service
        result = parser.process_service(
            service_name=args.service_name,
            wsdl_uri=args.wsdl_uri,
            xsd_uri=args.xsd_uri,
            openapi_uri=args.openapi_uri,
            output_base_path=args.output_base_path,
            java_source_dir=args.java_source_dir
        )

        if result["success"]:
            logger.info("Processing completed successfully!")
            logger.info(f"Output directory: {result['parsed_outputs']['version_dir']}")
            logger.info(f"Files processed: {list(result['parsed_outputs']['files'].keys())}")
            return 0
        else:
            logger.error(f"Processing failed: {result['error']}")
            return 1

    except KeyboardInterrupt:
        logging.getLogger(__name__).info("Process interrupted by user")
        return 130
    except Exception as e:
        logging.getLogger(__name__).error(f"Unexpected error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
