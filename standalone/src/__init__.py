# standalone_common/__init__.py
"""
Standalone common modules for the Service Definition Parser.

This package contains shared functionality adapted from the Airflow DAG common modules
for use in the standalone executable version.
"""

__version__ = "1.0.0"
__author__ = "Airflow LLM Service Mapper Team (Standalone Version)"

# Import main modules for easy access
from . import constants
from . import data_parsers
from . import validation_utils

__all__ = [
    "constants",
    "data_parsers", 
    "validation_utils"
]
