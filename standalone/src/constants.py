"""
Configuration constants for the Standalone Service Definition Parser.

This module contains all configuration constants used by the standalone parser.
It's adapted from the Airflow DAG constants but removes Airflow-specific settings.

Author: Airflow LLM Service Mapper Team (Standalone Version)
Version: 1.0.0
"""

# =============================================================================
# Storage Configuration
# =============================================================================

# Local Storage Paths (for standalone operation)
LOCAL_DATA_PATH = "data"
LOCAL_MODELS_PATH = "models"
LOCAL_LOGS_PATH = "logs"

# =============================================================================
# Dataset URIs (for compatibility with DAG modules)
# =============================================================================

PARSED_DEFINITIONS_DATASET_URI = "data://parsed_definitions"
TRAINING_DATA_DATASET_URI = "data://training_data"
TRAINED_MODEL_DATASET_URI = "data://trained_models"
OPERATIONAL_MAPPING_DATASET_URI = "data://operational_mappings"
OPERATIONAL_LOGS_DATASET_URI = "data://operational_logs"
MONITORING_METRICS_DATASET_URI = "data://monitoring_metrics"

# =============================================================================
# Data Processing Configuration
# =============================================================================

# Parser Settings
MAX_FILE_SIZE_MB = 100
SUPPORTED_WSDL_VERSIONS = ["1.1", "2.0"]
SUPPORTED_OPENAPI_VERSIONS = ["3.0", "3.1"]

# Training Data Generation
MAX_EXAMPLES_PER_OPERATION = 100
MIN_EXAMPLES_FOR_TRAINING = 50
TRAINING_DATA_FORMATS = ["json", "jsonl", "parquet"]

# =============================================================================
# Logging and Monitoring
# =============================================================================

# Log Levels
DEFAULT_LOG_LEVEL = "INFO"
DEBUG_MODE = False

# =============================================================================
# Feature Flags
# =============================================================================

# Experimental Features
ENABLE_JAVA_CODE_GENERATION = True
ENABLE_CSHARP_CODE_GENERATION = False
ENABLE_ADVANCED_HEURISTICS = True

# =============================================================================
# Version Information
# =============================================================================

PROJECT_VERSION = "1.0.0"
PYTHON_VERSION_REQUIRED = "3.9"

# Service definition catalog
SERVICE_CATALOG_VARIABLE_NAME = "service_definitions_catalog"

# Default paths
DEFAULT_OUTPUT_BASE_PATH = "data/parsed_definitions"
DEFAULT_TRAINING_DATA_PATH = "data/training_data"
DEFAULT_MODELS_PATH = "models/trained"

# Schema validation
SCHEMA_VALIDATION_ENABLED = True
