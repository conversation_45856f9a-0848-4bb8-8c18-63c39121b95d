# dags/common/parsers/source_code_parser.py
import re
from typing import Dict, List, Any

def parse_java_source(java_content: str) -> dict:
    """
    Parses Java source code to extract web service definitions.
    Focuses on JAX-WS annotations like @WebService, @WebMethod, etc.
    """
    result = {
        "service_type": "java",
        "web_services": [],
        "operations": [],
        "data_types": []
    }

    # Extract @WebService classes (handle multi-line annotations)
    web_service_pattern = r'@WebService\s*(?:\([^)]*\))?\s*(?:@\w+\s*(?:\([^)]*\))?\s*)*public\s+(?:interface|class)\s+(\w+)'
    web_services = re.finditer(web_service_pattern, java_content, re.MULTILINE | re.DOTALL)

    for match in web_services:
        service_name = match.group(1)

        # Extract WebService annotation details
        service_annotation_match = re.search(r'@WebService\s*\(([^)]*)\)', java_content[:match.end()])
        service_details = {"name": service_name}

        if service_annotation_match:
            annotation_content = service_annotation_match.group(1)
            # Extract name and targetNamespace
            name_match = re.search(r'name\s*=\s*"([^"]*)"', annotation_content)
            namespace_match = re.search(r'targetNamespace\s*=\s*"([^"]*)"', annotation_content)

            if name_match:
                service_details["annotation_name"] = name_match.group(1)
            if namespace_match:
                service_details["target_namespace"] = namespace_match.group(1)

        result["web_services"].append(service_details)

        # Find methods in this service (look for the class/interface body)
        class_start = java_content.find('{', match.end())
        if class_start == -1:
            continue

        service_content = java_content[class_start:]
        end_brace_index = find_closing_brace(service_content)
        if end_brace_index > 0:
            service_content = service_content[:end_brace_index]

        # Extract @WebMethod operations (handle multi-line method signatures)
        web_method_pattern = r'@WebMethod\s*(?:\([^)]*\))?\s*(?:@WebResult\s*(?:\([^)]*\))?\s*)*(\w+(?:\[\])?\s+(\w+)\s*\([^)]*\))'
        web_methods = re.finditer(web_method_pattern, service_content, re.MULTILINE | re.DOTALL)

        for method_match in web_methods:
            method_signature = method_match.group(1)
            method_name = method_match.group(2)

            # Extract operation name from @WebMethod annotation
            method_annotation_start = service_content.rfind('@WebMethod', 0, method_match.start())
            if method_annotation_start != -1:
                method_annotation_end = service_content.find(')', method_annotation_start)
                if method_annotation_end != -1:
                    method_annotation = service_content[method_annotation_start:method_annotation_end + 1]
                    operation_name_match = re.search(r'operationName\s*=\s*"([^"]*)"', method_annotation)
                    operation_name = operation_name_match.group(1) if operation_name_match else method_name
                else:
                    operation_name = method_name
            else:
                operation_name = method_name

            # Extract return type
            return_type_match = re.match(r'(\w+(?:\[\])?)', method_signature.strip())
            return_type = return_type_match.group(1) if return_type_match else "void"

            # Extract parameters from method signature
            param_section_match = re.search(r'\(([^)]*)\)', method_signature)
            parameters = []

            if param_section_match:
                param_section = param_section_match.group(1).strip()
                if param_section:
                    # Split parameters and extract @WebParam annotations
                    param_parts = re.split(r',(?![^()]*\))', param_section)

                    for param_part in param_parts:
                        param_part = param_part.strip()
                        if not param_part:
                            continue

                        # Extract @WebParam name if present
                        webparam_match = re.search(r'@WebParam\s*\([^)]*name\s*=\s*"([^"]*)"[^)]*\)', param_part)
                        param_name = webparam_match.group(1) if webparam_match else None

                        # Extract type and variable name
                        type_var_match = re.search(r'(\w+(?:\[\])?)\s+(\w+)(?:\s*$|(?=\s*,))', param_part)
                        if type_var_match:
                            param_type = type_var_match.group(1)
                            var_name = type_var_match.group(2)

                            parameters.append({
                                "name": param_name or var_name,
                                "variable_name": var_name,
                                "type": param_type
                            })

            result["operations"].append({
                "name": operation_name,
                "method_name": method_name,
                "service": service_name,
                "return_type": return_type,
                "parameters": parameters
            })

    # Extract data types from @XmlType classes
    xml_type_pattern = r'@XmlType\s*(?:\([^)]*\))?\s*(?:@\w+\s*(?:\([^)]*\))?\s*)*public\s+class\s+(\w+)'
    xml_types = re.finditer(xml_type_pattern, java_content, re.MULTILINE | re.DOTALL)

    for match in xml_types:
        type_name = match.group(1)

        # Extract XmlType annotation details
        type_annotation_match = re.search(r'@XmlType\s*\(([^)]*)\)', java_content[:match.end()])
        type_details = {"name": type_name, "category": "data_type"}

        if type_annotation_match:
            annotation_content = type_annotation_match.group(1)
            name_match = re.search(r'name\s*=\s*"([^"]*)"', annotation_content)
            if name_match:
                type_details["xml_name"] = name_match.group(1)

        # Find class fields
        class_start = java_content.find('{', match.end())
        if class_start != -1:
            class_content = java_content[class_start:]
            end_brace_index = find_closing_brace(class_content)
            if end_brace_index > 0:
                class_content = class_content[:end_brace_index]

                # Extract fields with @XmlElement annotations
                field_pattern = r'@XmlElement\s*(?:\([^)]*\))?\s*(?:protected|private|public)?\s*(\w+(?:\[\])?)\s+(\w+);'
                fields = re.finditer(field_pattern, class_content, re.MULTILINE)

                type_details["fields"] = []
                for field_match in fields:
                    field_type = field_match.group(1)
                    field_name = field_match.group(2)

                    # Check if field is required
                    field_annotation_start = class_content.rfind('@XmlElement', 0, field_match.start())
                    required = False
                    if field_annotation_start != -1:
                        field_annotation_end = class_content.find(')', field_annotation_start)
                        if field_annotation_end != -1:
                            field_annotation = class_content[field_annotation_start:field_annotation_end + 1]
                            required = 'required = true' in field_annotation

                    type_details["fields"].append({
                        "name": field_name,
                        "type": field_type,
                        "required": required
                    })

        result["data_types"].append(type_details)

    return result

def parse_csharp_source(csharp_content: str) -> dict:
    """
    Parses C# source code to extract web service definitions.
    Focuses on WCF attributes like [ServiceContract], [OperationContract], etc.
    """
    # Similar implementation for C# services
    # ...
    
    return {
        "service_type": "csharp",
        "web_services": [],
        "operations": []
    }

def find_closing_brace(text: str) -> int:
    """Helper function to find the matching closing brace"""
    brace_count = 0
    for i, char in enumerate(text):
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                return i
    return -1