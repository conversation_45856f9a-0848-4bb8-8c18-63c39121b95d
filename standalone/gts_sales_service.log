2025-07-10 11:44:02,452 - __main__ - INFO - Starting Standalone Service Definition Ingestion and Parser
2025-07-10 11:44:02,452 - __main__ - INFO - Processing service: gts_sales_service
2025-07-10 11:44:02,453 - __main__ - INFO - Validating parameters for service: gts_sales_service
2025-07-10 11:44:02,453 - __main__ - INFO - All parameters validated successfully
2025-07-10 11:44:02,453 - __main__ - INFO - Starting definition parsing for service: gts_sales_service
2025-07-10 11:44:02,453 - __main__ - INFO - Created output directory: ../data/parsed_definitions//gts_sales_service/v1752140642
2025-07-10 11:44:02,453 - __main__ - INFO - Processing WSDL from: ../shared/sample_data/wsdl_input/WSGtsSalesServiceBinding.wsdl
2025-07-10 11:44:02,456 - __main__ - INFO - W<PERSON>L successfully parsed and stored at: ../data/parsed_definitions//gts_sales_service/v1752140642/parsed_wsdl.json
2025-07-10 11:44:02,457 - __main__ - INFO - WSDL type definitions extracted and stored at: ../data/parsed_definitions//gts_sales_service/v1752140642/parsed_wsdl_types.json
2025-07-10 11:44:02,457 - __main__ - INFO - Extracted 0 type definitions from WSDL
2025-07-10 11:44:02,457 - __main__ - INFO - Processing XSD from: ../shared/sample_data/xsd_input/commonelements.xsd
2025-07-10 11:44:02,774 - __main__ - INFO - XSD successfully parsed and stored at: ../data/parsed_definitions//gts_sales_service/v1752140642/parsed_xsd.json
2025-07-10 11:44:02,777 - __main__ - INFO - XSD type definitions extracted and stored at: ../data/parsed_definitions//gts_sales_service/v1752140642/parsed_xsd_types.json
2025-07-10 11:44:02,777 - __main__ - INFO - Extracted 0 type definitions from XSD
2025-07-10 11:44:02,777 - __main__ - INFO - Processing OpenAPI from: ../shared/sample_data/osdmp_target/unified-api-interface.yaml
2025-07-10 11:44:03,620 - __main__ - ERROR - Error processing OpenAPI from ../shared/sample_data/osdmp_target/unified-api-interface.yaml: Object of type datetime is not JSON serializable
2025-07-10 11:44:03,622 - __main__ - INFO - Created manifest file at: ../data/parsed_definitions//gts_sales_service/v1752140642/manifest.json
2025-07-10 11:44:03,622 - __main__ - INFO - Registering service 'gts_sales_service' in catalog
2025-07-10 11:44:03,625 - __main__ - INFO - Registered service 'gts_sales_service' in catalog with version 1752140642
2025-07-10 11:44:03,625 - __main__ - INFO - Created 'latest' directory at ../data/parsed_definitions//gts_sales_service/latest
2025-07-10 11:44:03,625 - __main__ - INFO - Successfully processed service 'gts_sales_service'
2025-07-10 11:44:03,625 - __main__ - INFO - Processing completed successfully!
2025-07-10 11:44:03,625 - __main__ - INFO - Output directory: ../data/parsed_definitions//gts_sales_service/v1752140642
2025-07-10 11:44:03,625 - __main__ - INFO - Files processed: ['wsdl', 'wsdl_types', 'xsd', 'xsd_types', 'openapi_error', 'manifest']
2025-07-10 14:50:19,490 - __main__ - INFO - Starting Standalone Service Definition Ingestion and Parser
2025-07-10 14:50:19,490 - __main__ - INFO - Processing service: gts_sales_service
2025-07-10 14:50:19,490 - __main__ - INFO - Validating parameters for service: gts_sales_service
2025-07-10 14:50:19,490 - __main__ - INFO - All parameters validated successfully
2025-07-10 14:50:19,490 - __main__ - INFO - Starting definition parsing for service: gts_sales_service
2025-07-10 14:50:19,490 - __main__ - INFO - Created output directory: ../data/parsed_definitions//gts_sales_service/v1752151819
2025-07-10 14:50:19,491 - __main__ - INFO - Processing WSDL from: ../shared/sample_data/wsdl_input/WSGtsSalesServiceBinding.wsdl
2025-07-10 14:50:19,495 - __main__ - INFO - WSDL successfully parsed and stored at: ../data/parsed_definitions//gts_sales_service/v1752151819/parsed_wsdl.json
2025-07-10 14:50:19,496 - __main__ - INFO - WSDL type definitions extracted and stored at: ../data/parsed_definitions//gts_sales_service/v1752151819/parsed_wsdl_types.json
2025-07-10 14:50:19,496 - __main__ - INFO - Extracted 0 type definitions from WSDL
2025-07-10 14:50:19,496 - __main__ - INFO - Processing XSD from: ../shared/sample_data/xsd_input/commonelements.xsd
2025-07-10 14:50:19,964 - __main__ - INFO - XSD successfully parsed and stored at: ../data/parsed_definitions//gts_sales_service/v1752151819/parsed_xsd.json
2025-07-10 14:50:19,967 - __main__ - INFO - XSD type definitions extracted and stored at: ../data/parsed_definitions//gts_sales_service/v1752151819/parsed_xsd_types.json
2025-07-10 14:50:19,967 - __main__ - INFO - Extracted 0 type definitions from XSD
2025-07-10 14:50:19,967 - __main__ - INFO - Processing OpenAPI from: ../shared/sample_data/osdmp_target/unified-api-interface.yaml
2025-07-10 14:50:21,053 - __main__ - ERROR - Error processing OpenAPI from ../shared/sample_data/osdmp_target/unified-api-interface.yaml: Object of type datetime is not JSON serializable
2025-07-10 14:50:21,054 - __main__ - INFO - Created manifest file at: ../data/parsed_definitions//gts_sales_service/v1752151819/manifest.json
2025-07-10 14:50:21,054 - __main__ - INFO - Registering service 'gts_sales_service' in catalog
2025-07-10 14:50:21,061 - __main__ - INFO - Registered service 'gts_sales_service' in catalog with version 1752151819
2025-07-10 14:50:21,061 - __main__ - INFO - Created 'latest' directory at ../data/parsed_definitions//gts_sales_service/latest
2025-07-10 14:50:21,061 - __main__ - INFO - Successfully processed service 'gts_sales_service'
2025-07-10 14:50:21,061 - __main__ - INFO - Processing completed successfully!
2025-07-10 14:50:21,061 - __main__ - INFO - Output directory: ../data/parsed_definitions//gts_sales_service/v1752151819
2025-07-10 14:50:21,062 - __main__ - INFO - Files processed: ['wsdl', 'wsdl_types', 'xsd', 'xsd_types', 'openapi_error', 'manifest']
