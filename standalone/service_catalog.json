{"services": {"gts_sales_service": {"service_name": "gts_sales_service", "version": 1752151819, "last_updated": "2025-07-10T14:50:21.054107", "files": {"wsdl": "../data/parsed_definitions//gts_sales_service/v1752151819/parsed_wsdl.json", "wsdl_types": "../data/parsed_definitions//gts_sales_service/v1752151819/parsed_wsdl_types.json", "xsd": "../data/parsed_definitions//gts_sales_service/v1752151819/parsed_xsd.json", "xsd_types": "../data/parsed_definitions//gts_sales_service/v1752151819/parsed_xsd_types.json", "openapi_error": "Object of type datetime is not JSON serializable", "manifest": "../data/parsed_definitions//gts_sales_service/v1752151819/manifest.json"}, "status": "available"}, "todo_service": {"service_name": "todo_service", "version": 1752151602, "last_updated": "2025-07-10T14:46:42.464136", "files": {"wsdl": "../data/parsed_definitions//todo_service/v1752151602/parsed_wsdl.json", "wsdl_types": "../data/parsed_definitions//todo_service/v1752151602/parsed_wsdl_types.json", "xsd": "../data/parsed_definitions//todo_service/v1752151602/parsed_xsd.json", "xsd_types": "../data/parsed_definitions//todo_service/v1752151602/parsed_xsd_types.json", "openapi": "../data/parsed_definitions//todo_service/v1752151602/parsed_openapi.json", "openapi_types": "../data/parsed_definitions//todo_service/v1752151602/parsed_openapi_types.json", "manifest": "../data/parsed_definitions//todo_service/v1752151602/manifest.json"}, "status": "available"}}, "last_updated": "2025-07-10T14:50:21.054438"}