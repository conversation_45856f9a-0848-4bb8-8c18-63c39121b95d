# Implementation Plan for Critical Improvements

## 🎯 Immediate Action Items (Next 2 Weeks)

### 1. Create Airflow Documentation Structure

#### Create Missing Airflow Documentation Directory
```bash
mkdir -p airflow/docs
```

#### Required Documentation Files
1. **airflow/docs/README_AIRFLOW.md** - Airflow-specific overview
2. **airflow/docs/DEPLOYMENT_GUIDE.md** - Complete deployment instructions
3. **airflow/docs/DAG_REFERENCE.md** - Detailed DAG documentation
4. **airflow/docs/CONFIGURATION_GUIDE.md** - Configuration management
5. **airflow/docs/TROUBLESHOOTING.md** - Common issues and solutions

### 2. Fix Shared Module Import Issues

#### Current Problem
Airflow DAGs import from local `common/` instead of `shared/common/`:
```python
# Current (incorrect)
from common import constants, validation_utils
from common.model_management import save_fine_tuned_model

# Should be
from shared.common import constants, validation_utils
from shared.common.model_management import save_fine_tuned_model
```

#### Files to Update
- `airflow/dags/01_ingest_and_parse_definitions_dag.py`
- `airflow/dags/02_generate_training_data_dag.py`
- `airflow/dags/03_train_ml_model_dag.py`
- `airflow/dags/04_operational_mapping_pipeline_dag.py`
- `airflow/dags/05_monitoring_and_retraining_trigger_dag.py`
- `airflow/dags/06_ingest_operational_logs_dag.py`

#### Actions Required
1. Update all import statements in Airflow DAGs
2. Remove duplicate `airflow/dags/common/` directory
3. Update `airflow/requirements.txt` to include shared dependencies
4. Test all DAGs after import changes

### 3. Implement Basic Testing Framework

#### Create Testing Structure
```
tests/
├── unit/
│   ├── test_shared_common/
│   ├── test_standalone/
│   └── test_airflow_dags/
├── integration/
│   ├── test_standalone_airflow_compatibility/
│   └── test_end_to_end_workflows/
└── fixtures/
    ├── sample_wsdl/
    ├── sample_openapi/
    └── expected_outputs/
```

#### Priority Test Cases
1. **Shared module functionality tests**
2. **Airflow DAG syntax validation**
3. **Standalone parser integration tests**
4. **Docker container functionality tests**

## 🔧 Detailed Implementation Steps

### Step 1: Airflow Documentation Creation

#### 1.1 Create airflow/docs/README_AIRFLOW.md
```markdown
# Airflow LLM Service Mapper - Airflow Deployment

## Overview
Complete guide for deploying and managing the LLM Service Mapper using Apache Airflow.

## Quick Start
1. Prerequisites
2. Installation
3. Configuration
4. Running DAGs
5. Monitoring

## DAG Overview
- 01_ingest_and_parse_definitions_dag
- 02_generate_training_data_dag
- 03_train_ml_model_dag
- 04_operational_mapping_pipeline_dag
- 05_monitoring_and_retraining_trigger_dag
- 06_ingest_operational_logs_dag
```

#### 1.2 Create airflow/docs/DEPLOYMENT_GUIDE.md
```markdown
# Airflow Deployment Guide

## Docker Deployment (Recommended)
### Prerequisites
### Configuration
### Deployment Steps
### Verification

## Manual Deployment
### System Requirements
### Installation Steps
### Configuration
### Service Setup

## Cloud Deployment
### AWS MWAA
### Google Cloud Composer
### Azure Data Factory
```

### Step 2: Fix Import Issues

#### 2.1 Update DAG Import Statements
For each DAG file, replace:
```python
# OLD
from common import constants, validation_utils
from common.model_management import save_fine_tuned_model
from common.definition_store import DefinitionStore

# NEW
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'shared'))
from shared.common import constants, validation_utils
from shared.common.model_management import save_fine_tuned_model
from shared.common.definition_store import DefinitionStore
```

#### 2.2 Update airflow/requirements.txt
Add shared dependencies:
```txt
# Existing Airflow dependencies
apache-airflow==3.0.0
...

# Shared module dependencies
lxml>=4.9.0
xmlschema>=2.0.0
pyyaml>=6.0
requests>=2.28.0
pendulum>=2.1.0
```

#### 2.3 Remove Duplicate Common Directory
```bash
rm -rf airflow/dags/common/
```

### Step 3: Create Basic Testing Framework

#### 3.1 Create tests/conftest.py
```python
import pytest
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "shared"))
sys.path.insert(0, str(project_root / "standalone" / "src"))

@pytest.fixture
def sample_wsdl_content():
    """Load sample WSDL content for testing"""
    with open(project_root / "shared" / "sample_data" / "wsdl_input" / "todo-service.wsdl") as f:
        return f.read()

@pytest.fixture
def sample_openapi_content():
    """Load sample OpenAPI content for testing"""
    with open(project_root / "shared" / "sample_data" / "osdmp_target" / "todo-api.yaml") as f:
        return f.read()
```

#### 3.2 Create tests/unit/test_shared_common/test_data_parsers.py
```python
import pytest
from shared.common.data_parsers import parse_wsdl_content, parse_openapi_content

def test_parse_wsdl_content(sample_wsdl_content):
    """Test WSDL parsing functionality"""
    result = parse_wsdl_content(sample_wsdl_content)
    
    assert "services" in result
    assert "operations" in result
    assert "messages" in result
    assert "types" in result
    assert len(result["operations"]) > 0

def test_parse_openapi_content(sample_openapi_content):
    """Test OpenAPI parsing functionality"""
    result = parse_openapi_content(sample_openapi_content)
    
    assert "openapi" in result or "swagger" in result
    assert "info" in result
    assert "paths" in result
```

#### 3.3 Create tests/unit/test_airflow_dags/test_dag_syntax.py
```python
import pytest
import os
from airflow.models import DagBag

def test_dag_syntax():
    """Test that all DAGs can be loaded without syntax errors"""
    dag_bag = DagBag(dag_folder="airflow/dags", include_examples=False)
    
    assert len(dag_bag.import_errors) == 0, f"DAG import errors: {dag_bag.import_errors}"
    assert len(dag_bag.dags) > 0, "No DAGs found"

def test_dag_structure():
    """Test DAG structure and dependencies"""
    dag_bag = DagBag(dag_folder="airflow/dags", include_examples=False)
    
    expected_dags = [
        "ingest_and_parse_definitions_dag",
        "generate_training_data_dag",
        "train_ml_model_dag",
        "operational_mapping_pipeline_dag",
        "monitoring_and_retraining_trigger_dag",
        "ingest_operational_logs_dag"
    ]
    
    for dag_id in expected_dags:
        assert dag_id in dag_bag.dags, f"DAG {dag_id} not found"
```

## 📋 Validation Checklist

### Before Implementation
- [ ] Backup current codebase
- [ ] Review all proposed changes with team
- [ ] Ensure development environment is ready
- [ ] Verify Docker and Airflow installations

### During Implementation
- [ ] Test each change incrementally
- [ ] Verify imports work correctly
- [ ] Run existing tests after each change
- [ ] Document any issues encountered

### After Implementation
- [ ] Run full test suite
- [ ] Verify Airflow DAGs load correctly
- [ ] Test standalone functionality
- [ ] Validate Docker builds
- [ ] Update main README with new structure

## 🚨 Risk Mitigation

### Potential Issues
1. **Import path conflicts**: Ensure Python path is correctly configured
2. **Dependency conflicts**: Verify all shared dependencies are compatible
3. **DAG loading failures**: Test DAGs individually before bulk changes
4. **Docker build failures**: Update Dockerfiles to include shared modules

### Rollback Plan
1. Keep backup of original files
2. Use git branches for each major change
3. Test rollback procedures before implementation
4. Document rollback steps for each change

## 📊 Success Criteria

### Phase 1 Complete When:
- [ ] All Airflow documentation files created
- [ ] All DAG imports updated and working
- [ ] Basic test framework implemented
- [ ] All tests passing
- [ ] Docker builds successful
- [ ] Documentation updated

### Quality Gates
- All DAGs load without errors
- Standalone functionality unchanged
- Docker containers build and run
- Tests provide meaningful coverage
- Documentation is clear and complete

---

**This implementation plan provides a clear roadmap for addressing the most critical issues identified in the project analysis.**
