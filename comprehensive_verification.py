#!/usr/bin/env python3
"""
Comprehensive verification script for Airflow import fix and additional issues.

This script performs deep analysis of DAG files, shared modules, and potential issues.
"""

import os
import re
import ast
from pathlib import Path
from typing import List, Dict, Set, <PERSON>ple

def extract_imports_from_dag(dag_file: Path) -> Dict[str, List[str]]:
    """Extract all import statements from a DAG file."""
    imports = {
        'shared_imports': [],
        'local_imports': [],
        'standard_imports': [],
        'third_party_imports': []
    }
    
    try:
        with open(dag_file, 'r') as f:
            content = f.read()
        
        # Parse the AST to get imports
        tree = ast.parse(content)
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    module_name = alias.name
                    if module_name.startswith('shared.'):
                        imports['shared_imports'].append(module_name)
                    elif module_name.startswith('common.'):
                        imports['local_imports'].append(module_name)
                    elif module_name in ['sys', 'os', 'json', 'logging', 'pendulum']:
                        imports['standard_imports'].append(module_name)
                    else:
                        imports['third_party_imports'].append(module_name)
            
            elif isinstance(node, ast.ImportFrom):
                if node.module:
                    if node.module.startswith('shared.'):
                        for alias in node.names:
                            imports['shared_imports'].append(f"{node.module}.{alias.name}")
                    elif node.module.startswith('common.'):
                        for alias in node.names:
                            imports['local_imports'].append(f"{node.module}.{alias.name}")
                    elif node.module.startswith('airflow.'):
                        for alias in node.names:
                            imports['third_party_imports'].append(f"{node.module}.{alias.name}")
    
    except Exception as e:
        print(f"    ⚠️  Error parsing {dag_file.name}: {e}")
    
    return imports

def check_shared_module_exists(module_path: str) -> bool:
    """Check if a shared module exists in the filesystem."""
    # Convert module path to file path
    # e.g., "shared.common.constants" -> "shared/common/constants.py"
    parts = module_path.split('.')
    
    if len(parts) < 2 or parts[0] != 'shared':
        return False
    
    # Check for module file
    file_path = Path('/'.join(parts) + '.py')
    if file_path.exists():
        return True
    
    # Check for package directory with __init__.py
    dir_path = Path('/'.join(parts))
    if dir_path.is_dir() and (dir_path / '__init__.py').exists():
        return True
    
    return False

def find_problematic_imports() -> Dict[str, List[str]]:
    """Find imports that might cause issues."""
    issues = {
        'missing_shared_modules': [],
        'old_common_imports': [],
        'duplicate_imports': [],
        'circular_imports': []
    }
    
    dags_dir = Path("airflow/dags")
    
    for dag_file in dags_dir.glob("*_dag.py"):
        print(f"\n🔍 Analyzing {dag_file.name}...")
        
        with open(dag_file, 'r') as f:
            content = f.read()
        
        # Check for old common imports (should be shared now)
        old_common_patterns = [
            r'from common import',
            r'from common\.',
            r'import common\.'
        ]
        
        for pattern in old_common_patterns:
            if re.search(pattern, content):
                issues['old_common_imports'].append(f"{dag_file.name}: {pattern}")
                print(f"    ❌ Found old common import: {pattern}")
        
        # Extract imports
        imports = extract_imports_from_dag(dag_file)
        
        # Check shared imports
        for shared_import in imports['shared_imports']:
            if not check_shared_module_exists(shared_import.split('.')[0] + '.' + shared_import.split('.')[1]):
                issues['missing_shared_modules'].append(f"{dag_file.name}: {shared_import}")
                print(f"    ❌ Missing shared module: {shared_import}")
            else:
                print(f"    ✅ Shared module exists: {shared_import}")
        
        # Check for local imports (potential issues)
        for local_import in imports['local_imports']:
            issues['old_common_imports'].append(f"{dag_file.name}: {local_import}")
            print(f"    ⚠️  Local common import found: {local_import}")
    
    return issues

def check_docker_configuration() -> List[str]:
    """Check Docker configuration for potential issues."""
    issues = []
    
    docker_compose_path = Path("airflow/docker-compose.yaml")
    
    if not docker_compose_path.exists():
        issues.append("docker-compose.yaml not found")
        return issues
    
    with open(docker_compose_path, 'r') as f:
        content = f.read()
    
    # Check volume mounts
    required_mounts = [
        "/opt/airflow/shared",
        "/opt/airflow/data"
    ]
    
    for mount in required_mounts:
        if mount not in content:
            issues.append(f"Missing volume mount: {mount}")
        else:
            print(f"    ✅ Volume mount found: {mount}")
    
    # Check for potential path issues
    if "${AIRFLOW_PROJ_DIR:-.}/../shared" not in content:
        issues.append("Shared directory mount path might be incorrect")
    
    return issues

def check_airflow_requirements() -> List[str]:
    """Check Airflow requirements for missing dependencies."""
    issues = []
    
    requirements_path = Path("airflow/requirements.txt")
    
    if not requirements_path.exists():
        issues.append("airflow/requirements.txt not found")
        return issues
    
    with open(requirements_path, 'r') as f:
        requirements = f.read().lower()
    
    # Check for essential dependencies
    essential_deps = [
        'pendulum',
        'lxml',
        'pyyaml',
        'requests'
    ]
    
    for dep in essential_deps:
        if dep not in requirements:
            issues.append(f"Missing dependency in requirements.txt: {dep}")
        else:
            print(f"    ✅ Dependency found: {dep}")
    
    return issues

def check_dag_syntax_issues() -> List[str]:
    """Check for common DAG syntax issues."""
    issues = []
    
    dags_dir = Path("airflow/dags")
    
    for dag_file in dags_dir.glob("*_dag.py"):
        with open(dag_file, 'r') as f:
            content = f.read()
        
        # Check for duplicate import statements
        import_lines = [line.strip() for line in content.split('\n') if line.strip().startswith(('import ', 'from '))]
        seen_imports = set()
        
        for line in import_lines:
            if line in seen_imports:
                issues.append(f"{dag_file.name}: Duplicate import - {line}")
            seen_imports.add(line)
        
        # Check for missing DAG instantiation
        if "dag_instance = " not in content and dag_file.name.endswith("_dag.py"):
            # This is actually OK for newer Airflow versions with decorators
            pass
    
    return issues

def main():
    """Run comprehensive verification."""
    print("🔍 COMPREHENSIVE AIRFLOW VERIFICATION")
    print("=" * 50)
    
    all_issues = []
    
    # 1. Check import issues
    print("\n📦 Checking import issues...")
    import_issues = find_problematic_imports()
    
    for category, issues in import_issues.items():
        if issues:
            print(f"\n❌ {category.replace('_', ' ').title()}:")
            for issue in issues:
                print(f"  - {issue}")
            all_issues.extend(issues)
    
    # 2. Check Docker configuration
    print("\n🐳 Checking Docker configuration...")
    docker_issues = check_docker_configuration()
    
    if docker_issues:
        print("❌ Docker configuration issues:")
        for issue in docker_issues:
            print(f"  - {issue}")
        all_issues.extend(docker_issues)
    else:
        print("✅ Docker configuration looks good")
    
    # 3. Check requirements
    print("\n📋 Checking requirements...")
    req_issues = check_airflow_requirements()
    
    if req_issues:
        print("❌ Requirements issues:")
        for issue in req_issues:
            print(f"  - {issue}")
        all_issues.extend(req_issues)
    else:
        print("✅ Requirements look good")
    
    # 4. Check DAG syntax
    print("\n📝 Checking DAG syntax...")
    syntax_issues = check_dag_syntax_issues()
    
    if syntax_issues:
        print("❌ DAG syntax issues:")
        for issue in syntax_issues:
            print(f"  - {issue}")
        all_issues.extend(syntax_issues)
    else:
        print("✅ DAG syntax looks good")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 50)
    
    if all_issues:
        print(f"❌ Found {len(all_issues)} issues that need attention:")
        print("\nISSUES TO FIX:")
        for i, issue in enumerate(all_issues, 1):
            print(f"{i:2d}. {issue}")
        
        print("\n🔧 RECOMMENDED ACTIONS:")
        if any("old common import" in issue.lower() for issue in all_issues):
            print("- Update old 'from common' imports to 'from shared.common'")
        if any("missing" in issue.lower() for issue in all_issues):
            print("- Check file paths and ensure all required modules exist")
        if any("duplicate" in issue.lower() for issue in all_issues):
            print("- Remove duplicate import statements")
        
        return 1
    else:
        print("🎉 No issues found! Everything looks good.")
        return 0

if __name__ == "__main__":
    exit(main())
