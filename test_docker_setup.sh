#!/bin/bash
# Test script to verify that the Airflow Docker setup can import shared modules

set -e

echo "🧪 Testing Airflow Docker setup for shared module imports..."
echo "============================================================"

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found. Please install docker-compose."
    exit 1
fi

# Change to airflow directory
cd airflow/

echo "📁 Current directory: $(pwd)"
echo "📋 Checking docker-compose.yaml..."

# Check if docker-compose.yaml exists
if [ ! -f "docker-compose.yaml" ]; then
    echo "❌ docker-compose.yaml not found in airflow directory"
    exit 1
fi

# Check if shared volume is mounted
if grep -q "/opt/airflow/shared" docker-compose.yaml; then
    echo "✅ Shared volume mount found in docker-compose.yaml"
else
    echo "❌ Shared volume mount not found in docker-compose.yaml"
    echo "Please ensure the following line is in the volumes section:"
    echo "    - \${AIRFLOW_PROJ_DIR:-.}/../shared:/opt/airflow/shared"
    exit 1
fi

echo ""
echo "🐳 Starting Airflow services..."

# Start Airflow services
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Check if services are running
echo "📊 Checking service status..."
docker-compose ps

echo ""
echo "🧪 Testing shared module imports in container..."

# Test shared module import
docker-compose exec -T airflow-webserver python -c "
import sys
import os

print('Python path:')
for path in sys.path:
    print(f'  {path}')

print('')
print('Testing shared module imports...')

# Check if shared directory exists
if os.path.exists('/opt/airflow/shared'):
    print('✅ /opt/airflow/shared directory exists')
    
    # List contents
    print('Contents of /opt/airflow/shared:')
    for item in os.listdir('/opt/airflow/shared'):
        print(f'  {item}')
    
    # Add to path and test imports
    sys.path.insert(0, '/opt/airflow/shared')
    
    try:
        from shared.common import constants
        print('✅ constants imported successfully')
    except Exception as e:
        print(f'❌ constants import failed: {e}')
    
    try:
        from shared.common import data_parsers
        print('✅ data_parsers imported successfully')
    except Exception as e:
        print(f'❌ data_parsers import failed: {e}')
    
    try:
        from shared.common import validation_utils
        print('✅ validation_utils imported successfully')
    except Exception as e:
        print(f'❌ validation_utils import failed: {e}')
    
else:
    print('❌ /opt/airflow/shared directory does not exist')
    print('Available directories in /opt/airflow:')
    for item in os.listdir('/opt/airflow'):
        if os.path.isdir(f'/opt/airflow/{item}'):
            print(f'  {item}/')
"

echo ""
echo "🧪 Testing DAG syntax..."

# Test DAG syntax
docker-compose exec -T airflow-webserver python -c "
import sys
sys.path.insert(0, '/opt/airflow/shared')

print('Testing DAG file syntax...')
import py_compile

dag_files = [
    '/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py',
    '/opt/airflow/dags/02_generate_training_data_dag.py',
    '/opt/airflow/dags/03_train_ml_model_dag.py',
    '/opt/airflow/dags/04_operational_mapping_pipeline_dag.py',
    '/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py',
    '/opt/airflow/dags/06_ingest_operational_logs_dag.py'
]

for dag_file in dag_files:
    try:
        py_compile.compile(dag_file, doraise=True)
        print(f'✅ {dag_file.split(\"/\")[-1]} syntax OK')
    except Exception as e:
        print(f'❌ {dag_file.split(\"/\")[-1]} syntax error: {e}')
"

echo ""
echo "🧪 Testing Airflow DAG list..."

# Test Airflow DAG list
docker-compose exec -T airflow-webserver airflow dags list 2>/dev/null | grep -E "(01_|02_|03_|04_|05_|06_)" || echo "No DAGs found with expected naming pattern"

echo ""
echo "🧪 Checking for import errors..."

# Check for import errors
docker-compose exec -T airflow-webserver airflow dags list-import-errors

echo ""
echo "✅ Test completed!"
echo ""
echo "To access Airflow UI:"
echo "  http://localhost:8080"
echo "  Username: airflow"
echo "  Password: airflow"
echo ""
echo "To stop services:"
echo "  docker-compose down"
