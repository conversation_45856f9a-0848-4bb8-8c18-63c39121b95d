# Frequently Asked Questions (FAQ)

## 🚀 Getting Started

### Q: Which deployment option should I choose?
**A:** It depends on your use case:
- **Standalone**: For development, testing, CI/CD, or simple one-time conversions
- **Airflow**: For production ML pipelines, team collaboration, or complex workflows

See our [Deployment Comparison Guide](docs/DEPLOYMENT_COMPARISON.md) for detailed analysis.

### Q: How long does setup take?
**A:** 
- **Standalone**: 5 minutes (Python + dependencies)
- **Airflow**: 30-60 minutes (Docker + configuration)

### Q: What are the minimum system requirements?
**A:**
- **Standalone**: Python 3.9+, 4GB RAM, 1GB disk
- **Airflow**: Docker, 8GB RAM, 20GB disk

### Q: Can I try both options?
**A:** Yes! They're completely independent. Start with standalone for quick results, then explore Airflow for advanced features.

## 🔧 Technical Questions

### Q: What file formats are supported?
**A:** 
- **Input**: WSDL 1.1/2.0, XSD, OpenAPI 3.0/3.1, Swagger 2.0
- **Output**: JSON, YAML, structured type definitions

### Q: How accurate is the WSDL to OpenAPI conversion?
**A:** The conversion handles:
- ✅ Basic operations and data types (95%+ accuracy)
- ✅ Complex types and nested structures (90%+ accuracy)
- ⚠️ Advanced SOAP features may require manual review
- ⚠️ Custom extensions need case-by-case evaluation

### Q: Can I customize the conversion logic?
**A:** Yes! The system is designed for extensibility:
- **Standalone**: Modify parsers in `standalone/src/`
- **Airflow**: Extend shared modules in `shared/common/`
- **Both**: Add custom heuristics and rules

### Q: Does it support SOAP 1.2?
**A:** Yes, both SOAP 1.1 and 1.2 are supported through WSDL parsing.

### Q: What about WS-* standards (WS-Security, WS-Policy)?
**A:** Basic support is included. Complex WS-* features are parsed but may require manual mapping to OpenAPI equivalents.

## 🤖 Machine Learning

### Q: Do I need ML knowledge to use this?
**A:** 
- **Standalone**: No ML knowledge required
- **Airflow**: Basic ML understanding helpful for advanced features

### Q: What ML models are used?
**A:** The system uses Hugging Face transformers:
- **T5**: For sequence-to-sequence mapping
- **BERT**: For classification tasks
- **CodeT5**: For code generation (optional)

### Q: Can I use my own ML models?
**A:** Yes! The Airflow pipeline supports:
- Custom Hugging Face models
- Fine-tuning with your own data
- Integration with external ML services

### Q: How much training data do I need?
**A:** 
- **Minimum**: 50 examples per service type
- **Recommended**: 200+ examples for good performance
- **Optimal**: 1000+ examples for production quality

### Q: Can I train models without Airflow?
**A:** The ML training is currently Airflow-specific, but you can:
- Export training data from standalone
- Use external ML platforms
- Contribute standalone ML features

## 🐳 Docker & Deployment

### Q: Is Docker required?
**A:** 
- **Standalone**: No, but recommended for consistency
- **Airflow**: Yes, Docker Compose is the recommended deployment method

### Q: Can I deploy to Kubernetes?
**A:** Yes! Both options support Kubernetes:
- **Standalone**: Deploy as Jobs or CronJobs
- **Airflow**: Use official Helm charts

### Q: What about cloud deployment?
**A:** Supported platforms:
- **AWS**: ECS, EKS, MWAA (Managed Airflow)
- **Google Cloud**: GKE, Cloud Composer
- **Azure**: AKS, Data Factory
- **Any cloud**: Docker containers work everywhere

### Q: How do I handle secrets and credentials?
**A:** 
- **Standalone**: Environment variables or config files
- **Airflow**: Built-in secrets backend (Variables, Connections)

## 📊 Performance & Scaling

### Q: How fast is the conversion?
**A:** Typical performance:
- **Simple WSDL**: 1-5 seconds
- **Complex WSDL**: 10-30 seconds
- **Large files (>1MB)**: 30-60 seconds

### Q: Can I process multiple files simultaneously?
**A:** 
- **Standalone**: Run multiple instances
- **Airflow**: Built-in parallel processing

### Q: What's the maximum file size supported?
**A:** Default limit is 100MB, configurable up to system memory limits.

### Q: How do I optimize performance?
**A:** 
- Use SSD storage for better I/O
- Increase memory for large files
- Enable parallel processing in Airflow
- Use caching for repeated operations

## 🔍 Troubleshooting

### Q: My DAGs aren't loading in Airflow. What's wrong?
**A:** Common causes:
1. Import errors - check `airflow dags list-import-errors`
2. Missing dependencies - verify `requirements.txt`
3. Path issues - ensure shared modules are accessible
4. Syntax errors - validate Python syntax

See [Airflow Troubleshooting Guide](airflow/docs/TROUBLESHOOTING.md) for detailed solutions.

### Q: The standalone parser fails with import errors. How to fix?
**A:** 
```bash
# Add shared modules to Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)/shared"

# Or install in development mode
pip install -e shared/
```

### Q: Docker containers won't start. What should I check?
**A:** 
1. **Port conflicts**: Check if ports 8080, 5432, 6379 are available
2. **Memory**: Ensure at least 8GB RAM available
3. **Permissions**: Check file permissions on volumes
4. **Docker version**: Use Docker 20.10+ and Compose 2.0+

### Q: Parsing fails with "File not found" errors
**A:** 
1. **File paths**: Use absolute paths or verify relative paths
2. **Permissions**: Ensure files are readable
3. **Docker volumes**: Check volume mounts are correct
4. **File encoding**: Ensure UTF-8 encoding

## 🔒 Security

### Q: Is this secure for production use?
**A:** Yes, with proper configuration:
- Use HTTPS for Airflow web interface
- Secure database connections
- Implement proper authentication
- Follow container security best practices

### Q: How do I secure the Airflow web interface?
**A:** 
- Change default admin credentials
- Enable HTTPS/TLS
- Configure authentication (LDAP, OAuth)
- Use role-based access control

### Q: Can I run this in an air-gapped environment?
**A:** Yes, but you'll need to:
- Pre-download all dependencies
- Use local Docker registries
- Configure offline ML model access

## 📈 Production Considerations

### Q: Is this production-ready?
**A:** Yes! Both deployment options are designed for production:
- Comprehensive error handling
- Logging and monitoring
- Docker deployment
- Scalability features

### Q: How do I monitor the system?
**A:** 
- **Standalone**: Log files and exit codes
- **Airflow**: Built-in UI, metrics, and alerting
- **Both**: Custom monitoring via APIs

### Q: What's the recommended backup strategy?
**A:** 
- **Data**: Regular backups of parsed definitions and models
- **Configuration**: Version control for all config files
- **Database**: Regular Airflow database backups (if using Airflow)

### Q: How do I handle updates and upgrades?
**A:** 
- Use semantic versioning
- Test in staging environment first
- Backup before upgrades
- Follow migration guides

## 🤝 Contributing & Support

### Q: How can I contribute?
**A:** We welcome contributions!
1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

See [CONTRIBUTING.md](CONTRIBUTING.md) for detailed guidelines.

### Q: Where can I get help?
**A:** 
- **Documentation**: Comprehensive guides in this repository
- **Issues**: GitHub issues for bugs and feature requests
- **Discussions**: GitHub discussions for questions
- **Community**: Join our community channels

### Q: Can I request new features?
**A:** Absolutely! Please:
1. Check existing issues first
2. Describe your use case clearly
3. Provide examples if possible
4. Consider contributing the implementation

### Q: Is commercial support available?
**A:** The project is open source. For commercial support:
- Check with the maintainers
- Consider hiring contributors
- Explore enterprise support options

## 🔮 Future Roadmap

### Q: What features are planned?
**A:** Upcoming features include:
- Additional ML model support
- More output formats
- Enhanced UI for Airflow
- Performance optimizations
- Cloud-native features

### Q: Will you support other input formats?
**A:** We're considering:
- GraphQL schemas
- Protocol Buffers
- Avro schemas
- Custom XML schemas

### Q: Can I influence the roadmap?
**A:** Yes! Your feedback shapes our priorities:
- Submit feature requests
- Participate in discussions
- Contribute implementations
- Share your use cases

---

## 📞 Still Have Questions?

If you don't find your answer here:

1. **Check the Documentation**: Comprehensive guides available
2. **Search Issues**: Your question might already be answered
3. **Ask the Community**: Start a discussion
4. **Report Bugs**: Create an issue with details

**We're here to help you succeed with service mapping!** 🎯
