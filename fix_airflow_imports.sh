#!/bin/bash
# Definitive fix for Airflow shared module import issues

set -e

echo "🔧 FIXING AIRFLOW SHARED MODULE IMPORTS"
echo "======================================="

# Check if we're in the right directory
if [ ! -f "airflow/docker-compose.yaml" ]; then
    echo "❌ Please run this script from the project root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: airflow/docker-compose.yaml, shared/"
    exit 1
fi

echo "✅ Running from correct directory: $(pwd)"

# Check if shared directory exists
if [ ! -d "shared" ]; then
    echo "❌ Shared directory not found at: $(pwd)/shared"
    exit 1
fi

echo "✅ Shared directory exists at: $(pwd)/shared"

# Step 1: Fix docker-compose.yaml volume mounts
echo ""
echo "🐳 Step 1: Fixing Docker volume mounts..."

if grep -q "/opt/airflow/shared" airflow/docker-compose.yaml; then
    echo "✅ Shared volume mount already exists in docker-compose.yaml"
else
    echo "⚠️  Adding shared volume mount to docker-compose.yaml..."
    
    # Backup original file
    cp airflow/docker-compose.yaml airflow/docker-compose.yaml.backup.$(date +%s)
    
    # Add volume mounts after the plugins line
    sed -i.tmp '/- ${AIRFLOW_PROJ_DIR:-.}\/plugins:\/opt\/airflow\/plugins/a\
    - ${AIRFLOW_PROJ_DIR:-.}/../shared:/opt/airflow/shared\
    - ${AIRFLOW_PROJ_DIR:-.}/../data:/opt/airflow/data' airflow/docker-compose.yaml
    
    # Remove temporary file
    rm -f airflow/docker-compose.yaml.tmp
    
    echo "✅ Volume mounts added to docker-compose.yaml"
fi

# Step 2: Stop and restart Airflow services
echo ""
echo "🔄 Step 2: Restarting Airflow services..."

cd airflow/

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    echo "⚠️  Stopping existing Airflow services..."
    docker-compose down
    sleep 5
fi

echo "🚀 Starting Airflow services with new configuration..."
docker-compose up -d

# Step 3: Wait for services to be ready
echo ""
echo "⏳ Step 3: Waiting for services to be ready..."
echo "   This may take 30-60 seconds..."

# Wait for webserver to be ready
for i in {1..12}; do
    if docker-compose exec -T airflow-webserver python -c "print('Ready')" 2>/dev/null; then
        echo "✅ Airflow webserver is ready"
        break
    fi
    echo "   Waiting... ($i/12)"
    sleep 5
done

# Step 4: Test the fix
echo ""
echo "🧪 Step 4: Testing the fix..."

echo "Test 1: Checking if shared directory is mounted..."
if docker-compose exec -T airflow-webserver test -d /opt/airflow/shared; then
    echo "✅ /opt/airflow/shared directory exists in container"
else
    echo "❌ /opt/airflow/shared directory NOT found in container"
    echo "   This indicates a volume mount issue"
    exit 1
fi

echo ""
echo "Test 2: Checking shared/common directory..."
if docker-compose exec -T airflow-webserver test -d /opt/airflow/shared/common; then
    echo "✅ /opt/airflow/shared/common directory exists"
else
    echo "❌ /opt/airflow/shared/common directory NOT found"
    exit 1
fi

echo ""
echo "Test 3: Testing Python import of shared modules..."
docker-compose exec -T airflow-webserver python -c "
import sys
import os

# Add shared to path
sys.path.insert(0, '/opt/airflow/shared')

print('Testing shared module imports...')

try:
    from shared.common import constants
    print('✅ Successfully imported shared.common.constants')
except Exception as e:
    print(f'❌ Failed to import shared.common.constants: {e}')
    sys.exit(1)

try:
    from shared.common import data_parsers
    print('✅ Successfully imported shared.common.data_parsers')
except Exception as e:
    print(f'❌ Failed to import shared.common.data_parsers: {e}')
    sys.exit(1)

try:
    from shared.common import validation_utils
    print('✅ Successfully imported shared.common.validation_utils')
except Exception as e:
    print(f'❌ Failed to import shared.common.validation_utils: {e}')
    sys.exit(1)

print('🎉 All shared module imports successful!')
"

if [ $? -ne 0 ]; then
    echo "❌ Shared module import test failed"
    exit 1
fi

echo ""
echo "Test 4: Checking DAG import errors..."
IMPORT_ERRORS=$(docker-compose exec -T airflow-webserver airflow dags list-import-errors 2>/dev/null)

if [ -z "$IMPORT_ERRORS" ]; then
    echo "✅ No DAG import errors found"
else
    echo "⚠️  DAG import errors found:"
    echo "$IMPORT_ERRORS"
    echo ""
    echo "If you see 'No module named shared' errors, the DAGs may need a few minutes to reload."
    echo "Try running: docker-compose restart airflow-webserver airflow-scheduler"
fi

echo ""
echo "Test 5: Listing DAGs..."
DAG_COUNT=$(docker-compose exec -T airflow-webserver airflow dags list 2>/dev/null | grep -c "_dag" || echo "0")
echo "✅ Found $DAG_COUNT DAGs in Airflow"

# Step 5: Final verification
echo ""
echo "🎯 Step 5: Final verification..."

echo "Checking if all expected DAGs are visible..."
EXPECTED_DAGS=(
    "01_ingest_and_parse_definitions"
    "02_generate_training_data"
    "03_train_ml_model"
    "04_operational_mapping_pipeline"
    "05_monitoring_and_retraining_trigger"
    "06_ingest_operational_logs"
)

for dag in "${EXPECTED_DAGS[@]}"; do
    if docker-compose exec -T airflow-webserver airflow dags list 2>/dev/null | grep -q "$dag"; then
        echo "✅ DAG found: $dag"
    else
        echo "⚠️  DAG not found: $dag"
    fi
done

echo ""
echo "🎉 AIRFLOW IMPORT FIX COMPLETE!"
echo "==============================="
echo ""
echo "✅ What was fixed:"
echo "   - Added shared directory volume mount to Docker"
echo "   - Restarted Airflow services"
echo "   - Verified shared module imports work"
echo ""
echo "🌐 Access Airflow UI:"
echo "   URL: http://localhost:8080"
echo "   Username: airflow"
echo "   Password: airflow"
echo ""
echo "🔍 To check for any remaining issues:"
echo "   docker-compose exec airflow-webserver airflow dags list-import-errors"
echo ""
echo "🔄 If you still see import errors:"
echo "   1. Wait 2-3 minutes for DAGs to reload"
echo "   2. Restart services: docker-compose restart"
echo "   3. Check logs: docker-compose logs airflow-webserver"

cd ..  # Return to project root
