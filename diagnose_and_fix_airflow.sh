#!/bin/bash
# Comprehensive diagnosis and fix for Airflow issues

set -e

echo "🔍 AIRFLOW DIAGNOSIS AND FIX"
echo "============================"

# Check if we're in the right directory
if [ ! -f "airflow/docker-compose.yaml" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

PROJECT_ROOT=$(pwd)
echo "Project root: $PROJECT_ROOT"

cd airflow/

echo ""
echo "📊 Step 1: Current Service Status"
echo "================================="
docker-compose ps

echo ""
echo "📋 Step 2: Checking Docker System"
echo "================================="
echo "Docker version:"
docker --version
echo ""
echo "Docker Compose version:"
docker-compose --version
echo ""
echo "Available disk space:"
df -h . | head -2

echo ""
echo "🧹 Step 3: Clean Restart"
echo "========================"
echo "Stopping all services..."
docker-compose down --volumes --remove-orphans

echo "Cleaning up Docker system..."
docker system prune -f

echo "Removing any orphaned volumes..."
docker volume prune -f

echo ""
echo "📁 Step 4: Checking Required Directories"
echo "========================================"
echo "Checking project structure..."

# Check if required directories exist
REQUIRED_DIRS=(
    "$PROJECT_ROOT/shared"
    "$PROJECT_ROOT/shared/common"
    "$PROJECT_ROOT/airflow/dags"
    "$PROJECT_ROOT/airflow/logs"
    "$PROJECT_ROOT/airflow/config"
    "$PROJECT_ROOT/airflow/plugins"
)

for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir exists"
    else
        echo "❌ $dir missing - creating..."
        mkdir -p "$dir"
    fi
done

# Check if data directory exists, create if not
if [ ! -d "$PROJECT_ROOT/data" ]; then
    echo "📁 Creating data directory..."
    mkdir -p "$PROJECT_ROOT/data"
fi

echo ""
echo "🔧 Step 5: Fixing docker-compose.yaml"
echo "====================================="

# Backup current file
cp docker-compose.yaml docker-compose.yaml.backup.$(date +%s)

# Create a minimal working docker-compose.yaml
cat > docker-compose.yaml << 'EOF'
# Basic Airflow cluster configuration for CeleryExecutor with Redis and PostgreSQL.
---
x-airflow-common:
  &airflow-common
  image: ${AIRFLOW_IMAGE_NAME:-apache/airflow:3.0.2}
  environment:
    &airflow-common-env
    AIRFLOW__CORE__EXECUTOR: CeleryExecutor
    AIRFLOW__CORE__AUTH_MANAGER: airflow.providers.fab.auth_manager.fab_auth_manager.FabAuthManager
    AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
    AIRFLOW__CELERY__RESULT_BACKEND: db+*********************************************
    AIRFLOW__CELERY__BROKER_URL: redis://:@redis:6379/0
    AIRFLOW__CORE__FERNET_KEY: ''
    AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true'
    AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
    AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK: 'true'
    _PIP_ADDITIONAL_REQUIREMENTS: ${_PIP_ADDITIONAL_REQUIREMENTS:-}
  volumes:
    - ${AIRFLOW_PROJ_DIR:-.}/dags:/opt/airflow/dags
    - ${AIRFLOW_PROJ_DIR:-.}/logs:/opt/airflow/logs
    - ${AIRFLOW_PROJ_DIR:-.}/config:/opt/airflow/config
    - ${AIRFLOW_PROJ_DIR:-.}/plugins:/opt/airflow/plugins
EOF

# Add the shared volume mount with the correct absolute path
echo "    - $PROJECT_ROOT/shared:/opt/airflow/shared" >> docker-compose.yaml
echo "    - $PROJECT_ROOT/data:/opt/airflow/data" >> docker-compose.yaml

# Add the rest of the configuration
cat >> docker-compose.yaml << 'EOF'
  user: "${AIRFLOW_UID:-50000}:0"
  depends_on:
    &airflow-common-depends-on
    redis:
      condition: service_healthy
    postgres:
      condition: service_healthy

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 10s
      retries: 5
      start_period: 5s
    restart: always

  redis:
    image: redis:7.2-bookworm
    expose:
      - 6379
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 30s
      retries: 50
      start_period: 30s
    restart: always

  airflow-webserver:
    <<: *airflow-common
    command: webserver
    ports:
      - "8080:8080"
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: always
    depends_on:
      <<: *airflow-common-depends-on
      airflow-init:
        condition: service_completed_successfully

  airflow-scheduler:
    <<: *airflow-common
    command: scheduler
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8974/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: always
    depends_on:
      <<: *airflow-common-depends-on
      airflow-init:
        condition: service_completed_successfully

  airflow-worker:
    <<: *airflow-common
    command: celery worker
    healthcheck:
      test:
        - "CMD-SHELL"
        - 'celery --app airflow.providers.celery.executors.celery_executor.app inspect ping -d "celery@$${HOSTNAME}" || celery --app airflow.executors.celery_executor.app inspect ping -d "celery@$${HOSTNAME}"'
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    environment:
      <<: *airflow-common-env
      DUMB_INIT_SETSID: "0"
    restart: always
    depends_on:
      <<: *airflow-common-depends-on
      airflow-webserver:
        condition: service_healthy
      airflow-init:
        condition: service_completed_successfully

  airflow-triggerer:
    <<: *airflow-common
    command: triggerer
    healthcheck:
      test: ["CMD-SHELL", 'airflow jobs check --job-type TriggererJob --hostname "$${HOSTNAME}"']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    restart: always
    depends_on:
      <<: *airflow-common-depends-on
      airflow-init:
        condition: service_completed_successfully

  airflow-init:
    <<: *airflow-common
    entrypoint: /bin/bash
    command:
      - -c
      - |
        mkdir -p /opt/airflow/{logs,dags,plugins,config}
        chown -R "${AIRFLOW_UID:-50000}:0" /opt/airflow/{logs,dags,plugins,config}
        exec /entrypoint airflow version
    environment:
      <<: *airflow-common-env
      _AIRFLOW_DB_MIGRATE: 'true'
      _AIRFLOW_WWW_USER_CREATE: 'true'
      _AIRFLOW_WWW_USER_USERNAME: ${_AIRFLOW_WWW_USER_USERNAME:-airflow}
      _AIRFLOW_WWW_USER_PASSWORD: ${_AIRFLOW_WWW_USER_PASSWORD:-airflow}
    user: "0:0"

volumes:
  postgres-db-volume:
EOF

echo "✅ Created new docker-compose.yaml with absolute paths"

echo ""
echo "🚀 Step 6: Starting Services"
echo "============================"
echo "Starting Airflow services..."
docker-compose up -d

echo ""
echo "⏳ Step 7: Waiting for Services"
echo "==============================="
echo "Waiting for services to start (this may take 2-3 minutes)..."

# Wait for init to complete
echo "Waiting for airflow-init to complete..."
docker-compose logs -f airflow-init &
LOGS_PID=$!

# Wait for init container to finish
while docker-compose ps airflow-init | grep -q "running\|starting"; do
    sleep 5
done

kill $LOGS_PID 2>/dev/null || true

echo ""
echo "Waiting for webserver to be ready..."
for i in {1..24}; do  # Wait up to 2 minutes
    if docker-compose ps airflow-webserver | grep -q "Up"; then
        echo "✅ Webserver is running"
        break
    fi
    echo "   Waiting... ($i/24)"
    sleep 5
done

echo ""
echo "🧪 Step 8: Testing the Fix"
echo "=========================="

# Check service status
echo "Service status:"
docker-compose ps

echo ""
echo "Testing shared directory mount..."
if docker-compose exec -T airflow-webserver test -d /opt/airflow/shared 2>/dev/null; then
    echo "✅ /opt/airflow/shared directory exists!"
    
    echo ""
    echo "Contents of /opt/airflow/shared:"
    docker-compose exec -T airflow-webserver ls -la /opt/airflow/shared/
    
    echo ""
    echo "Testing Python import..."
    docker-compose exec -T airflow-webserver python -c "
import sys
sys.path.insert(0, '/opt/airflow/shared')
try:
    from shared.common import constants
    print('✅ Successfully imported shared.common.constants')
except Exception as e:
    print(f'❌ Import failed: {e}')
" 2>/dev/null || echo "⚠️  Python test failed - webserver may still be starting"

else
    echo "❌ /opt/airflow/shared still not found"
    echo ""
    echo "🔍 Debugging information:"
    echo "Available directories in /opt/airflow:"
    docker-compose exec -T airflow-webserver ls -la /opt/airflow/ 2>/dev/null || echo "Cannot access container"
    
    echo ""
    echo "Webserver logs:"
    docker-compose logs --tail=20 airflow-webserver
fi

echo ""
echo "🏁 DIAGNOSIS COMPLETE"
echo "===================="
echo ""
echo "🌐 If services are running, access Airflow UI:"
echo "   URL: http://localhost:8080"
echo "   Username: airflow"
echo "   Password: airflow"
echo ""
echo "🔍 To check logs:"
echo "   docker-compose logs airflow-webserver"
echo "   docker-compose logs airflow-scheduler"

cd ..  # Return to project root
