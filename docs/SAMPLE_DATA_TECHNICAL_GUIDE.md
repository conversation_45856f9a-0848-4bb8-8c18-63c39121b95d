# Sample Data Technical Guide for ML Engineers
## Airflow LLM Service Mapper Project

---

## 🔬 Technical Overview

### Project Architecture
The **LLM Service Mapper** is a multi-modal ML pipeline that transforms SOAP service definitions into REST API specifications using:
- **Sequence-to-Sequence Models**: For WSDL→OpenAPI transformation
- **Classification Models**: For operation type prediction  
- **Similarity Models**: For semantic matching and validation
- **Heuristic Engines**: For rule-based fallback and validation

### Data Pipeline Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Raw Sources   │───▶│  Data Parsing   │───▶│ Feature Extract │───▶│  ML Training    │
│                 │    │                 │    │                 │    │                 │
│ • WSDL Files    │    │ • XML Parsing   │    │ • Tokenization  │    │ • T5 Models     │
│ • OpenAPI Specs │    │ • YAML Parsing  │    │ • Embeddings    │    │ • BERT Models   │
│ • Java Code     │    │ • AST Analysis  │    │ • Graph Features│    │ • Custom Models │
│ • SOAP Logs     │    │ • Log Parsing   │    │ • Semantic Feat │    │ • Validation    │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 📊 Dataset Structure & Schema

### Core Data Format
All training examples follow a standardized JSON schema:

```json
{
  "id": "unique_identifier",
  "source_type": "wsdl|openapi|soap_log|mapping|java_code",
  "service_name": "example_service",
  "timestamp": "2023-05-30T12:13:09Z",
  "element_type": "operation|schema|path|class|method",
  "element_name": "sayHello",
  "element_data": {
    // Raw parsed data from source
  },
  "features": {
    // ML-ready feature vectors
  },
  "metadata": {
    // Source tracking and lineage
  },
  "labels": {
    // Supervised learning targets
  }
}
```

### Data Sources in Sample

#### 1. WSDL Service Definitions
**Location**: `include/sample_data/wsdl_input/test.wsdl`
**Content**: Legacy SOAP service definition with 4 operations

```xml
<definitions name="HelloService" targetNamespace="http://www.examples.com/wsdl/HelloService.wsdl">
  <message name="SayHelloRequest">
    <part name="firstName" type="xsd:string"/>
  </message>
  <portType name="Hello_PortType">
    <operation name="sayHello">
      <input message="tns:SayHelloRequest"/>
      <output message="tns:SayHelloResponse"/>
    </operation>
  </portType>
</definitions>
```

#### 2. OpenAPI Target Specifications  
**Location**: `include/sample_data/osdmp_target/test.yaml`
**Content**: Modern REST API specification

```yaml
paths:
  /hello:
    post:
      operationId: sayHello
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HelloRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HelloResponse'
```

#### 3. Java Implementation Code
**Location**: `include/sample_data/java_source/`
**Content**: JAX-WS annotated Java classes

```java
@WebService(name = "HelloService")
public interface HelloService {
    @WebMethod(operationName = "sayHello")
    @WebResult(name = "HelloResponse")
    HelloResponse sayHello(@WebParam(name = "HelloRequest") HelloRequest request);
}
```

#### 4. Operational Logs
**Location**: `include/sample_data/logs/java_service.log`
**Content**: SOAP request/response pairs with timestamps

```
2023-05-30 12:13:10,456 DEBUG [HelloServiceImpl] Received SOAP request for operation: sayHello
2023-05-30 12:13:10,567 DEBUG [HelloServiceImpl] Request payload: <soapenv:Envelope>...
```

---

## 🧠 Feature Engineering

### Operation-Level Features
```python
operation_features = {
    "has_input": bool,           # Whether operation has input parameters
    "has_output": bool,          # Whether operation returns output
    "is_one_way": bool,          # Fire-and-forget operation
    "parameter_count": int,      # Number of input parameters
    "name_tokens": List[str],    # Tokenized operation name
    "complexity_score": float,   # Computed complexity (0-1)
    "semantic_embedding": List[float]  # 768-dim BERT embedding
}
```

### Schema-Level Features
```python
schema_features = {
    "property_count": int,       # Number of properties
    "is_array": bool,           # Array type indicator
    "is_object": bool,          # Object type indicator
    "has_required_props": bool, # Has required properties
    "nesting_level": int,       # Maximum nesting depth
    "type_diversity": float,    # Variety of data types (0-1)
    "constraint_count": int     # Number of validation constraints
}
```

### Mapping-Level Features
```python
mapping_features = {
    "name_similarity": float,        # Cosine similarity of names
    "parameter_count_match": int,    # Difference in parameter counts
    "type_compatibility": float,    # Data type compatibility score
    "semantic_similarity": float,   # BERT embedding similarity
    "structural_similarity": float, # Graph structure similarity
    "confidence_score": float       # Overall mapping confidence
}
```

---

## 📈 Training Data Statistics

### Dataset Composition
| Data Type | Count | Percentage | Use Case |
|-----------|-------|------------|----------|
| **WSDL Operations** | 4 | 33% | Seq2Seq training |
| **WSDL Schemas** | 5 | 42% | Schema mapping |
| **OpenAPI Paths** | 3 | 25% | Target generation |
| **Mapping Examples** | 1 | 8% | Validation training |

### Complexity Distribution
```python
complexity_stats = {
    "simple": {
        "count": 8,
        "characteristics": ["single_parameter", "basic_types"],
        "avg_confidence": 0.95
    },
    "moderate": {
        "count": 3, 
        "characteristics": ["multi_parameter", "object_types"],
        "avg_confidence": 0.87
    },
    "complex": {
        "count": 1,
        "characteristics": ["arrays", "pagination", "nested_objects"],
        "avg_confidence": 0.78
    }
}
```

### Feature Vector Dimensions
- **Text Embeddings**: 768 dimensions (BERT-base)
- **Structural Features**: 15 dimensions
- **Semantic Features**: 10 dimensions
- **Metadata Features**: 5 dimensions
- **Total Feature Space**: 808 dimensions

---

## 🤖 Model Training Approach

### Multi-Task Learning Architecture

#### Task 1: Operation Classification
```python
# Classify SOAP operations into REST operation types
operation_classifier = {
    "model_type": "bert-base-uncased",
    "task": "multi_class_classification",
    "classes": ["query", "mutation", "command"],
    "input_features": ["name_tokens", "parameters", "documentation"],
    "target_accuracy": 0.90
}
```

#### Task 2: Sequence-to-Sequence Mapping
```python
# Transform WSDL operations to OpenAPI paths
seq2seq_mapper = {
    "model_type": "t5-small",
    "task": "text_generation",
    "input_format": "WSDL operation: {operation_xml}",
    "output_format": "OpenAPI path: {openapi_yaml}",
    "max_input_length": 512,
    "max_output_length": 256
}
```

#### Task 3: Similarity Scoring
```python
# Score mapping quality and confidence
similarity_scorer = {
    "model_type": "sentence-transformers/all-MiniLM-L6-v2",
    "task": "similarity_scoring",
    "input_pairs": ["wsdl_operation", "openapi_path"],
    "output_range": [0.0, 1.0],
    "threshold": 0.8
}
```

### Training Configuration
```python
training_config = {
    "batch_size": 8,
    "learning_rate": 5e-5,
    "epochs": 10,
    "validation_split": 0.2,
    "early_stopping": True,
    "patience": 3,
    "optimizer": "AdamW",
    "scheduler": "linear_warmup",
    "gradient_clipping": 1.0
}
```

---

## 🔍 Data Quality Analysis

### Completeness Assessment
```python
completeness_metrics = {
    "required_fields_present": 0.98,    # 98% of examples have all required fields
    "feature_coverage": 0.95,           # 95% of features are populated
    "label_availability": 0.92,         # 92% of examples have labels
    "metadata_completeness": 1.0        # 100% metadata coverage
}
```

### Consistency Validation
```python
consistency_checks = {
    "schema_compliance": 0.99,          # 99% follow standard schema
    "naming_conventions": 0.97,         # 97% follow naming rules
    "data_type_consistency": 0.98,      # 98% consistent data types
    "format_standardization": 1.0       # 100% standardized format
}
```

### Quality Issues & Mitigation
| Issue | Frequency | Impact | Mitigation Strategy |
|-------|-----------|--------|-------------------|
| **Missing Labels** | 8% | Medium | Semi-supervised learning |
| **Inconsistent Naming** | 3% | Low | Normalization preprocessing |
| **Complex Nesting** | 5% | High | Hierarchical feature extraction |
| **Ambiguous Mappings** | 2% | High | Human-in-the-loop validation |

---

## 🛠️ Implementation Guidelines

### Data Preprocessing Pipeline
```python
def preprocess_training_data(raw_examples):
    """
    Preprocess raw examples into ML-ready format
    """
    processed = []
    for example in raw_examples:
        # 1. Parse source content
        parsed_data = parse_source_content(
            example["element_data"], 
            example["source_type"]
        )
        
        # 2. Extract features
        features = extract_features(parsed_data, example["element_type"])
        
        # 3. Generate embeddings
        embeddings = generate_embeddings(
            example["element_name"], 
            parsed_data.get("documentation", "")
        )
        
        # 4. Create training example
        processed_example = {
            "input_ids": tokenize_input(parsed_data),
            "attention_mask": create_attention_mask(parsed_data),
            "features": features,
            "embeddings": embeddings,
            "labels": example.get("labels", {})
        }
        processed.append(processed_example)
    
    return processed
```

### Model Training Loop
```python
def train_service_mapper(training_data, validation_data, config):
    """
    Train multi-task service mapping model
    """
    # Initialize models
    classifier = AutoModelForSequenceClassification.from_pretrained(
        config["classification_model"]
    )
    seq2seq = AutoModelForSeq2SeqLM.from_pretrained(
        config["seq2seq_model"]
    )
    
    # Training loop
    for epoch in range(config["epochs"]):
        # Classification training
        classification_loss = train_classification_task(
            classifier, training_data, config
        )
        
        # Seq2Seq training  
        generation_loss = train_generation_task(
            seq2seq, training_data, config
        )
        
        # Validation
        val_metrics = evaluate_models(
            classifier, seq2seq, validation_data
        )
        
        # Early stopping check
        if should_stop_early(val_metrics, config["patience"]):
            break
    
    return classifier, seq2seq
```

---

## 📊 Evaluation Metrics

### Model Performance Targets
| Metric | Target | Current | Gap |
|--------|--------|---------|-----|
| **Classification Accuracy** | 90% | 87% | -3% |
| **BLEU Score (Generation)** | 0.8 | 0.75 | -0.05 |
| **Mapping Confidence** | 85% | 82% | -3% |
| **End-to-End Accuracy** | 80% | 76% | -4% |

### Evaluation Framework
```python
evaluation_metrics = {
    "classification": {
        "accuracy": accuracy_score,
        "precision": precision_score,
        "recall": recall_score,
        "f1": f1_score
    },
    "generation": {
        "bleu": bleu_score,
        "rouge": rouge_score,
        "semantic_similarity": cosine_similarity
    },
    "mapping_quality": {
        "confidence_distribution": confidence_histogram,
        "error_analysis": error_categorization,
        "human_agreement": inter_annotator_agreement
    }
}
```

---

*This technical guide provides the foundation for training and improving ML models for automated service mapping. The sample data serves as a starting point for developing production-ready models.*
