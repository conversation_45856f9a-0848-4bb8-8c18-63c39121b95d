# Airflow Shared Module Import Fix

This document explains the fix for the `ModuleNotFoundError: No module named 'shared'` issue in Airflow DAGs.

## Problem

The Airflow DAGs were failing to import shared modules with the error:
```
ModuleNotFoundError: No module named 'shared'
```

This happened because:
1. The `shared` directory wasn't mounted in the Airflow Docker containers
2. The DAGs were trying to import from a path that didn't exist in the container

## Solution

### 1. Updated Docker Compose Configuration

**File**: `airflow/docker-compose.yaml`

Added volume mounts for shared modules and data:
```yaml
volumes:
  - ${AIRFLOW_PROJ_DIR:-.}/dags:/opt/airflow/dags
  - ${AIRFLOW_PROJ_DIR:-.}/logs:/opt/airflow/logs
  - ${AIRFLOW_PROJ_DIR:-.}/config:/opt/airflow/config
  - ${AIRFLOW_PROJ_DIR:-.}/plugins:/opt/airflow/plugins
  - ${AIRFLOW_PROJ_DIR:-.}/../shared:/opt/airflow/shared    # ← NEW
  - ${AIRFLOW_PROJ_DIR:-.}/../data:/opt/airflow/data        # ← NEW
```

### 2. Updated DAG Import Logic

**Files**: All DAG files (`01_*.py` through `06_*.py`)

Updated the shared module import logic to work in both Docker and local environments:

```python
# Import shared modules
import sys
import os

# Add shared directory to Python path
# In Docker container, shared is mounted at /opt/airflow/shared
shared_path = '/opt/airflow/shared'
if os.path.exists(shared_path) and shared_path not in sys.path:
    sys.path.append(shared_path)
else:
    # Fallback for local development (relative path)
    shared_path = os.path.join(os.path.dirname(__file__), '..', '..', 'shared')
    if os.path.exists(shared_path) and shared_path not in sys.path:
        sys.path.append(shared_path)

# Now import shared modules
from shared.common import constants, data_parsers, validation_utils
```

### 3. Updated Documentation

**File**: `airflow/docs/TROUBLESHOOTING.md`

Updated the troubleshooting guide with the new import pattern.

## Testing the Fix

### Option 1: Automated Test Script

Run the provided test script:
```bash
./test_docker_setup.sh
```

This script will:
- Start Airflow services
- Test shared module imports in the container
- Check DAG syntax
- List any import errors

### Option 2: Manual Testing

1. **Start Airflow**:
   ```bash
   cd airflow/
   docker-compose up -d
   ```

2. **Test shared imports**:
   ```bash
   docker-compose exec airflow-webserver python -c "
   import sys
   sys.path.append('/opt/airflow/shared')
   from shared.common import constants
   print('✅ Shared modules imported successfully')
   "
   ```

3. **Check DAG import errors**:
   ```bash
   docker-compose exec airflow-webserver airflow dags list-import-errors
   ```

4. **Access Airflow UI**:
   - URL: http://localhost:8080
   - Username: airflow
   - Password: airflow

### Option 3: Quick Syntax Check

Test DAG syntax without starting full services:
```bash
cd airflow/
docker-compose run --rm airflow-webserver python -c "
import sys
sys.path.append('/opt/airflow/shared')
import py_compile
py_compile.compile('/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py', doraise=True)
print('✅ DAG syntax OK')
"
```

## Verification

After applying the fix, you should see:

1. **No import errors** in Airflow DAG list:
   ```bash
   docker-compose exec airflow-webserver airflow dags list-import-errors
   # Should return empty or no errors related to shared modules
   ```

2. **DAGs visible** in Airflow UI:
   - All 6 DAGs should appear in the UI
   - No red error indicators

3. **Successful shared imports** in container:
   ```bash
   docker-compose exec airflow-webserver python -c "
   import sys; sys.path.append('/opt/airflow/shared')
   from shared.common import constants, data_parsers, validation_utils
   print('All imports successful')
   "
   ```

## File Structure

After the fix, the container file structure looks like:
```
/opt/airflow/
├── dags/                    # Airflow DAGs
│   ├── 01_ingest_and_parse_definitions_dag.py
│   ├── 02_generate_training_data_dag.py
│   ├── ...
├── shared/                  # ← Mounted shared modules
│   ├── common/
│   │   ├── constants.py
│   │   ├── data_parsers.py
│   │   ├── validation_utils.py
│   │   └── ...
│   └── ...
├── data/                    # ← Mounted data directory
│   └── parsed_definitions/
├── logs/
├── config/
└── plugins/
```

## Troubleshooting

### Issue: "shared directory not found"
**Solution**: Ensure you're running docker-compose from the `airflow/` directory and that the `../shared` path exists relative to it.

### Issue: "Permission denied"
**Solution**: Check file permissions:
```bash
sudo chown -R 50000:0 ./shared/
sudo chmod -R 755 ./shared/
```

### Issue: "DAGs still not loading"
**Solution**: 
1. Restart Airflow services:
   ```bash
   docker-compose restart airflow-webserver airflow-scheduler
   ```
2. Check container logs:
   ```bash
   docker-compose logs airflow-webserver
   ```

## Benefits of This Fix

1. **Dual Environment Support**: Works in both Docker and local development
2. **Robust Path Resolution**: Handles different deployment scenarios
3. **Backward Compatible**: Doesn't break existing functionality
4. **Clear Error Handling**: Provides fallback mechanisms
5. **Maintainable**: Easy to understand and modify

## Related Files

- `airflow/docker-compose.yaml` - Volume mounts
- `airflow/dags/*.py` - All DAG files with updated imports
- `airflow/docs/TROUBLESHOOTING.md` - Updated troubleshooting guide
- `test_docker_setup.sh` - Automated test script
