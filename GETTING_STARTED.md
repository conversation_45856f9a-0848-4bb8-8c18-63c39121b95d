# Getting Started with LLM Service Mapper

## 🎯 Choose Your Path

The LLM Service Mapper offers two deployment options. Choose based on your needs:

### 🚀 Quick Decision Matrix

| Use Case | Recommended Approach | Setup Time | Best For |
|----------|---------------------|------------|----------|
| **One-time conversion** | Standalone | 5 minutes | Development, Testing |
| **CI/CD integration** | Standalone | 10 minutes | Automation, Pipelines |
| **Production pipeline** | Airflow | 30 minutes | Enterprise, Scale |
| **ML model training** | Airflow | 45 minutes | Advanced ML workflows |
| **Batch processing** | Either | 5-30 minutes | Depends on scale |

## ⚡ Path A: Standalone Parser (5-Minute Quick Start)

**Perfect for**: Development, testing, CI/CD, one-time conversions

### Prerequisites
- Python 3.9+
- 4GB RAM minimum

### 1. Clone and Setup
```bash
# Clone repository
git clone <repository-url>
cd airflow-llm-service-mapper

# Navigate to standalone
cd standalone/

# Install dependencies
pip install -r requirements.txt
```

### 2. Verify Installation
```bash
# Run tests
python tests/test_standalone_parser.py

# Check help
python src/standalone_ingest_parser.py --help
```

### 3. Quick Example
```bash
# Process sample data
python src/standalone_ingest_parser.py \
  --service-name sample_todo_service \
  --wsdl-uri ../shared/sample_data/wsdl_input/todo-service.wsdl \
  --openapi-uri ../shared/sample_data/osdmp_target/todo-api.yaml

# Check output
ls -la data/sample_todo_service/
```

### 4. Your First Service
```bash
# Process your own files
python src/standalone_ingest_parser.py \
  --service-name my_service \
  --wsdl-uri /path/to/your/service.wsdl \
  --openapi-uri /path/to/your/api.yaml \
  --output-base-path ./output
```

### ✅ Standalone Complete!
You're now ready to parse WSDL and OpenAPI files. Check the output in your specified directory.

**Next Steps**:
- Read [standalone/docs/README_standalone.md](standalone/docs/README_standalone.md) for advanced usage
- Try Docker deployment: [standalone/docs/DEPLOYMENT_OPTIONS.md](standalone/docs/DEPLOYMENT_OPTIONS.md)

---

## 🔄 Path B: Full Airflow Pipeline (30-Minute Setup)

**Perfect for**: Production environments, ML workflows, team collaboration

### Prerequisites
- Docker & Docker Compose
- 8GB RAM minimum
- 20GB free disk space

### 1. Clone and Navigate
```bash
# Clone repository
git clone <repository-url>
cd airflow-llm-service-mapper/airflow
```

### 2. Environment Setup
```bash
# Create environment file
cp .env.example .env

# Generate Fernet key
python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"

# Add the key to .env file
echo "AIRFLOW__CORE__FERNET_KEY=<your-generated-key>" >> .env
echo "AIRFLOW_UID=$(id -u)" >> .env
```

### 3. Start Airflow
```bash
# Initialize database
docker-compose up airflow-init

# Start all services
docker-compose up -d

# Check status
docker-compose ps
```

### 4. Access Airflow UI
- **URL**: http://localhost:8080
- **Username**: admin
- **Password**: admin

### 5. Verify Installation
```bash
# Check DAGs loaded
docker-compose exec airflow-webserver airflow dags list

# Check for import errors
docker-compose exec airflow-webserver airflow dags list-import-errors
```

### 6. Run Your First DAG
1. Open Airflow UI: http://localhost:8080
2. Navigate to DAGs page
3. Find "01_ingest_and_parse_definitions"
4. Click the play button to trigger
5. Fill in parameters:
   - **service_name**: `my_first_service`
   - **wsdl_uri**: `/opt/airflow/shared/sample_data/wsdl_input/todo-service.wsdl`
   - **openapi_uri**: `/opt/airflow/shared/sample_data/osdmp_target/todo-api.yaml`
6. Click "Trigger DAG"

### 7. Monitor Execution
- Watch the DAG run in the Graph view
- Check task logs for details
- Verify output in the data directory

### ✅ Airflow Complete!
You now have a full MLOps pipeline running. The DAGs will process your service definitions and prepare them for ML training.

**Next Steps**:
- Read [airflow/docs/README_AIRFLOW.md](airflow/docs/README_AIRFLOW.md) for detailed usage
- Explore [airflow/docs/DAG_REFERENCE.md](airflow/docs/DAG_REFERENCE.md) for all DAG capabilities
- Set up monitoring: [airflow/docs/MONITORING_GUIDE.md](airflow/docs/MONITORING_GUIDE.md)

---

## 🐳 Docker Quick Start (Both Paths)

### Standalone Docker
```bash
# Build and run standalone
cd standalone/
docker build -t standalone-parser:latest .

# Process sample data
docker run --rm \
  -v $(pwd)/../shared/sample_data:/app/data:ro \
  -v $(pwd)/output:/app/output:rw \
  standalone-parser:latest \
  python src/standalone_ingest_parser.py \
    --service-name docker_sample \
    --wsdl-uri /app/data/wsdl_input/todo-service.wsdl \
    --openapi-uri /app/data/osdmp_target/todo-api.yaml \
    --output-base-path /app/output
```

### Airflow Docker
```bash
# Start Airflow with Docker
cd airflow/
docker-compose up -d

# Access UI at http://localhost:8080
```

---

## 🔧 Development Setup

### For Contributors
```bash
# Clone repository
git clone <repository-url>
cd airflow-llm-service-mapper

# Install development dependencies
pip install -r requirements-test.txt

# Set up pre-commit hooks
pre-commit install

# Run tests
pytest

# Run linting
flake8 shared/ standalone/src/
black --check shared/ standalone/src/
isort --check-only shared/ standalone/src/
```

### IDE Setup
- **VS Code**: Install Python, Docker, and YAML extensions
- **PyCharm**: Configure Python interpreter and Docker integration
- **Vim/Neovim**: Install Python LSP and Docker plugins

---

## 🚨 Troubleshooting Quick Fixes

### Standalone Issues
```bash
# Import errors
export PYTHONPATH="${PYTHONPATH}:$(pwd)/shared"

# Permission errors
chmod +x standalone/run_parser.sh

# Missing dependencies
pip install -r standalone/requirements.txt
```

### Airflow Issues
```bash
# DAGs not loading
docker-compose exec airflow-webserver airflow dags list-import-errors

# Web UI not accessible
docker-compose restart airflow-webserver

# Database issues
docker-compose down -v
docker-compose up airflow-init
docker-compose up -d
```

### Docker Issues
```bash
# Port conflicts
docker-compose down
# Edit docker-compose.yml to change ports
docker-compose up -d

# Permission issues
sudo chown -R $(id -u):$(id -g) ./data ./logs

# Out of space
docker system prune -f
```

---

## 📚 What's Next?

### Learning Path
1. **Start Simple**: Use standalone parser with sample data
2. **Explore Features**: Try different parsing options and outputs
3. **Scale Up**: Move to Airflow for production workflows
4. **Customize**: Extend with your own parsers and models
5. **Contribute**: Submit improvements and new features

### Key Resources
- **[Documentation Hub](README.md#-documentation)**: Complete documentation index
- **[Sample Data Guide](docs/SAMPLE_DATA_TECHNICAL_GUIDE.md)**: Understanding sample data
- **[Architecture Guide](docs/DEPLOYMENT_ARCHITECTURE_GUIDE.md)**: System architecture
- **[Troubleshooting](airflow/docs/TROUBLESHOOTING.md)**: Common issues and solutions

### Community
- **Issues**: Report bugs and request features
- **Discussions**: Ask questions and share experiences
- **Contributions**: Submit pull requests and improvements

---

## 🎯 Success Checklist

### Standalone Success
- [ ] Repository cloned and dependencies installed
- [ ] Sample data processed successfully
- [ ] Output files generated and validated
- [ ] Your own WSDL/OpenAPI files processed
- [ ] Docker deployment working (optional)

### Airflow Success
- [ ] Docker Compose environment running
- [ ] Airflow UI accessible at http://localhost:8080
- [ ] All DAGs loaded without import errors
- [ ] First DAG execution completed successfully
- [ ] Output data generated in expected format
- [ ] Monitoring and logs accessible

### Development Success
- [ ] Test suite running successfully
- [ ] Code quality checks passing
- [ ] Pre-commit hooks installed
- [ ] IDE configured for Python development
- [ ] Documentation reviewed and understood

---

**🎉 Congratulations! You're now ready to transform SOAP services to REST APIs using machine learning!**

Choose your path, follow the steps, and start mapping services. The comprehensive documentation will guide you through advanced features and production deployment.
