#!/bin/bash
# Convenience script for cleaning up parsed service definition data

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [COMMAND] [OPTIONS]

COMMANDS:
    summary                     Show current data summary
    clean-all                   Remove all parsed data
    clean-services <names>      Remove specific services
    clean-old [--keep N]        Remove old versions (keep latest N, default: 3)
    clean-orphaned             Remove orphaned data
    
OPTIONS:
    --dry-run                   Show what would be deleted without deleting
    --force                     Skip confirmation prompts
    --base-path PATH            Custom base path (default: data/parsed_definitions)
    --catalog-path PATH         Custom catalog path (default: standalone/service_catalog.json)
    --log-level LEVEL           Set log level (DEBUG, INFO, WARNING, ERROR)

EXAMPLES:
    $0 summary                                    # Show data summary
    $0 clean-all --dry-run                       # Preview cleaning all data
    $0 clean-services service1 service2          # Clean specific services
    $0 clean-old --keep 2                        # Keep only latest 2 versions
    $0 clean-orphaned                            # Remove orphaned data
    $0 clean-all --force                         # Clean all without confirmation

EOF
}

# Check if Python script exists
SCRIPT_PATH="cleanup_parsed_data.py"
if [ ! -f "$SCRIPT_PATH" ]; then
    print_error "Cleanup script not found: $SCRIPT_PATH"
    print_info "Make sure you're running this from the project root directory."
    exit 1
fi

# Check if no arguments provided
if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

# Parse command
COMMAND="$1"
shift

# Build Python command arguments
PYTHON_ARGS=()

case "$COMMAND" in
    "summary")
        PYTHON_ARGS+=("--summary")
        ;;
    "clean-all")
        PYTHON_ARGS+=("--clean-all")
        ;;
    "clean-services")
        if [ $# -eq 0 ]; then
            print_error "clean-services requires at least one service name"
            exit 1
        fi
        
        # Collect service names until we hit an option
        SERVICES=()
        while [ $# -gt 0 ] && [[ ! "$1" =~ ^-- ]]; do
            SERVICES+=("$1")
            shift
        done
        
        if [ ${#SERVICES[@]} -eq 0 ]; then
            print_error "No service names provided"
            exit 1
        fi
        
        PYTHON_ARGS+=("--clean-services" "${SERVICES[@]}")
        ;;
    "clean-old")
        PYTHON_ARGS+=("--clean-old-versions")
        ;;
    "clean-orphaned")
        PYTHON_ARGS+=("--clean-orphaned")
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac

# Parse remaining options
while [ $# -gt 0 ]; do
    case "$1" in
        --dry-run)
            PYTHON_ARGS+=("--dry-run")
            shift
            ;;
        --force)
            PYTHON_ARGS+=("--force")
            shift
            ;;
        --keep)
            if [ $# -lt 2 ]; then
                print_error "--keep requires a number"
                exit 1
            fi
            PYTHON_ARGS+=("--keep" "$2")
            shift 2
            ;;
        --base-path)
            if [ $# -lt 2 ]; then
                print_error "--base-path requires a path"
                exit 1
            fi
            PYTHON_ARGS+=("--base-path" "$2")
            shift 2
            ;;
        --catalog-path)
            if [ $# -lt 2 ]; then
                print_error "--catalog-path requires a path"
                exit 1
            fi
            PYTHON_ARGS+=("--catalog-path" "$2")
            shift 2
            ;;
        --log-level)
            if [ $# -lt 2 ]; then
                print_error "--log-level requires a level"
                exit 1
            fi
            PYTHON_ARGS+=("--log-level" "$2")
            shift 2
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Execute the Python script
print_info "Executing: python $SCRIPT_PATH ${PYTHON_ARGS[*]}"
python "$SCRIPT_PATH" "${PYTHON_ARGS[@]}"

# Check exit status
if [ $? -eq 0 ]; then
    print_success "Cleanup operation completed successfully"
else
    print_error "Cleanup operation failed"
    exit 1
fi
