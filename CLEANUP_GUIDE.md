# Parsed Data Cleanup Guide

This guide explains how to use the cleanup utilities to manage parsed service definition data generated by the SOADA Airflow MVP project.

## Overview

The cleanup utilities help you manage the parsed data stored in `data/parsed_definitions/` and the service catalog. They provide several cleanup strategies:

- **Clean all data**: Remove everything and reset the service catalog
- **Clean specific services**: Remove data for selected services only  
- **Clean old versions**: Keep only the latest N versions of each service
- **Clean orphaned data**: Remove data not referenced in the service catalog
- **Dry run mode**: Preview changes without actually deleting anything

## Files

- `cleanup_parsed_data.py` - Main Python cleanup script with full functionality
- `cleanup.sh` - Convenient shell wrapper script
- `CLEANUP_GUIDE.md` - This documentation

## Quick Start

### Show Current Data Summary

```bash
# Using shell script (recommended)
./cleanup.sh summary

# Using Python script directly
python cleanup_parsed_data.py --summary
```

### Clean All Data (with confirmation)

```bash
# Preview what would be deleted
./cleanup.sh clean-all --dry-run

# Actually clean all data
./cleanup.sh clean-all
```

### Clean Specific Services

```bash
# Clean specific services
./cleanup.sh clean-services service1 service2

# With dry run
./cleanup.sh clean-services service1 service2 --dry-run
```

### Clean Old Versions

```bash
# Keep only latest 3 versions (default)
./cleanup.sh clean-old

# Keep only latest 2 versions
./cleanup.sh clean-old --keep 2

# Preview changes
./cleanup.sh clean-old --keep 1 --dry-run
```

### Clean Orphaned Data

```bash
# Remove data not in service catalog
./cleanup.sh clean-orphaned

# Preview changes
./cleanup.sh clean-orphaned --dry-run
```

## Command Reference

### Shell Script (`cleanup.sh`)

```bash
./cleanup.sh [COMMAND] [OPTIONS]
```

**Commands:**
- `summary` - Show current data summary
- `clean-all` - Remove all parsed data
- `clean-services <names>` - Remove specific services
- `clean-old [--keep N]` - Remove old versions (keep latest N, default: 3)
- `clean-orphaned` - Remove orphaned data

**Options:**
- `--dry-run` - Show what would be deleted without deleting
- `--force` - Skip confirmation prompts
- `--base-path PATH` - Custom base path (default: data/parsed_definitions)
- `--catalog-path PATH` - Custom catalog path (default: standalone/service_catalog.json)
- `--log-level LEVEL` - Set log level (DEBUG, INFO, WARNING, ERROR)

### Python Script (`cleanup_parsed_data.py`)

```bash
python cleanup_parsed_data.py [ACTION] [OPTIONS]
```

**Actions (mutually exclusive):**
- `--summary` - Display summary of current parsed data
- `--clean-all` - Remove all parsed data and reset service catalog
- `--clean-services SERVICE [SERVICE ...]` - Remove data for specific services
- `--clean-old-versions` - Remove old versions, keeping only the latest N versions
- `--clean-orphaned` - Remove data not referenced in service catalog

**Options:**
- `--base-path PATH` - Base directory containing parsed definitions
- `--catalog-path PATH` - Path to service catalog JSON file
- `--keep N` - Number of versions to keep when using --clean-old-versions (default: 3)
- `--dry-run` - Show what would be deleted without actually deleting
- `--force` - Skip confirmation prompts (use with caution)
- `--log-level LEVEL` - Set logging level (DEBUG, INFO, WARNING, ERROR)

## Examples

### Basic Usage

```bash
# Check what data exists
./cleanup.sh summary

# Clean everything (with confirmation)
./cleanup.sh clean-all

# Clean specific services
./cleanup.sh clean-services test_service old_service

# Keep only latest version of each service
./cleanup.sh clean-old --keep 1
```

### Advanced Usage

```bash
# Use custom paths
./cleanup.sh summary --base-path /custom/data --catalog-path /custom/catalog.json

# Force cleanup without confirmation (dangerous!)
./cleanup.sh clean-all --force

# Detailed logging
./cleanup.sh clean-old --log-level DEBUG

# Preview complex cleanup
./cleanup.sh clean-old --keep 2 --dry-run --log-level DEBUG
```

### Maintenance Workflows

```bash
# Weekly cleanup: keep latest 5 versions
./cleanup.sh clean-old --keep 5

# Monthly cleanup: remove orphaned data
./cleanup.sh clean-orphaned

# Before major changes: backup and clean
./cleanup.sh summary > data_summary_backup.txt
./cleanup.sh clean-old --keep 3
```

## Data Structure

The cleanup utilities work with this data structure:

```
data/parsed_definitions/
├── service_name_1/
│   ├── v1751563876/           # Versioned output
│   │   ├── parsed_wsdl.json
│   │   ├── parsed_wsdl_types.json
│   │   ├── parsed_xsd.json
│   │   ├── parsed_xsd_types.json
│   │   ├── parsed_openapi.json
│   │   ├── parsed_openapi_types.json
│   │   ├── parsed_java_source.json
│   │   └── manifest.json
│   ├── v1751563000/           # Older version
│   └── latest/                # Symlink to latest version
└── service_name_2/
    └── ...

standalone/service_catalog.json   # Service registry
```

## Safety Features

- **Confirmation prompts**: Destructive operations require confirmation unless `--force` is used
- **Dry run mode**: Preview changes with `--dry-run` before actual execution
- **Detailed logging**: See exactly what files/directories are being processed
- **Size reporting**: Shows how much space will be freed
- **Catalog consistency**: Automatically updates service catalog when cleaning data

## Troubleshooting

### Permission Errors
```bash
# Make sure scripts are executable
chmod +x cleanup.sh
chmod +x cleanup_parsed_data.py
```

### Path Issues
```bash
# Run from project root directory
cd /path/to/soada_airflow_mvp
./cleanup.sh summary
```

### Custom Paths
```bash
# Use absolute paths if needed
./cleanup.sh summary --base-path /absolute/path/to/data --catalog-path /absolute/path/to/catalog.json
```

### Recovery
If you accidentally delete data:
- Check if you have backups
- Re-run the parser to regenerate data
- Use version control to restore files if they were committed

## Integration

### With Airflow
The cleanup utilities work with data generated by the Airflow DAG. The service catalog format is compatible with both standalone and Airflow environments.

### With CI/CD
```bash
# In CI/CD pipeline
./cleanup.sh clean-old --keep 3 --force --log-level INFO
```

### Scheduled Cleanup
```bash
# Add to crontab for weekly cleanup
0 2 * * 0 cd /path/to/project && ./cleanup.sh clean-old --keep 5 --force
```
