#!/usr/bin/env python3
"""
Simple test script for the cleanup utilities.

This script creates some test data and verifies that the cleanup utilities
work correctly in different scenarios.
"""

import os
import json
import shutil
import tempfile
from pathlib import Path
import subprocess
import sys

def create_test_data(base_path: Path, catalog_path: Path):
    """Create test data structure for testing cleanup utilities."""
    print("Creating test data structure...")
    
    # Create base directory
    base_path.mkdir(parents=True, exist_ok=True)
    
    # Create test services with multiple versions
    services = {
        "test_service_1": ["v1000000001", "v1000000002", "v1000000003"],
        "test_service_2": ["v1000000001", "v1000000002"],
        "orphaned_service": ["v1000000001"]  # This won't be in catalog
    }
    
    catalog = {
        "services": {},
        "last_updated": "2025-07-04T00:00:00.000000"
    }
    
    for service_name, versions in services.items():
        service_dir = base_path / service_name
        service_dir.mkdir(exist_ok=True)
        
        for version in versions:
            version_dir = service_dir / version
            version_dir.mkdir(exist_ok=True)
            
            # Create test files
            test_files = [
                "parsed_wsdl.json",
                "parsed_wsdl_types.json", 
                "parsed_openapi.json",
                "parsed_openapi_types.json",
                "manifest.json"
            ]
            
            for filename in test_files:
                file_path = version_dir / filename
                with open(file_path, 'w') as f:
                    json.dump({"test": "data", "file": filename}, f)
        
        # Create latest directory (copy of latest version)
        latest_dir = service_dir / "latest"
        if latest_dir.exists():
            shutil.rmtree(latest_dir)
        shutil.copytree(service_dir / versions[-1], latest_dir)
        
        # Add to catalog (except orphaned_service)
        if service_name != "orphaned_service":
            latest_version = versions[-1]
            catalog["services"][service_name] = {
                "service_name": service_name,
                "version": int(latest_version[1:]),  # Remove 'v' prefix
                "last_updated": "2025-07-04T00:00:00.000000",
                "files": {
                    "wsdl": f"../data/test_parsed_definitions/{service_name}/{latest_version}/parsed_wsdl.json",
                    "wsdl_types": f"../data/test_parsed_definitions/{service_name}/{latest_version}/parsed_wsdl_types.json",
                    "openapi": f"../data/test_parsed_definitions/{service_name}/{latest_version}/parsed_openapi.json",
                    "openapi_types": f"../data/test_parsed_definitions/{service_name}/{latest_version}/parsed_openapi_types.json",
                    "manifest": f"../data/test_parsed_definitions/{service_name}/{latest_version}/manifest.json"
                },
                "status": "available"
            }
    
    # Save catalog
    catalog_path.parent.mkdir(parents=True, exist_ok=True)
    with open(catalog_path, 'w') as f:
        json.dump(catalog, f, indent=2)
    
    print(f"Created test data in {base_path}")
    print(f"Created test catalog in {catalog_path}")

def run_cleanup_command(args: list, base_path: Path, catalog_path: Path) -> tuple:
    """Run cleanup command and return (return_code, stdout, stderr)."""
    cmd = [
        sys.executable, "cleanup_parsed_data.py",
        "--base-path", str(base_path),
        "--catalog-path", str(catalog_path)
    ] + args
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    return result.returncode, result.stdout, result.stderr

def test_summary(base_path: Path, catalog_path: Path):
    """Test the summary functionality."""
    print("\n=== Testing Summary ===")
    
    return_code, stdout, stderr = run_cleanup_command(["--summary"], base_path, catalog_path)
    
    assert return_code == 0, f"Summary failed: {stderr}"
    assert "test_service_1: 3 versions" in stdout
    assert "test_service_2: 2 versions" in stdout
    assert "orphaned_service: 1 versions" in stdout
    assert "⚠ Orphaned" in stdout
    
    print("✓ Summary test passed")

def test_clean_orphaned(base_path: Path, catalog_path: Path):
    """Test cleaning orphaned data."""
    print("\n=== Testing Clean Orphaned (Dry Run) ===")
    
    return_code, stdout, stderr = run_cleanup_command(["--clean-orphaned", "--dry-run"], base_path, catalog_path)
    
    assert return_code == 0, f"Clean orphaned failed: {stderr}"
    assert "orphaned_service" in stdout
    assert "DRY RUN" in stdout
    
    # Verify orphaned service still exists (dry run)
    assert (base_path / "orphaned_service").exists()
    
    print("✓ Clean orphaned (dry run) test passed")

def test_clean_old_versions(base_path: Path, catalog_path: Path):
    """Test cleaning old versions."""
    print("\n=== Testing Clean Old Versions (Keep 2) ===")
    
    return_code, stdout, stderr = run_cleanup_command(["--clean-old-versions", "--keep", "2", "--dry-run"], base_path, catalog_path)
    
    assert return_code == 0, f"Clean old versions failed: {stderr}"
    assert "test_service_1" in stdout  # Should have versions to remove
    assert "DRY RUN" in stdout
    
    print("✓ Clean old versions test passed")

def test_clean_specific_services(base_path: Path, catalog_path: Path):
    """Test cleaning specific services."""
    print("\n=== Testing Clean Specific Services ===")
    
    return_code, stdout, stderr = run_cleanup_command(["--clean-services", "orphaned_service", "--dry-run"], base_path, catalog_path)
    
    assert return_code == 0, f"Clean specific services failed: {stderr}"
    assert "orphaned_service" in stdout
    assert "DRY RUN" in stdout
    
    print("✓ Clean specific services test passed")

def main():
    """Run all tests."""
    print("Starting cleanup utility tests...")
    
    # Create temporary directories for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        base_path = temp_path / "test_parsed_definitions"
        catalog_path = temp_path / "test_catalog.json"
        
        # Create test data
        create_test_data(base_path, catalog_path)
        
        # Run tests
        try:
            test_summary(base_path, catalog_path)
            test_clean_orphaned(base_path, catalog_path)
            test_clean_old_versions(base_path, catalog_path)
            test_clean_specific_services(base_path, catalog_path)
            
            print("\n🎉 All tests passed!")
            
        except AssertionError as e:
            print(f"\n❌ Test failed: {e}")
            sys.exit(1)
        except Exception as e:
            print(f"\n💥 Unexpected error: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
