#!/bin/bash
# Fix Airflow volume mount issues with absolute paths

set -e

echo "🔧 FIXING AIRFLOW VOLUME MOUNT ISSUES"
echo "====================================="

# Get the absolute path to the project root
PROJECT_ROOT=$(pwd)
echo "Project root: $PROJECT_ROOT"

# Check if we're in the right directory
if [ ! -f "airflow/docker-compose.yaml" ]; then
    echo "❌ Please run this script from the project root directory"
    echo "   Current directory: $PROJECT_ROOT"
    echo "   Expected files: airflow/docker-compose.yaml, shared/"
    exit 1
fi

# Check if shared directory exists
if [ ! -d "shared" ]; then
    echo "❌ Shared directory not found at: $PROJECT_ROOT/shared"
    exit 1
fi

echo "✅ Shared directory exists at: $PROJECT_ROOT/shared"

# Step 1: Stop all Airflow services
echo ""
echo "🛑 Step 1: Stopping all Airflow services..."
cd airflow/
docker-compose down --volumes --remove-orphans
sleep 5

# Step 2: Update docker-compose.yaml with absolute paths
echo ""
echo "📝 Step 2: Updating docker-compose.yaml with absolute paths..."

# Backup the original file
cp docker-compose.yaml docker-compose.yaml.backup.$(date +%s)

# Create a new docker-compose.yaml with absolute paths
cat > docker-compose.yaml.new << EOF
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

# Basic Airflow cluster configuration for CeleryExecutor with Redis and PostgreSQL.
#
# WARNING: This configuration is for local development. Do not use it in a production deployment.
#
# This configuration supports basic configuration using environment variables or an .env file
# The following variables are supported:
#
# AIRFLOW_IMAGE_NAME           - Docker image name used to run Airflow.
#                                Default: apache/airflow:3.0.2
# AIRFLOW_UID                  - User ID in Airflow containers
#                                Default: 50000
# AIRFLOW_PROJ_DIR             - Base path to which all the files will be volumed.
#                                Default: .
# Those configurations are useful mostly in case of standalone testing/running Airflow in test/try-out mode
#
# _AIRFLOW_WWW_USER_USERNAME   - Username for the administrator account (if requested).
#                                Default: airflow
# _AIRFLOW_WWW_USER_PASSWORD   - Password for the administrator account (if requested).
#                                Default: airflow
# _PIP_ADDITIONAL_REQUIREMENTS - Additional PIP requirements to add when starting all containers.
#                                Use this option ONLY for quick checks. Installing requirements at container
#                                startup is done EVERY TIME the service is started.
#                                A better way is to build a custom image or extend the official image
#                                as described in https://airflow.apache.org/docs/docker-stack/build.html.
#                                Default: ''
#
# Feel free to modify this file to suit your needs.
---
x-airflow-common:
  &airflow-common
  # In order to add custom dependencies or upgrade provider distributions you can use your extended image.
  # Comment the image line, place your Dockerfile in the directory where you placed the docker-compose.yaml
  # and uncomment the "build" line below, Then run \`docker-compose build\` to build the images.
  image: \${AIRFLOW_IMAGE_NAME:-apache/airflow:3.0.2}
  # build: .
  environment:
    &airflow-common-env
    AIRFLOW__CORE__EXECUTOR: CeleryExecutor
    AIRFLOW__CORE__AUTH_MANAGER: airflow.providers.fab.auth_manager.fab_auth_manager.FabAuthManager
    AIRFLOW__DATABASE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
    AIRFLOW__CELERY__RESULT_BACKEND: db+*********************************************
    AIRFLOW__CELERY__BROKER_URL: redis://:@redis:6379/0
    AIRFLOW__CORE__FERNET_KEY: ''
    AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true'
    AIRFLOW__CORE__LOAD_EXAMPLES: 'true'
    AIRFLOW__CORE__EXECUTION_API_SERVER_URL: 'http://airflow-apiserver:8080/execution/'
    # yamllint disable rule:line-length
    # Use simple http server on scheduler for health checks
    # See https://airflow.apache.org/docs/apache-airflow/stable/administration-and-deployment/logging-monitoring/check-health.html#scheduler-health-check-server
    # yamllint enable rule:line-length
    AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK: 'true'
    # WARNING: Use _PIP_ADDITIONAL_REQUIREMENTS option ONLY for a quick checks
    # for other purpose (development, test and especially production usage) build/extend Airflow image.
    _PIP_ADDITIONAL_REQUIREMENTS: \${_PIP_ADDITIONAL_REQUIREMENTS:-}
    # The following line can be used to set a custom config file, stored in the local config folder
    AIRFLOW_CONFIG: '/opt/airflow/config/airflow.cfg'
  volumes:
    - \${AIRFLOW_PROJ_DIR:-.}/dags:/opt/airflow/dags
    - \${AIRFLOW_PROJ_DIR:-.}/logs:/opt/airflow/logs
    - \${AIRFLOW_PROJ_DIR:-.}/config:/opt/airflow/config
    - \${AIRFLOW_PROJ_DIR:-.}/plugins:/opt/airflow/plugins
    - $PROJECT_ROOT/shared:/opt/airflow/shared
    - $PROJECT_ROOT/data:/opt/airflow/data
  user: "\${AIRFLOW_UID:-50000}:0"
  depends_on:
    &airflow-common-depends-on
    redis:
      condition: service_healthy
    postgres:
      condition: service_healthy
EOF

# Copy the rest of the original file (services section)
tail -n +88 docker-compose.yaml >> docker-compose.yaml.new

# Replace the original file
mv docker-compose.yaml.new docker-compose.yaml

echo "✅ Updated docker-compose.yaml with absolute paths"
echo "   Shared: $PROJECT_ROOT/shared -> /opt/airflow/shared"
echo "   Data:   $PROJECT_ROOT/data -> /opt/airflow/data"

# Step 3: Start services
echo ""
echo "🚀 Step 3: Starting Airflow services..."
docker-compose up -d

# Step 4: Wait for services
echo ""
echo "⏳ Step 4: Waiting for services to be ready..."
sleep 30

# Step 5: Test the fix
echo ""
echo "🧪 Step 5: Testing the volume mount fix..."

echo "Test 1: Checking if shared directory is mounted..."
if docker-compose exec -T airflow-webserver test -d /opt/airflow/shared; then
    echo "✅ /opt/airflow/shared directory exists in container"
else
    echo "❌ /opt/airflow/shared directory still NOT found"
    echo "   Checking what's in /opt/airflow/..."
    docker-compose exec -T airflow-webserver ls -la /opt/airflow/
    exit 1
fi

echo ""
echo "Test 2: Listing shared directory contents..."
echo "Contents of /opt/airflow/shared:"
docker-compose exec -T airflow-webserver ls -la /opt/airflow/shared/

echo ""
echo "Test 3: Checking shared/common directory..."
if docker-compose exec -T airflow-webserver test -d /opt/airflow/shared/common; then
    echo "✅ /opt/airflow/shared/common directory exists"
    echo "Contents of /opt/airflow/shared/common:"
    docker-compose exec -T airflow-webserver ls -la /opt/airflow/shared/common/
else
    echo "❌ /opt/airflow/shared/common directory NOT found"
    exit 1
fi

echo ""
echo "Test 4: Testing Python import..."
docker-compose exec -T airflow-webserver python -c "
import sys
sys.path.insert(0, '/opt/airflow/shared')

print('Testing shared module imports...')
try:
    from shared.common import constants
    print('✅ Successfully imported shared.common.constants')
except Exception as e:
    print(f'❌ Failed to import shared.common.constants: {e}')
    sys.exit(1)

try:
    from shared.common import data_parsers
    print('✅ Successfully imported shared.common.data_parsers')
except Exception as e:
    print(f'❌ Failed to import shared.common.data_parsers: {e}')
    sys.exit(1)

print('🎉 All imports successful!')
"

echo ""
echo "Test 5: Checking DAG import errors..."
sleep 10  # Give DAGs time to reload
IMPORT_ERRORS=$(docker-compose exec -T airflow-webserver airflow dags list-import-errors 2>/dev/null || echo "")

if [ -z "$IMPORT_ERRORS" ]; then
    echo "✅ No DAG import errors found"
else
    echo "⚠️  DAG import errors:"
    echo "$IMPORT_ERRORS"
fi

echo ""
echo "🎉 VOLUME MOUNT FIX COMPLETE!"
echo "============================="
echo ""
echo "✅ What was fixed:"
echo "   - Used absolute paths instead of relative paths"
echo "   - Completely restarted Docker services"
echo "   - Verified shared directory is properly mounted"
echo ""
echo "🌐 Access Airflow UI:"
echo "   URL: http://localhost:8080"
echo "   Username: airflow"
echo "   Password: airflow"
echo ""
echo "🔍 If you still see import errors:"
echo "   1. Wait 2-3 minutes for DAGs to reload"
echo "   2. Check: docker-compose exec airflow-webserver airflow dags list-import-errors"

cd ..  # Return to project root
EOF
