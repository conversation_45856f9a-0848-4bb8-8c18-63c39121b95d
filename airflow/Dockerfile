# Choose your desired Airflow version and Python version
# Ensure this base image matches your intended Airflow 3.x deployment
FROM apache/airflow:3.0.0-python3.9

# Switch to root to install OS-level dependencies if needed
USER root
# Example: Install git, or other build tools if Python packages need them
# RUN apt-get update && apt-get install -y --no-install-recommends \
#     git \
#  && apt-get clean && rm -rf /var/lib/apt/lists/*

# Example: If you need Java for any parsing tools (uncomment if needed)
# RUN apt-get update && apt-get install -y openjdk-11-jre-headless && apt-get clean

# Switch back to the airflow user
USER airflow

# Copy requirements first to leverage Docker layer caching
COPY requirements.txt /requirements.txt
RUN pip install --no-cache-dir -r /requirements.txt

# You can add other setup steps here if needed, e.g.:
# COPY ./my_custom_parser_binary /usr/local/bin/my_custom_parser_binary
# RUN chmod +x /usr/local/bin/my_custom_parser_binary