# DAG Reference Guide

## 🔄 Pipeline Overview

The LLM Service Mapper consists of 6 interconnected DAGs that form a complete MLOps pipeline:

```mermaid
graph TD
    A[01_ingest_and_parse_definitions] --> B[02_generate_training_data]
    B --> C[03_train_ml_model]
    C --> D[04_operational_mapping_pipeline]
    D --> E[05_monitoring_and_retraining_trigger]
    E --> C
    F[06_ingest_operational_logs] --> B
    D --> F
```

## 📋 DAG Dependency Matrix

| DAG | Triggers | Triggered By | Schedule | Max Active Runs |
|-----|----------|--------------|----------|-----------------|
| 01_ingest_and_parse | Manual/API | None | None | 3 |
| 02_generate_training_data | Dataset | 01_ingest_and_parse | Auto | 5 |
| 03_train_ml_model | Manual | 02_generate_training_data | None | 1 |
| 04_operational_mapping | API/Manual | 03_train_ml_model | None | 10 |
| 05_monitoring_retraining | Schedule | 04_operational_mapping | @hourly | 1 |
| 06_ingest_operational_logs | Schedule | None | @daily | 2 |

## 🎯 DAG 01: Ingest and Parse Definitions

### Purpose
Entry point for the pipeline. Ingests WSDL, XSD, and OpenAPI files and parses them into structured formats.

### Parameters
```python
{
    "service_name": "unique_service_identifier",
    "wsdl_uri": "path/to/service.wsdl",
    "xsd_uri": "path/to/types.xsd",  # Optional
    "openapi_uri": "path/to/api.yaml",
    "output_base_path": "data/parsed_definitions"
}
```

### Task Flow
1. **validate_parameters**: Validates input parameters and file accessibility
2. **fetch_and_parse_definitions**: Downloads and parses all definition files
3. **register_in_catalog**: Stores service metadata in Airflow Variables

### Outputs
- Parsed WSDL structure (JSON)
- Parsed XSD types (JSON)
- Parsed OpenAPI specification (JSON)
- Type definitions for ML training
- Service catalog entry

### Example Usage
```bash
# Via Airflow UI
# Navigate to DAGs → 01_ingest_and_parse_definitions → Trigger DAG
# Fill parameters:
# - service_name: "todo_service"
# - wsdl_uri: "/opt/airflow/shared/sample_data/wsdl_input/todo-service.wsdl"
# - openapi_uri: "/opt/airflow/shared/sample_data/osdmp_target/todo-api.yaml"

# Via CLI
airflow dags trigger 01_ingest_and_parse_definitions \
  --conf '{"service_name": "todo_service", "wsdl_uri": "shared/sample_data/wsdl_input/todo-service.wsdl"}'
```

### Common Issues
- **File not found**: Ensure file paths are accessible from Airflow containers
- **Parsing errors**: Check file format and schema validity
- **Permission errors**: Verify volume mounts and file permissions

## 🔄 DAG 02: Generate Training Data

### Purpose
Converts parsed service definitions into structured training data for ML models.

### Parameters
```python
{
    "service_name": "service_to_process",
    "include_wsdl": True,
    "include_openapi": True,
    "include_java_code": False,
    "output_path": "data/training_data"
}
```

### Task Flow
1. **validate_service_exists**: Checks service exists in catalog
2. **load_service_definitions**: Retrieves parsed definitions
3. **generate_training_examples**: Creates ML training examples
4. **generate_java_code**: Optional Java code generation
5. **store_training_data**: Saves formatted training data

### Training Data Formats
- **Sequence-to-Sequence**: WSDL operation → OpenAPI path mapping
- **Classification**: Service type classification
- **Named Entity Recognition**: Type and field extraction
- **Code Generation**: Definition → Java class mapping

### Example Output
```json
{
  "examples": [
    {
      "id": "todo_service_001",
      "source_type": "wsdl_to_openapi",
      "input": "WSDL operation: getTodos(userId: string) -> TodoList",
      "target": "GET /users/{userId}/todos -> TodoList[]",
      "metadata": {
        "service_name": "todo_service",
        "operation_type": "query",
        "complexity": "simple"
      }
    }
  ]
}
```

## 🤖 DAG 03: Train ML Model

### Purpose
Fine-tunes Hugging Face transformer models using generated training data.

### Parameters
```python
{
    "service_names": "[]",  # JSON array, empty for all services
    "training_data_path": "data/training_data",
    "model_type": "mapping",
    "model_name": "llm-mapper-model",
    "base_model": "google/flan-t5-small",
    "epochs": 3,
    "batch_size": 8,
    "learning_rate": 5e-5,
    "use_peft": True,
    "validation_split": 0.2
}
```

### Task Flow
1. **load_training_data**: Aggregates training data from multiple services
2. **format_for_huggingface**: Converts to Hugging Face dataset format
3. **create_hf_datasets**: Creates train/validation splits
4. **train_hf_model**: Fine-tunes model with PEFT (LoRA)
5. **save_and_register_model**: Saves model and updates pointers

### Model Types
- **mapping**: Sequence-to-sequence mapping (T5-based)
- **classification**: Service classification (BERT-based)
- **generation**: Code generation (CodeT5-based)

### Training Configuration
```python
training_args = {
    "output_dir": "./results",
    "num_train_epochs": 3,
    "per_device_train_batch_size": 8,
    "per_device_eval_batch_size": 8,
    "warmup_steps": 500,
    "weight_decay": 0.01,
    "logging_dir": "./logs",
    "evaluation_strategy": "epoch",
    "save_strategy": "epoch",
    "load_best_model_at_end": True
}
```

## 🚀 DAG 04: Operational Mapping Pipeline

### Purpose
Performs real-time service mapping using trained models for production requests.

### Parameters
```python
{
    "request_id": "unique_request_id",
    "source_service_definition": "wsdl_content_or_path",
    "target_format": "openapi",
    "model_version": "latest",
    "use_heuristics": True,
    "confidence_threshold": 0.8
}
```

### Task Flow
1. **validate_mapping_request**: Validates input request
2. **load_models_and_config**: Loads latest trained models
3. **apply_ml_mapping**: Uses ML models for mapping
4. **apply_heuristic_fallback**: Falls back to heuristics if needed
5. **validate_mapping_results**: Validates output quality
6. **store_mapping_results**: Saves results and metadata

### Mapping Strategies
1. **ML-First**: Use trained models as primary approach
2. **Heuristic-Fallback**: Use rule-based mapping if ML confidence is low
3. **Hybrid**: Combine ML and heuristic approaches
4. **Validation**: Ensure output conforms to target format

## 📊 DAG 05: Monitoring and Retraining Trigger

### Purpose
Monitors model performance and automatically triggers retraining when needed.

### Parameters
```python
{
    "lookback_hours": 24,
    "min_samples_for_analysis": 100,
    "performance_threshold": 0.8,
    "retraining_threshold": 0.05,
    "notification_channels": ["email", "slack"]
}
```

### Task Flow
1. **collect_performance_metrics**: Gathers operational metrics
2. **analyze_model_performance**: Evaluates model accuracy and drift
3. **detect_performance_degradation**: Identifies performance issues
4. **generate_performance_report**: Creates detailed analysis
5. **trigger_retraining_if_needed**: Starts retraining if thresholds exceeded
6. **send_notifications**: Alerts administrators

### Monitored Metrics
- **Accuracy**: Mapping correctness rate
- **Confidence**: Average model confidence scores
- **Latency**: Response time metrics
- **Error Rate**: Failed mapping percentage
- **Data Drift**: Input distribution changes

## 📝 DAG 06: Ingest Operational Logs

### Purpose
Processes operational logs to extract additional training data for continuous learning.

### Parameters
```python
{
    "service_name": "target_service",
    "soap_log_path": "logs/soap",
    "rest_log_path": "logs/rest",
    "log_date_range": "1",
    "output_path": "data/operational_logs",
    "sample_rate": 1.0,
    "anonymize_data": True
}
```

### Task Flow
1. **validate_service_exists**: Ensures service is registered
2. **collect_soap_logs**: Processes SOAP request/response logs
3. **collect_rest_logs**: Processes REST API logs
4. **match_logs_with_definitions**: Correlates logs with service definitions
5. **store_operational_logs**: Saves processed logs for training

### Log Processing Features
- **Request/Response Pairing**: Matches requests with responses
- **Data Anonymization**: Removes sensitive information
- **Format Standardization**: Converts to common format
- **Quality Filtering**: Removes incomplete or invalid logs

## 🔧 DAG Configuration Best Practices

### Resource Allocation
```python
# High-memory tasks (ML training)
default_args = {
    "pool": "ml_training_pool",
    "queue": "ml_queue",
    "retries": 1,
    "retry_delay": timedelta(minutes=5)
}

# I/O intensive tasks (parsing)
default_args = {
    "pool": "parsing_pool", 
    "queue": "default",
    "retries": 3,
    "retry_delay": timedelta(minutes=1)
}
```

### Error Handling
```python
@task(retries=3, retry_delay=timedelta(minutes=2))
def robust_task():
    try:
        # Task logic
        pass
    except SpecificException as e:
        logger.error(f"Specific error: {e}")
        raise AirflowSkipException("Skipping due to expected error")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        raise AirflowFailException("Task failed due to unexpected error")
```

### Monitoring Integration
```python
# Custom metrics
from airflow.providers.prometheus.hooks.prometheus import PrometheusHook

@task
def track_metrics():
    prometheus = PrometheusHook()
    prometheus.send_metric("dag_success_rate", 0.95, {"dag_id": "01_ingest"})
```

## 🚨 Troubleshooting Guide

### Common DAG Issues

#### Import Errors
```bash
# Check import errors
airflow dags list-import-errors

# Common fixes
# 1. Verify shared module path
# 2. Check Python dependencies
# 3. Validate DAG syntax
```

#### Task Failures
```bash
# View task logs
airflow tasks logs <dag_id> <task_id> <execution_date>

# Common causes
# 1. Resource constraints
# 2. Missing dependencies
# 3. Configuration errors
```

#### Performance Issues
```bash
# Monitor DAG performance
airflow dags state <dag_id> <execution_date>

# Optimization strategies
# 1. Increase parallelism
# 2. Optimize task dependencies
# 3. Use appropriate pools
```

---

**This reference guide provides comprehensive information for understanding and operating all DAGs in the LLM Service Mapper pipeline.**
