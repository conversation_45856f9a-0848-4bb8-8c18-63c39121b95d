# Airflow Deployment Guide

## 🎯 Deployment Options Overview

| Deployment Type | Use Case | Complexity | Scalability | Maintenance |
|----------------|----------|------------|-------------|-------------|
| **Docker Compose** | Development, Testing | Low | Limited | Low |
| **Manual Installation** | Custom Environments | Medium | Medium | High |
| **Kubernetes** | Production, Scale | High | High | Medium |
| **Cloud Managed** | Enterprise Production | Low | Very High | Very Low |

## 🐳 Docker Compose Deployment (Recommended)

### Prerequisites
```bash
# Verify Docker installation
docker --version  # Should be 20.10+
docker-compose --version  # Should be 2.0+

# Check system resources
free -h  # Minimum 8GB RAM
df -h    # Minimum 20GB free space
```

### Quick Deployment
```bash
# 1. Clone repository
git clone <repository-url>
cd airflow-llm-service-mapper/airflow

# 2. Initialize environment
cp .env.example .env
echo "AIRFLOW_UID=$(id -u)" >> .env

# 3. Initialize Airflow database
docker-compose up airflow-init

# 4. Start all services
docker-compose up -d

# 5. Verify deployment
curl http://localhost:8080/health
```

### Environment File Setup
Create `.env` file with required configuration:
```bash
# Generate Fernet key
python -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())"

# Create .env file
cat > .env << EOF
AIRFLOW_UID=$(id -u)
AIRFLOW_GID=0
_AIRFLOW_WWW_USER_USERNAME=admin
_AIRFLOW_WWW_USER_PASSWORD=admin
AIRFLOW__CORE__FERNET_KEY=<your-generated-fernet-key>
EOF
```

### Service Architecture
```yaml
services:
  postgres:          # Database
  redis:             # Message broker
  airflow-webserver: # Web UI (port 8080)
  airflow-scheduler: # Task scheduler
  airflow-worker:    # Task executor
  airflow-triggerer: # Event-driven tasks
  airflow-init:      # Database initialization
  flower:            # Celery monitoring (port 5555)
```

### Volume Configuration
```yaml
volumes:
  # Core Airflow
  - ./dags:/opt/airflow/dags
  - ./logs:/opt/airflow/logs
  - ./plugins:/opt/airflow/plugins
  
  # LLM Service Mapper
  - ./data:/opt/airflow/data
  - ./models:/opt/airflow/models
  - ../shared:/opt/airflow/shared
  
  # Configuration
  - ./config:/opt/airflow/config
```

### Environment Configuration
```bash
# .env file
AIRFLOW_UID=1000
AIRFLOW_GID=0

# Database
POSTGRES_USER=airflow
POSTGRES_PASSWORD=airflow
POSTGRES_DB=airflow

# Airflow Core
AIRFLOW__CORE__EXECUTOR=CeleryExecutor
AIRFLOW__CORE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow
AIRFLOW__CELERY__RESULT_BACKEND=db+*********************************************
AIRFLOW__CELERY__BROKER_URL=redis://:@redis:6379/0

# Security
AIRFLOW__CORE__FERNET_KEY=<generate-with-python-cryptography>
AIRFLOW__WEBSERVER__SECRET_KEY=<generate-random-string>

# LLM Service Mapper
LLM_MAPPER_DATA_PATH=/opt/airflow/data
LLM_MAPPER_MODELS_PATH=/opt/airflow/models
LLM_MAPPER_LOG_LEVEL=INFO
```

### Production Optimizations
```yaml
# docker-compose.prod.yml
services:
  airflow-webserver:
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
  
  airflow-worker:
    deploy:
      replicas: 4
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
```

## 🔧 Manual Installation

### System Requirements
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.9 python3.9-venv python3.9-dev
sudo apt install postgresql postgresql-contrib redis-server
sudo apt install build-essential libpq-dev

# CentOS/RHEL
sudo yum install python39 python39-devel
sudo yum install postgresql postgresql-server redis
sudo yum install gcc postgresql-devel
```

### Installation Steps
```bash
# 1. Create Airflow user
sudo useradd -m -s /bin/bash airflow
sudo su - airflow

# 2. Create virtual environment
python3.9 -m venv airflow-env
source airflow-env/bin/activate

# 3. Install Airflow
pip install apache-airflow[postgres,redis,celery]==3.0.0
pip install -r requirements.txt

# 4. Set environment variables
export AIRFLOW_HOME=/home/<USER>/airflow
export AIRFLOW__CORE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:password@localhost/airflow

# 5. Initialize database
airflow db init
airflow users create --username admin --password admin --firstname Admin --lastname User --role Admin --email <EMAIL>

# 6. Copy project files
cp -r /path/to/project/airflow/dags $AIRFLOW_HOME/
cp -r /path/to/project/shared $AIRFLOW_HOME/

# 7. Start services
airflow webserver -p 8080 -D
airflow scheduler -D
airflow celery worker -D
```

### Service Configuration
```bash
# /etc/systemd/system/airflow-webserver.service
[Unit]
Description=Airflow webserver daemon
After=network.target postgresql.service redis.service

[Service]
User=airflow
Group=airflow
Type=simple
ExecStart=/home/<USER>/airflow-env/bin/airflow webserver -p 8080
Restart=on-failure

[Install]
WantedBy=multi-user.target
```

## ☁️ Cloud Managed Deployments

### AWS Managed Workflows for Apache Airflow (MWAA)

#### Setup Steps
```bash
# 1. Create S3 bucket for DAGs
aws s3 mb s3://your-mwaa-bucket

# 2. Upload DAGs and requirements
aws s3 sync ./dags s3://your-mwaa-bucket/dags/
aws s3 cp requirements.txt s3://your-mwaa-bucket/

# 3. Create MWAA environment
aws mwaa create-environment \
  --name llm-service-mapper \
  --source-bucket-arn arn:aws:s3:::your-mwaa-bucket \
  --dag-s3-path dags \
  --requirements-s3-path requirements.txt \
  --execution-role-arn arn:aws:iam::account:role/mwaa-execution-role
```

#### Configuration
```python
# mwaa_config.py
MWAA_CONFIG = {
    "Environment": {
        "AirflowConfigurationOptions": {
            "core.default_timezone": "UTC",
            "core.load_examples": "False",
            "webserver.expose_config": "True",
            "scheduler.dag_dir_list_interval": "300"
        },
        "LoggingConfiguration": {
            "DagProcessingLogs": {"Enabled": True, "LogLevel": "INFO"},
            "SchedulerLogs": {"Enabled": True, "LogLevel": "INFO"},
            "TaskLogs": {"Enabled": True, "LogLevel": "INFO"},
            "WebserverLogs": {"Enabled": True, "LogLevel": "INFO"},
            "WorkerLogs": {"Enabled": True, "LogLevel": "INFO"}
        }
    }
}
```

### Google Cloud Composer

#### Setup Steps
```bash
# 1. Enable APIs
gcloud services enable composer.googleapis.com

# 2. Create Composer environment
gcloud composer environments create llm-service-mapper \
  --location us-central1 \
  --python-version 3.9 \
  --node-count 3 \
  --disk-size 100GB

# 3. Upload DAGs
gcloud composer environments storage dags import \
  --environment llm-service-mapper \
  --location us-central1 \
  --source ./dags
```

## 🚀 Kubernetes Deployment

### Helm Chart Installation
```bash
# 1. Add Airflow Helm repository
helm repo add apache-airflow https://airflow.apache.org
helm repo update

# 2. Create namespace
kubectl create namespace airflow

# 3. Install with custom values
helm install airflow apache-airflow/airflow \
  --namespace airflow \
  --values values.yaml
```

### Custom Values Configuration
```yaml
# values.yaml
images:
  airflow:
    repository: apache/airflow
    tag: 3.0.0-python3.9

executor: "CeleryExecutor"

postgresql:
  enabled: true
  auth:
    username: airflow
    password: airflow
    database: airflow

redis:
  enabled: true

dags:
  persistence:
    enabled: true
    size: 10Gi
  gitSync:
    enabled: true
    repo: https://github.com/your-org/airflow-llm-service-mapper.git
    branch: main
    subPath: airflow/dags

config:
  core:
    load_examples: 'False'
  webserver:
    expose_config: 'True'
  scheduler:
    dag_dir_list_interval: 300
```

## 🔒 Security Configuration

### Authentication Setup
```python
# webserver_config.py
from airflow.auth.managers.fab.security_manager_override import FabAirflowSecurityManagerOverride
from flask_appbuilder.security.manager import AUTH_LDAP

AUTH_TYPE = AUTH_LDAP
AUTH_LDAP_SERVER = "ldap://ldap.company.com"
AUTH_LDAP_BIND_USER = "cn=admin,dc=company,dc=com"
AUTH_LDAP_BIND_PASSWORD = "password"
AUTH_LDAP_SEARCH = "ou=users,dc=company,dc=com"
AUTH_LDAP_UID_FIELD = "uid"
```

### SSL/TLS Configuration
```bash
# Generate certificates
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout airflow.key -out airflow.crt

# Update docker-compose.yml
services:
  airflow-webserver:
    environment:
      - AIRFLOW__WEBSERVER__WEB_SERVER_SSL_CERT=/opt/airflow/certs/airflow.crt
      - AIRFLOW__WEBSERVER__WEB_SERVER_SSL_KEY=/opt/airflow/certs/airflow.key
    volumes:
      - ./certs:/opt/airflow/certs
```

## 📊 Monitoring & Observability

### Metrics Collection
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'airflow'
    static_configs:
      - targets: ['airflow-webserver:8080']
    metrics_path: '/admin/metrics'
```

### Log Aggregation
```yaml
# fluentd configuration
<source>
  @type tail
  path /opt/airflow/logs/**/*.log
  pos_file /var/log/fluentd/airflow.log.pos
  tag airflow.*
  format multiline
  format_firstline /^\[\d{4}-\d{2}-\d{2}/
</source>
```

## 🔧 Troubleshooting

### Common Issues

#### DAGs Not Loading
```bash
# Check import errors
docker-compose exec airflow-webserver airflow dags list-import-errors

# Verify Python path
docker-compose exec airflow-webserver python -c "import sys; print(sys.path)"
```

#### Database Connection Issues
```bash
# Test database connection
docker-compose exec airflow-webserver airflow db check

# Reset database (development only)
docker-compose exec airflow-webserver airflow db reset
```

#### Performance Issues
```bash
# Monitor resource usage
docker stats

# Check task queue
docker-compose exec airflow-webserver airflow celery flower
```

### Health Checks
```bash
# Comprehensive health check script
#!/bin/bash
echo "Checking Airflow deployment health..."

# Check services
docker-compose ps | grep -E "(Up|healthy)" || echo "❌ Services not healthy"

# Check web interface
curl -f http://localhost:8080/health || echo "❌ Web interface not responding"

# Check DAG loading
docker-compose exec -T airflow-webserver airflow dags list | grep -q "01_ingest" || echo "❌ DAGs not loaded"

echo "✅ Health check complete"
```

---

**Choose the deployment option that best fits your infrastructure and requirements!**
