# Airflow LLM Service Mapper - Airflow Deployment

## 🚀 Overview

The Airflow LLM Service Mapper provides a complete MLOps pipeline for transforming SOAP/WSDL services to REST/OpenAPI specifications using Apache Airflow 3.0, machine learning models, and large language models.

## 📋 Prerequisites

### System Requirements
- **Docker & Docker Compose**: Latest versions
- **Memory**: Minimum 8GB RAM (16GB recommended)
- **Storage**: 20GB free space
- **CPU**: 4+ cores recommended

### Software Dependencies
- Apache Airflow 3.0+
- Python 3.9+
- PostgreSQL (included in Docker setup)
- Redis (for Celery executor in production)

## 🚀 Quick Start

### 1. Clone and Navigate
```bash
git clone <repository-url>
cd airflow-llm-service-mapper/airflow
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit configuration (optional)
vim .env
```

### 3. Start Airflow
```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f airflow-webserver
```

### 4. Access Airflow UI
- **URL**: http://localhost:8080
- **Username**: admin
- **Password**: admin

### 5. Verify Installation
```bash
# Check DAG loading
docker-compose exec airflow-webserver airflow dags list

# Test DAG syntax
docker-compose exec airflow-webserver airflow dags test 01_ingest_and_parse_definitions
```

## 🔄 DAG Workflow Overview

### Pipeline Architecture
```
01_ingest_and_parse_definitions → 02_generate_training_data → 03_train_ml_model
                                                                      ↓
06_ingest_operational_logs ← 05_monitoring_and_retraining ← 04_operational_mapping
```

### DAG Descriptions

#### 1. **01_ingest_and_parse_definitions_dag**
- **Purpose**: Entry point for service definition ingestion
- **Inputs**: WSDL, XSD, OpenAPI files
- **Outputs**: Parsed service definitions
- **Trigger**: Manual or API call

#### 2. **02_generate_training_data_dag**
- **Purpose**: Generate ML training data from parsed definitions
- **Inputs**: Parsed service definitions
- **Outputs**: Structured training datasets
- **Trigger**: Automatic (when new definitions available)

#### 3. **03_train_ml_model_dag**
- **Purpose**: Train/fine-tune ML models using Hugging Face
- **Inputs**: Training datasets
- **Outputs**: Trained models and evaluation metrics
- **Trigger**: Manual or scheduled

#### 4. **04_operational_mapping_pipeline_dag**
- **Purpose**: Real-time service mapping using trained models
- **Inputs**: Service mapping requests
- **Outputs**: Mapped service definitions
- **Trigger**: External API calls

#### 5. **05_monitoring_and_retraining_trigger_dag**
- **Purpose**: Monitor model performance and trigger retraining
- **Inputs**: Operational metrics
- **Outputs**: Performance reports, retraining triggers
- **Trigger**: Scheduled (hourly)

#### 6. **06_ingest_operational_logs_dag**
- **Purpose**: Process operational logs for continuous learning
- **Inputs**: SOAP/REST API logs
- **Outputs**: Processed log data for training
- **Trigger**: Scheduled (daily)

## 🔧 Configuration

### Environment Variables
```bash
# Core Airflow Settings
AIRFLOW__CORE__EXECUTOR=LocalExecutor
AIRFLOW__DATABASE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow
AIRFLOW__CORE__FERNET_KEY=<your-fernet-key>

# LLM Service Mapper Settings
LLM_MAPPER_DATA_PATH=/opt/airflow/data
LLM_MAPPER_MODELS_PATH=/opt/airflow/models
LLM_MAPPER_LOG_LEVEL=INFO

# ML Model Settings
HUGGINGFACE_CACHE_DIR=/opt/airflow/models/hf_cache
TRANSFORMERS_OFFLINE=0

# Optional: External Services
OPENAI_API_KEY=<your-openai-key>
HUGGINGFACE_TOKEN=<your-hf-token>
```

### Volume Mounts
```yaml
volumes:
  - ./data:/opt/airflow/data                    # Data storage
  - ./models:/opt/airflow/models                # ML models
  - ../shared:/opt/airflow/shared               # Shared modules
  - ./logs:/opt/airflow/logs                    # Airflow logs
```

## 📊 Monitoring & Operations

### Health Checks
```bash
# Check Airflow components
docker-compose exec airflow-webserver airflow info

# Check database connection
docker-compose exec airflow-webserver airflow db check

# Check DAG integrity
docker-compose exec airflow-webserver airflow dags list-import-errors
```

### Log Management
```bash
# View specific DAG logs
docker-compose logs airflow-scheduler | grep "01_ingest_and_parse"

# Access task logs via UI
# Navigate to: Airflow UI → DAGs → [DAG Name] → Graph → [Task] → Logs
```

### Performance Monitoring
- **Airflow UI**: Monitor DAG runs, task duration, success rates
- **System Metrics**: CPU, memory, disk usage via Docker stats
- **Custom Metrics**: ML model performance, parsing success rates

## 🛠️ Development Workflow

### Local Development
```bash
# Edit DAGs
vim dags/01_ingest_and_parse_definitions_dag.py

# Restart scheduler to pick up changes
docker-compose restart airflow-scheduler

# Test DAG changes
docker-compose exec airflow-webserver airflow dags test <dag_id>
```

### Adding New DAGs
1. Create DAG file in `dags/` directory
2. Follow naming convention: `##_descriptive_name_dag.py`
3. Import shared modules from `shared/common/`
4. Add comprehensive documentation
5. Test thoroughly before deployment

### Debugging
```bash
# Access Airflow container
docker-compose exec airflow-webserver bash

# Run Python REPL with Airflow context
docker-compose exec airflow-webserver python
>>> from airflow.models import DagBag
>>> dag_bag = DagBag()
>>> dag_bag.import_errors
```

## 🔒 Security Considerations

### Authentication
- Default admin/admin credentials should be changed in production
- Consider integrating with LDAP/OAuth for enterprise environments
- Use Airflow's built-in RBAC for user management

### Secrets Management
- Store sensitive data in Airflow Variables or Connections
- Use environment variables for configuration
- Never commit secrets to version control

### Network Security
- Run behind reverse proxy in production
- Use HTTPS for web interface
- Restrict database access to Airflow components only

## 📚 Additional Resources

- **[Deployment Guide](DEPLOYMENT_GUIDE.md)**: Detailed deployment instructions
- **[DAG Reference](DAG_REFERENCE.md)**: Complete DAG documentation
- **[Configuration Guide](CONFIGURATION_GUIDE.md)**: Advanced configuration options
- **[Troubleshooting](TROUBLESHOOTING.md)**: Common issues and solutions
- **[Monitoring Guide](MONITORING_GUIDE.md)**: Monitoring and alerting setup

## 🆘 Getting Help

### Common Issues
1. **DAGs not appearing**: Check import errors in Airflow UI
2. **Task failures**: Check task logs and shared module imports
3. **Performance issues**: Monitor resource usage and adjust worker counts
4. **Database issues**: Verify PostgreSQL connection and migrations

### Support Channels
- **Documentation**: Check this guide and linked resources
- **Logs**: Always check Airflow and task logs first
- **Community**: Airflow community forums and documentation
- **Issues**: Project issue tracker for bugs and feature requests

---

**Ready to start mapping services with Airflow! 🎯**
