# Airflow Configuration Guide

## 🔧 Configuration Overview

The LLM Service Mapper uses multiple configuration layers:

1. **Environment Variables** (.env file)
2. **Airflow Configuration** (airflow.cfg)
3. **Docker Compose** (docker-compose.yml)
4. **Application Configuration** (shared/config/)

## 📁 Configuration Files Structure

```
airflow/
├── .env                          # Environment variables
├── docker-compose.yml            # Docker configuration
├── airflow.cfg                   # Airflow configuration (optional)
├── webserver_config.py           # Web server configuration (optional)
└── config/
    ├── airflow_variables.json    # Airflow Variables
    └── connections.json          # Airflow Connections
```

## 🌍 Environment Variables (.env)

### Core Airflow Settings
```bash
# User and Group IDs
AIRFLOW_UID=1000
AIRFLOW_GID=0

# Default admin user
_AIRFLOW_WWW_USER_USERNAME=admin
_AIRFLOW_WWW_USER_PASSWORD=admin

# Security
AIRFLOW__CORE__FERNET_KEY=<your-fernet-key>
AIRFLOW__WEBSERVER__SECRET_KEY=<your-secret-key>

# Database
AIRFLOW__CORE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow
AIRFLOW__CELERY__RESULT_BACKEND=db+*********************************************
AIRFLOW__CELERY__BROKER_URL=redis://:@redis:6379/0

# Executor
AIRFLOW__CORE__EXECUTOR=CeleryExecutor
AIRFLOW__CORE__PARALLELISM=32
AIRFLOW__CORE__MAX_ACTIVE_TASKS_PER_DAG=16
AIRFLOW__CORE__MAX_ACTIVE_RUNS_PER_DAG=16

# Logging
AIRFLOW__LOGGING__LOGGING_LEVEL=INFO
AIRFLOW__CORE__LOAD_EXAMPLES=False
```

### LLM Service Mapper Settings
```bash
# Data Paths
LLM_MAPPER_DATA_PATH=/opt/airflow/data
LLM_MAPPER_MODELS_PATH=/opt/airflow/models
LLM_MAPPER_SHARED_PATH=/opt/airflow/shared
LLM_MAPPER_LOG_LEVEL=INFO

# ML Model Configuration
HUGGINGFACE_CACHE_DIR=/opt/airflow/models/hf_cache
TRANSFORMERS_OFFLINE=0
TRANSFORMERS_CACHE=/opt/airflow/models/transformers_cache

# Processing Limits
MAX_FILE_SIZE_MB=100
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT_SECONDS=30
MAX_RETRY_ATTEMPTS=3

# Feature Flags
ENABLE_JAVA_CODE_GENERATION=true
ENABLE_ADVANCED_HEURISTICS=true
ENABLE_MODEL_QUANTIZATION=false
ENABLE_DISTRIBUTED_TRAINING=false
```

### External Service Configuration
```bash
# Optional: OpenAI API
OPENAI_API_KEY=<your-openai-key>
OPENAI_MODEL=gpt-3.5-turbo

# Optional: Hugging Face
HUGGINGFACE_TOKEN=<your-hf-token>

# Optional: Monitoring
PROMETHEUS_ENDPOINT=http://prometheus:9090
GRAFANA_ENDPOINT=http://grafana:3000

# Optional: Notifications
SLACK_WEBHOOK_URL=<your-slack-webhook>
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USER=<your-email>
EMAIL_SMTP_PASSWORD=<your-password>
```

## 🐳 Docker Compose Configuration

### Development Configuration
```yaml
# docker-compose.yml
version: '3.8'

x-airflow-common: &airflow-common
  image: apache/airflow:3.0.0-python3.9
  environment: &airflow-common-env
    AIRFLOW__CORE__EXECUTOR: CeleryExecutor
    AIRFLOW__CORE__SQL_ALCHEMY_CONN: postgresql+psycopg2://airflow:airflow@postgres/airflow
    AIRFLOW__CELERY__RESULT_BACKEND: db+*********************************************
    AIRFLOW__CELERY__BROKER_URL: redis://:@redis:6379/0
    AIRFLOW__CORE__FERNET_KEY: ${AIRFLOW__CORE__FERNET_KEY}
    AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION: 'true'
    AIRFLOW__CORE__LOAD_EXAMPLES: 'false'
    AIRFLOW__API__AUTH_BACKENDS: 'airflow.api.auth.backend.basic_auth'
  volumes:
    - ./dags:/opt/airflow/dags
    - ./logs:/opt/airflow/logs
    - ./plugins:/opt/airflow/plugins
    - ./data:/opt/airflow/data
    - ./models:/opt/airflow/models
    - ../shared:/opt/airflow/shared
  user: "${AIRFLOW_UID:-50000}:0"
  depends_on: &airflow-common-depends-on
    redis:
      condition: service_healthy
    postgres:
      condition: service_healthy

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_USER: airflow
      POSTGRES_PASSWORD: airflow
      POSTGRES_DB: airflow
    volumes:
      - postgres-db-volume:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "airflow"]
      interval: 5s
      retries: 5
    restart: always

  redis:
    image: redis:latest
    expose:
      - 6379
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 30s
      retries: 50
    restart: always

  airflow-webserver:
    <<: *airflow-common
    command: webserver
    ports:
      - 8080:8080
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8080/health"]
      interval: 10s
      timeout: 10s
      retries: 5
    restart: always
    depends_on:
      <<: *airflow-common-depends-on
      airflow-init:
        condition: service_completed_successfully

volumes:
  postgres-db-volume:
```

### Production Configuration
```yaml
# docker-compose.prod.yml
services:
  airflow-webserver:
    <<: *airflow-common
    command: webserver
    ports:
      - "80:8080"
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    environment:
      <<: *airflow-common-env
      AIRFLOW__WEBSERVER__WEB_SERVER_SSL_CERT: /opt/airflow/certs/airflow.crt
      AIRFLOW__WEBSERVER__WEB_SERVER_SSL_KEY: /opt/airflow/certs/airflow.key
    volumes:
      - ./certs:/opt/airflow/certs

  airflow-worker:
    <<: *airflow-common
    command: celery worker
    deploy:
      replicas: 4
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
```

## ⚙️ Airflow Configuration (airflow.cfg)

### Core Settings
```ini
[core]
# Executor
executor = CeleryExecutor
parallelism = 32
max_active_tasks_per_dag = 16
max_active_runs_per_dag = 16

# DAG Settings
dags_folder = /opt/airflow/dags
dags_are_paused_at_creation = True
load_examples = False
dag_dir_list_interval = 300

# Security
fernet_key = <your-fernet-key>
```

### Scheduler Settings
```ini
[scheduler]
# Performance
job_heartbeat_sec = 5
scheduler_heartbeat_sec = 5
num_runs = -1
processor_poll_interval = 1

# DAG Processing
dag_dir_list_interval = 300
max_threads = 2
catchup_by_default = False
```

### Webserver Settings
```ini
[webserver]
# Basic Settings
web_server_host = 0.0.0.0
web_server_port = 8080
web_server_worker_timeout = 120

# Security
secret_key = <your-secret-key>
expose_config = True
authenticate = True
auth_backend = airflow.auth.backends.password_auth

# SSL (Production)
web_server_ssl_cert = /opt/airflow/certs/airflow.crt
web_server_ssl_key = /opt/airflow/certs/airflow.key
```

### Celery Settings
```ini
[celery]
# Broker
broker_url = redis://:@redis:6379/0
result_backend = db+*********************************************

# Worker Settings
worker_concurrency = 16
worker_log_server_port = 8793
flower_host = 0.0.0.0
flower_port = 5555
```

## 🔐 Security Configuration

### Authentication Setup
```python
# webserver_config.py
from airflow.auth.managers.fab.security_manager_override import FabAirflowSecurityManagerOverride
from flask_appbuilder.security.manager import AUTH_LDAP, AUTH_OAUTH

# LDAP Authentication
AUTH_TYPE = AUTH_LDAP
AUTH_LDAP_SERVER = "ldap://ldap.company.com"
AUTH_LDAP_BIND_USER = "cn=admin,dc=company,dc=com"
AUTH_LDAP_BIND_PASSWORD = "password"
AUTH_LDAP_SEARCH = "ou=users,dc=company,dc=com"
AUTH_LDAP_UID_FIELD = "uid"

# OAuth Authentication (Google)
AUTH_TYPE = AUTH_OAUTH
OAUTH_PROVIDERS = [
    {
        'name': 'google',
        'token_key': 'access_token',
        'icon': 'fa-google',
        'remote_app': {
            'client_id': 'your-google-client-id',
            'client_secret': 'your-google-client-secret',
            'server_metadata_url': 'https://accounts.google.com/.well-known/openid_configuration',
            'client_kwargs': {
                'scope': 'openid email profile'
            }
        }
    }
]
```

### Secrets Management
```python
# Using Airflow Variables for secrets
from airflow.models import Variable

# Store secrets
Variable.set("openai_api_key", "your-secret-key", serialize_json=False)
Variable.set("database_password", "your-db-password", serialize_json=False)

# Retrieve secrets in DAGs
@task
def use_secret():
    api_key = Variable.get("openai_api_key")
    # Use the secret
```

### Connection Configuration
```python
# Using Airflow Connections
from airflow.hooks.base import BaseHook

# Create connection via UI or CLI
# airflow connections add 'my_postgres' \
#   --conn-type 'postgres' \
#   --conn-host 'localhost' \
#   --conn-login 'airflow' \
#   --conn-password 'airflow' \
#   --conn-schema 'airflow'

# Use connection in DAGs
@task
def use_connection():
    conn = BaseHook.get_connection("my_postgres")
    # Use connection
```

## 📊 Monitoring Configuration

### Prometheus Metrics
```ini
# airflow.cfg
[metrics]
statsd_on = True
statsd_host = prometheus-statsd-exporter
statsd_port = 9125
statsd_prefix = airflow
```

### Custom Metrics
```python
# In DAG tasks
from airflow.providers.prometheus.hooks.prometheus import PrometheusHook

@task
def track_custom_metrics():
    prometheus = PrometheusHook()
    prometheus.send_metric("dag_execution_time", 120.5, {"dag_id": "my_dag"})
    prometheus.send_metric("task_success_rate", 0.95, {"task_id": "my_task"})
```

### Log Configuration
```ini
# airflow.cfg
[logging]
logging_level = INFO
fab_logging_level = WARN
log_format = [%%(asctime)s] {%%(filename)s:%%(lineno)d} %%(levelname)s - %%(message)s
simple_log_format = %%(asctime)s %%(levelname)s - %%(message)s

# Remote logging (S3)
remote_logging = True
remote_log_conn_id = aws_default
remote_base_log_folder = s3://my-airflow-logs
encrypt_s3_logs = True
```

## 🔄 Performance Tuning

### Resource Optimization
```yaml
# docker-compose.yml resource limits
services:
  airflow-scheduler:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  airflow-worker:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
```

### Database Optimization
```ini
# airflow.cfg
[core]
sql_alchemy_pool_size = 10
sql_alchemy_pool_recycle = 3600
sql_alchemy_max_overflow = 20

[scheduler]
max_threads = 4
```

### Task Pool Configuration
```python
# Create task pools via CLI or UI
# airflow pools set ml_training_pool 2 "ML Training Pool"
# airflow pools set parsing_pool 5 "Parsing Pool"

# Use pools in DAGs
@task(pool="ml_training_pool")
def train_model():
    pass

@task(pool="parsing_pool")
def parse_file():
    pass
```

## 🔧 Development Configuration

### Local Development
```bash
# .env.dev
AIRFLOW__CORE__LOAD_EXAMPLES=True
AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=False
AIRFLOW__LOGGING__LOGGING_LEVEL=DEBUG
AIRFLOW__WEBSERVER__RELOAD_ON_PLUGIN_CHANGE=True
```

### Testing Configuration
```bash
# .env.test
AIRFLOW__CORE__UNIT_TEST_MODE=True
AIRFLOW__CORE__LOAD_EXAMPLES=False
AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION=True
```

## 📋 Configuration Validation

### Validation Script
```bash
#!/bin/bash
echo "🔍 Configuration Validation"

# Check required environment variables
required_vars=("AIRFLOW__CORE__FERNET_KEY" "AIRFLOW__WEBSERVER__SECRET_KEY")
for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "❌ Missing required variable: $var"
    else
        echo "✅ $var is set"
    fi
done

# Check file permissions
if [[ -r "./dags" && -w "./logs" && -w "./data" ]]; then
    echo "✅ File permissions correct"
else
    echo "❌ File permission issues"
fi

# Check Docker resources
memory=$(docker system info --format '{{.MemTotal}}')
if [[ $memory -gt 8000000000 ]]; then
    echo "✅ Sufficient memory available"
else
    echo "⚠️ Low memory available"
fi
```

---

**Proper configuration is crucial for optimal Airflow performance and security. Review and adjust these settings based on your specific requirements.**
