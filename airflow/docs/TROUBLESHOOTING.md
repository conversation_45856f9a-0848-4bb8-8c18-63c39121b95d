# Airflow Troubleshooting Guide

## 🚨 Quick Diagnosis

### Health Check Script
```bash
#!/bin/bash
echo "🔍 Airflow Health Check"
echo "======================"

# Check Docker services
echo "📦 Docker Services:"
docker-compose ps | grep -E "(Up|healthy)" && echo "✅ Services running" || echo "❌ Services down"

# Check web interface
echo "🌐 Web Interface:"
curl -f http://localhost:8080/health &>/dev/null && echo "✅ Web UI accessible" || echo "❌ Web UI not responding"

# Check database
echo "🗄️ Database:"
docker-compose exec -T airflow-webserver airflow db check &>/dev/null && echo "✅ Database connected" || echo "❌ Database issues"

# Check DAG loading
echo "📋 DAG Loading:"
docker-compose exec -T airflow-webserver airflow dags list-import-errors | grep -q "No import errors" && echo "✅ DAGs loaded" || echo "❌ DAG import errors"

echo "======================"
```

### Common Symptoms & Quick Fixes

| Symptom | Quick Fix | Details |
|---------|-----------|---------|
| DAGs not appearing | Check import errors | `airflow dags list-import-errors` |
| Tasks failing | Check logs | Navigate to task → View Logs |
| Slow performance | Check resources | `docker stats` |
| Web UI not loading | Restart webserver | `docker-compose restart airflow-webserver` |
| Database errors | Reset DB (dev only) | `airflow db reset` |

## 🔧 DAG Issues

### DAGs Not Loading

#### Symptom
- DAGs don't appear in Airflow UI
- Empty DAG list or missing DAGs

#### Diagnosis
```bash
# Check import errors
docker-compose exec airflow-webserver airflow dags list-import-errors

# Check DAG folder
docker-compose exec airflow-webserver ls -la /opt/airflow/dags/

# Test DAG syntax
docker-compose exec airflow-webserver python -m py_compile /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py
```

#### Common Causes & Solutions

**1. Import Path Issues**
```python
# ❌ Incorrect
from common import constants

# ✅ Correct
import sys
import os
sys.path.append('/opt/airflow/shared')
from shared.common import constants
```

**2. Missing Dependencies**
```bash
# Check installed packages
docker-compose exec airflow-webserver pip list | grep -E "(lxml|xmlschema|transformers)"

# Install missing packages
docker-compose exec airflow-webserver pip install -r requirements.txt
```

**3. Syntax Errors**
```bash
# Validate Python syntax
docker-compose exec airflow-webserver python -m py_compile /opt/airflow/dags/*.py

# Check for common issues
grep -n "import" /opt/airflow/dags/*.py | grep "common"
```

### Task Failures

#### Symptom
- Tasks show red (failed) status
- Task logs show errors

#### Diagnosis Steps
```bash
# 1. Check task logs
airflow tasks logs <dag_id> <task_id> <execution_date>

# 2. Check task state
airflow tasks state <dag_id> <task_id> <execution_date>

# 3. Test task locally
airflow tasks test <dag_id> <task_id> <execution_date>
```

#### Common Task Failures

**1. File Not Found Errors**
```bash
# Check file paths
docker-compose exec airflow-webserver ls -la /opt/airflow/shared/sample_data/

# Verify volume mounts
docker-compose exec airflow-webserver mount | grep airflow
```

**2. Memory Issues**
```bash
# Check memory usage
docker stats --no-stream

# Increase memory limits in docker-compose.yml
services:
  airflow-worker:
    deploy:
      resources:
        limits:
          memory: 4G
```

**3. Permission Issues**
```bash
# Check file permissions
docker-compose exec airflow-webserver ls -la /opt/airflow/data/

# Fix permissions
sudo chown -R 50000:0 ./data/
sudo chmod -R 755 ./data/
```

### Shared Module Import Issues

#### Symptom
- `ModuleNotFoundError: No module named 'shared'`
- Import errors in DAG logs

#### Solution
```python
# Add to top of each DAG file
import sys
import os

# Add shared directory to Python path
# In Docker container, shared is mounted at /opt/airflow/shared
shared_path = '/opt/airflow/shared'
if os.path.exists(shared_path) and shared_path not in sys.path:
    sys.path.append(shared_path)
else:
    # Fallback for local development (relative path)
    shared_path = os.path.join(os.path.dirname(__file__), '..', '..', 'shared')
    if os.path.exists(shared_path) and shared_path not in sys.path:
        sys.path.append(shared_path)

# Now import shared modules
from shared.common import constants, data_parsers
from shared.common.definition_store import DefinitionStore
```

#### Verification
```bash
# Test imports in container
docker-compose exec airflow-webserver python -c "
import sys
sys.path.append('/opt/airflow/shared')
from shared.common import constants
print('✅ Shared modules imported successfully')
"
```

## 🐳 Docker Issues

### Container Startup Problems

#### Symptom
- Containers fail to start
- Services show "Exited" status

#### Diagnosis
```bash
# Check container status
docker-compose ps

# Check container logs
docker-compose logs airflow-webserver
docker-compose logs airflow-scheduler
docker-compose logs postgres

# Check resource usage
docker system df
free -h
```

#### Common Solutions

**1. Port Conflicts**
```bash
# Check port usage
netstat -tulpn | grep :8080

# Change ports in docker-compose.yml
services:
  airflow-webserver:
    ports:
      - "8081:8080"  # Use different port
```

**2. Insufficient Resources**
```bash
# Check available memory
free -h

# Reduce resource requirements
services:
  airflow-webserver:
    deploy:
      resources:
        limits:
          memory: 1G
```

**3. Volume Mount Issues**
```bash
# Check volume permissions
ls -la ./dags ./logs ./data

# Fix ownership
sudo chown -R $(id -u):$(id -g) ./dags ./logs ./data
```

### Database Connection Issues

#### Symptom
- "Connection refused" errors
- Database timeout errors

#### Diagnosis
```bash
# Check PostgreSQL container
docker-compose logs postgres

# Test database connection
docker-compose exec postgres psql -U airflow -d airflow -c "\l"

# Check connection string
docker-compose exec airflow-webserver env | grep SQL_ALCHEMY_CONN
```

#### Solutions

**1. Database Not Ready**
```bash
# Wait for database initialization
docker-compose up airflow-init

# Check initialization logs
docker-compose logs airflow-init
```

**2. Connection String Issues**
```bash
# Verify connection string in .env
AIRFLOW__CORE__SQL_ALCHEMY_CONN=postgresql+psycopg2://airflow:airflow@postgres/airflow

# Test connection manually
docker-compose exec airflow-webserver python -c "
from sqlalchemy import create_engine
engine = create_engine('postgresql+psycopg2://airflow:airflow@postgres/airflow')
print('✅ Database connection successful')
"
```

## 🔄 Performance Issues

### Slow DAG Execution

#### Symptoms
- Tasks take longer than expected
- High CPU/memory usage
- Timeouts

#### Diagnosis
```bash
# Monitor resource usage
docker stats --no-stream

# Check task duration
airflow dags report

# Analyze task logs for bottlenecks
grep -i "duration\|time\|slow" /opt/airflow/logs/dag_id/task_id/*/1.log
```

#### Optimization Strategies

**1. Increase Parallelism**
```python
# In DAG definition
default_args = {
    "max_active_tasks": 10,
    "max_active_runs": 3
}

# In airflow.cfg
[core]
parallelism = 32
max_active_tasks_per_dag = 16
```

**2. Resource Allocation**
```yaml
# docker-compose.yml
services:
  airflow-worker:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

**3. Task Optimization**
```python
# Use task pools for resource management
@task(pool="ml_training_pool")
def train_model():
    pass

# Implement caching
@task
def cached_parsing(file_path):
    cache_key = f"parsed_{hash(file_path)}"
    if cache_exists(cache_key):
        return load_from_cache(cache_key)
    result = parse_file(file_path)
    save_to_cache(cache_key, result)
    return result
```

### Memory Issues

#### Symptoms
- Out of memory errors
- Container restarts
- Slow performance

#### Solutions

**1. Increase Container Memory**
```yaml
# docker-compose.yml
services:
  airflow-worker:
    deploy:
      resources:
        limits:
          memory: 8G
```

**2. Optimize Code**
```python
# Process data in chunks
def process_large_file(file_path):
    with open(file_path, 'r') as f:
        for chunk in read_in_chunks(f, chunk_size=1000):
            process_chunk(chunk)

# Clear variables
def cleanup_task():
    large_variable = None
    gc.collect()
```

## 🔐 Security Issues

### Authentication Problems

#### Symptoms
- Cannot log into Airflow UI
- Permission denied errors

#### Solutions

**1. Reset Admin User**
```bash
# Create new admin user
docker-compose exec airflow-webserver airflow users create \
  --username admin \
  --password admin \
  --firstname Admin \
  --lastname User \
  --role Admin \
  --email <EMAIL>
```

**2. Check User Permissions**
```bash
# List users
docker-compose exec airflow-webserver airflow users list

# Check user roles
docker-compose exec airflow-webserver airflow users export
```

### SSL/TLS Issues

#### Symptoms
- Certificate errors
- HTTPS not working

#### Solutions

**1. Generate Self-Signed Certificate**
```bash
# Generate certificate
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ./certs/airflow.key \
  -out ./certs/airflow.crt \
  -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"

# Update docker-compose.yml
services:
  airflow-webserver:
    environment:
      - AIRFLOW__WEBSERVER__WEB_SERVER_SSL_CERT=/opt/airflow/certs/airflow.crt
      - AIRFLOW__WEBSERVER__WEB_SERVER_SSL_KEY=/opt/airflow/certs/airflow.key
```

## 📊 Monitoring & Debugging

### Enable Debug Logging

```bash
# Set debug level in .env
AIRFLOW__LOGGING__LOGGING_LEVEL=DEBUG

# Or in docker-compose.yml
environment:
  - AIRFLOW__LOGGING__LOGGING_LEVEL=DEBUG
```

### Custom Monitoring

```python
# Add to DAG for custom metrics
from airflow.providers.prometheus.hooks.prometheus import PrometheusHook

@task
def monitor_task_performance():
    start_time = time.time()
    # Task logic here
    duration = time.time() - start_time
    
    # Send metrics
    prometheus = PrometheusHook()
    prometheus.send_metric("task_duration", duration, {"task_id": "my_task"})
```

## 🆘 Emergency Procedures

### Complete Reset (Development Only)

```bash
# ⚠️ WARNING: This will delete all data
docker-compose down -v
docker system prune -f
rm -rf ./logs/* ./data/*
docker-compose up airflow-init
docker-compose up -d
```

### Backup and Restore

```bash
# Backup
docker-compose exec postgres pg_dump -U airflow airflow > backup.sql
tar -czf airflow_backup.tar.gz ./dags ./data ./logs

# Restore
docker-compose exec -T postgres psql -U airflow airflow < backup.sql
tar -xzf airflow_backup.tar.gz
```

## 📞 Getting Help

### Log Collection
```bash
# Collect all logs for support
mkdir -p support_logs
docker-compose logs > support_logs/docker_logs.txt
cp -r ./logs support_logs/airflow_logs
docker-compose exec airflow-webserver airflow dags list-import-errors > support_logs/import_errors.txt
tar -czf support_package.tar.gz support_logs/
```

### Support Information
- **Airflow Version**: Check with `airflow version`
- **Docker Version**: Check with `docker --version`
- **System Resources**: Include `free -h` and `df -h` output
- **Error Messages**: Include complete error messages and stack traces
- **Configuration**: Include relevant configuration (sanitized)

---

**Most issues can be resolved by checking logs, verifying configuration, and ensuring proper resource allocation.**
