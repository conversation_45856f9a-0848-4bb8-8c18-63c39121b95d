"""
DAG for ingesting and parsing service definitions (WSDL/XSD and OpenAPI).

This DAG is the entry point of the LLM service mapping pipeline. It fetches
service definitions from various sources, parses them into structured formats,
and stores the results for downstream processing by other DAGs.

Author: Airflow LLM Service Mapper Team
Version: 1.0.0
"""

from __future__ import annotations

import logging
import os
import pendulum
from typing import Dict, Any, Optional

from airflow.decorators import dag, task
from airflow.datasets import Dataset
from airflow.exceptions import AirflowException
from airflow.models.param import Param
from airflow.utils.context import Context

# Import shared modules
import sys
import os

# Add shared directory to Python path BEFORE importing shared modules
# In Docker container, shared is mounted at /opt/airflow/shared
shared_path = '/opt/airflow/shared'
if os.path.exists(shared_path) and shared_path not in sys.path:
    sys.path.insert(0, shared_path)  # Use insert(0, ...) for higher priority
else:
    # Fallback for local development (relative path)
    shared_path = os.path.join(os.path.dirname(__file__), '..', '..', 'shared')
    if os.path.exists(shared_path) and shared_path not in sys.path:
        sys.path.insert(0, shared_path)

# Now import shared modules
from shared.common import constants, data_parsers, validation_utils, type_definition_parser

# Configure logging
logger = logging.getLogger(__name__)

# Dataset for lineage tracking
PARSED_DEFINITIONS_DS = Dataset(constants.PARSED_DEFINITIONS_DATASET_URI)

@dag(
    dag_id="01_ingest_and_parse_definitions",
    description="Ingest and parse service definitions (WSDL/XSD and OpenAPI)",
    schedule=None,  # Manual trigger or triggered by external events
    start_date=pendulum.datetime(2024, 1, 1, tz="UTC"),
    catchup=False,
    max_active_runs=3,  # Allow multiple concurrent runs for different services
    max_active_tasks=10,
    default_args={
        "owner": "llm-mapper-team",
        "depends_on_past": False,
        "email_on_failure": False,
        "email_on_retry": False,
        "retries": 2,
        "retry_delay": pendulum.duration(minutes=5),
    },
    tags=["llm-mapper", "ingestion", "parsing", "data-pipeline"],
    params={
        "service_name": Param(
            "example_service",
            type="string",
            description="Unique name for the service being processed. Must be alphanumeric with underscores/hyphens only.",
            pattern=r"^[a-zA-Z0-9_-]+$"
        ),
        "wsdl_definitions": Param(
            [
                {
                    "service_name": "example_service",
                    "wsdl_uri": "include/sample_data/wsdl_input/todo-service.wsdl",
                    "xsd_uris": ["include/sample_data/xsd_input/todo-types.xsd"]
                }
            ],
            type="array",
            description="List of WSDL service definitions with associated XSD files. Each entry should have 'service_name', 'wsdl_uri', and optional 'xsd_uris' array."
        ),
        "wsdl_uri": Param(
            None,
            type=["null", "string"],
            description="[DEPRECATED] Single WSDL URI - use wsdl_definitions instead. Kept for backward compatibility."
        ),
        "xsd_uri": Param(
            None,
            type=["null", "string"],
            description="[DEPRECATED] Single XSD URI - use wsdl_definitions instead. Kept for backward compatibility."
        ),
        "openapi_uri": Param(
            "include/sample_data/osdmp_target/todo-api.yaml",
            type="string",
            description="URI to the target OpenAPI definition file."
        ),
        "output_base_path": Param(
            "data/parsed_definitions",
            type="string",
            description="Base path to store parsed outputs."
        ),
    },
    doc_md="""
    ## Ingest and Parse Service Definitions DAG

    This DAG is the entry point of the LLM service mapping pipeline. It fetches service
    definitions from various sources, parses them into structured formats, and stores
    the results for downstream processing.

    ### Workflow
    1. **Validate Parameters**: Ensures all input parameters are valid
    2. **Fetch and Parse Definitions**: Downloads and parses WSDL, XSD, and OpenAPI files
    3. **Validate Parsed Data**: Ensures parsed data conforms to expected schemas
    4. **Store Results**: Saves parsed definitions to specified output path
    5. **Register in Catalog**: Makes the service available for downstream DAGs

    ### Inputs
    - **service_name**: Unique identifier for the service
    - **wsdl_uri**: Path or URL to WSDL file
    - **xsd_uri**: Optional path or URL to XSD file
    - **openapi_uri**: Path or URL to OpenAPI specification
    - **output_base_path**: Directory to store parsed outputs

    ### Outputs
    - Structured JSON representations of parsed definitions
    - Service catalog entry for downstream DAG consumption
    - Dataset trigger for dependent DAGs

    ### Dependencies
    - None (entry point DAG)

    ### Triggers
    - Manual execution
    - External API calls
    - File system events (future enhancement)
    """,
)
def ingest_and_parse_definitions_dag():
    """
    Main DAG function that defines the workflow for ingesting and parsing service definitions.

    Returns:
        DAG: Configured Airflow DAG instance
    """

    @task
    def validate_parameters(
        service_name: str,
        wsdl_definitions: List[Dict[str, Any]],
        wsdl_uri: Optional[str],
        xsd_uri: Optional[str],
        openapi_uri: str,
        output_base_path: str,
        **context: Context
    ) -> Dict[str, Any]:
        """
        Validate all input parameters before processing.

        Args:
            service_name: Unique service identifier
            wsdl_definitions: List of WSDL service definitions with associated XSD files
            wsdl_uri: [DEPRECATED] Single WSDL URI for backward compatibility
            xsd_uri: [DEPRECATED] Single XSD URI for backward compatibility
            openapi_uri: Path or URL to OpenAPI file
            output_base_path: Base directory for outputs
            **context: Airflow context

        Returns:
            Dict containing validated parameters

        Raises:
            AirflowException: If any parameter validation fails
        """
        logger.info(f"Validating parameters for service: {service_name}")

        # Validate service name format
        import re
        if not re.match(r"^[a-zA-Z0-9_-]+$", service_name):
            raise AirflowException(
                f"Invalid service_name '{service_name}'. Must contain only alphanumeric characters, underscores, and hyphens."
            )

        # Handle backward compatibility: convert single WSDL/XSD to new format
        if wsdl_uri and not wsdl_definitions:
            logger.info("Converting legacy single WSDL/XSD format to new multi-service format")
            wsdl_definitions = [{
                "service_name": service_name,
                "wsdl_uri": wsdl_uri,
                "xsd_uris": [xsd_uri] if xsd_uri else []
            }]
        elif not wsdl_definitions:
            raise AirflowException("Either wsdl_definitions or wsdl_uri must be provided")

        # Validate required parameters
        if not openapi_uri or not openapi_uri.strip():
            raise AirflowException("openapi_uri cannot be empty")
        if not output_base_path or not output_base_path.strip():
            raise AirflowException("output_base_path cannot be empty")

        # Validate WSDL definitions structure
        if not isinstance(wsdl_definitions, list) or not wsdl_definitions:
            raise AirflowException("wsdl_definitions must be a non-empty list")

        validated_wsdl_definitions = []
        for i, wsdl_def in enumerate(wsdl_definitions):
            if not isinstance(wsdl_def, dict):
                raise AirflowException(f"wsdl_definitions[{i}] must be a dictionary")

            def_service_name = wsdl_def.get("service_name")
            def_wsdl_uri = wsdl_def.get("wsdl_uri")
            def_xsd_uris = wsdl_def.get("xsd_uris", [])

            if not def_service_name:
                raise AirflowException(f"wsdl_definitions[{i}] missing required 'service_name'")
            if not def_wsdl_uri:
                raise AirflowException(f"wsdl_definitions[{i}] missing required 'wsdl_uri'")
            if not isinstance(def_xsd_uris, list):
                raise AirflowException(f"wsdl_definitions[{i}]['xsd_uris'] must be a list")

            # Validate service name format
            if not re.match(r"^[a-zA-Z0-9_-]+$", def_service_name):
                raise AirflowException(f"Invalid service_name format in wsdl_definitions[{i}]: {def_service_name}")

            # Check if WSDL file exists for local paths
            if not def_wsdl_uri.startswith(("http://", "https://", "s3://", "gs://")):
                if not os.path.exists(def_wsdl_uri):
                    raise AirflowException(f"Local WSDL file not found: {def_wsdl_uri}")

            # Check if XSD files exist for local paths
            for xsd_uri_check in def_xsd_uris:
                if not xsd_uri_check.startswith(("http://", "https://", "s3://", "gs://")):
                    if not os.path.exists(xsd_uri_check):
                        raise AirflowException(f"Local XSD file not found: {xsd_uri_check}")

            validated_wsdl_definitions.append({
                "service_name": def_service_name,
                "wsdl_uri": def_wsdl_uri,
                "xsd_uris": def_xsd_uris
            })

        # Check OpenAPI file
        if openapi_uri and not openapi_uri.startswith(("http://", "https://", "s3://", "gs://")):
            if not os.path.exists(openapi_uri):
                raise AirflowException(f"Local OpenAPI file not found: {openapi_uri}")

        logger.info(f"All parameters validated successfully. Processing {len(validated_wsdl_definitions)} WSDL service(s)")
        return {
            "service_name": service_name,
            "wsdl_definitions": validated_wsdl_definitions,
            "wsdl_uri": wsdl_uri,  # Keep for backward compatibility
            "xsd_uri": xsd_uri,    # Keep for backward compatibility
            "openapi_uri": openapi_uri,
            "output_base_path": output_base_path,
            "validation_timestamp": pendulum.now().isoformat()
        }

    @task(outlets=[PARSED_DEFINITIONS_DS])
    def fetch_and_parse_definitions(validated_params: Dict[str, Any], **context: Context) -> Dict[str, Any]:
        """
        Fetch all service definitions, parse them, and store the results.

        Args:
            validated_params: Dictionary containing validated input parameters
            **context: Airflow context

        Returns:
            Dict containing metadata about parsed files and their locations

        Raises:
            AirflowException: If any parsing or storage operation fails
        """
        service_name = validated_params["service_name"]
        wsdl_definitions = validated_params["wsdl_definitions"]
        openapi_uri = validated_params["openapi_uri"]
        output_base_path = validated_params["output_base_path"]

        logger.info(f"Starting definition parsing for service: {service_name}")
        logger.info(f"Processing {len(wsdl_definitions)} WSDL service definition(s)")

        # Create timestamp for versioning
        timestamp = pendulum.now().timestamp()
        version_dir = f"{output_base_path}/{service_name}/v{timestamp}"

        try:
            os.makedirs(version_dir, exist_ok=True)
            logger.info(f"Created output directory: {version_dir}")
        except OSError as e:
            raise AirflowException(f"Failed to create output directory {version_dir}: {str(e)}")

        parsed_outputs = {
            "service_name": service_name,
            "timestamp": timestamp,
            "version_dir": version_dir,
            "files": {},
            "processing_start": pendulum.now().isoformat(),
            "wsdl_services": {}
        }

        # Multi-WSDL and XSD Processing
        try:
            logger.info("Processing multiple WSDL service definitions with associated XSD files")

            # Use the new multi-WSDL parsing function
            multi_wsdl_results = data_parsers.parse_multiple_wsdl_with_xsd(
                wsdl_definitions=wsdl_definitions,
                base_path=output_base_path
            )

            # Store the complete multi-service results
            multi_wsdl_output_path = f"{version_dir}/parsed_multi_wsdl.json"
            with open(multi_wsdl_output_path, 'w', encoding='utf-8') as f:
                json.dump(multi_wsdl_results, f, indent=2, ensure_ascii=False)

            parsed_outputs["files"]["multi_wsdl"] = multi_wsdl_output_path
            logger.info(f"Multi-WSDL results stored at: {multi_wsdl_output_path}")

            # Process each service individually for backward compatibility and detailed storage
            total_wsdl_types = 0
            total_xsd_types = 0

            for service_def_name, service_data in multi_wsdl_results["services"].items():
                logger.info(f"Processing individual service: {service_def_name}")

                # Store individual WSDL
                wsdl_output_path = f"{version_dir}/parsed_wsdl_{service_def_name}.json"
                with open(wsdl_output_path, 'w', encoding='utf-8') as f:
                    json.dump(service_data["wsdl"], f, indent=2, ensure_ascii=False)

                # Extract and store WSDL type definitions
                try:
                    wsdl_content = data_parsers._load_content_from_uri(
                        service_data["source_wsdl_uri"],
                        output_base_path
                    )
                    wsdl_types = type_definition_parser.extract_wsdl_types(wsdl_content)
                    wsdl_types_path = f"{version_dir}/parsed_wsdl_types_{service_def_name}.json"
                    with open(wsdl_types_path, 'w', encoding='utf-8') as f:
                        json.dump(wsdl_types, f, indent=2, ensure_ascii=False)

                    total_wsdl_types += wsdl_types['metadata']['total_types']
                    logger.info(f"WSDL type definitions for {service_def_name} extracted: {wsdl_types['metadata']['total_types']} types")
                except Exception as e:
                    logger.warning(f"Failed to extract WSDL type definitions for {service_def_name}: {str(e)}")

                # Store individual XSD schemas
                if service_data["xsd_schemas"]:
                    for xsd_uri, xsd_data in service_data["xsd_schemas"].items():
                        # Create safe filename from URI
                        xsd_filename = xsd_uri.replace('/', '_').replace('\\', '_').replace(':', '_')
                        xsd_output_path = f"{version_dir}/parsed_xsd_{service_def_name}_{xsd_filename}.json"
                        with open(xsd_output_path, 'w', encoding='utf-8') as f:
                            json.dump(xsd_data, f, indent=2, ensure_ascii=False)

                        # Extract and store XSD type definitions
                        try:
                            xsd_content = data_parsers._load_content_from_uri(xsd_uri, output_base_path)
                            xsd_types = type_definition_parser.extract_xsd_types(xsd_content)
                            xsd_types_path = f"{version_dir}/parsed_xsd_types_{service_def_name}_{xsd_filename}.json"
                            with open(xsd_types_path, 'w', encoding='utf-8') as f:
                                json.dump(xsd_types, f, indent=2, ensure_ascii=False)

                            total_xsd_types += xsd_types['metadata']['total_types']
                            logger.info(f"XSD type definitions for {service_def_name} from {xsd_uri}: {xsd_types['metadata']['total_types']} types")
                        except Exception as e:
                            logger.warning(f"Failed to extract XSD type definitions for {service_def_name} from {xsd_uri}: {str(e)}")

                # Store service-specific metadata
                parsed_outputs["wsdl_services"][service_def_name] = {
                    "wsdl_file": wsdl_output_path,
                    "xsd_files": list(service_data["xsd_schemas"].keys()),
                    "resolved_imports": service_data["resolved_imports"]
                }

            # Store summary information for backward compatibility
            if len(multi_wsdl_results["services"]) == 1:
                # Single service - maintain backward compatibility
                first_service = list(multi_wsdl_results["services"].values())[0]
                parsed_outputs["files"]["wsdl"] = f"{version_dir}/parsed_wsdl_{list(multi_wsdl_results['services'].keys())[0]}.json"
                if first_service["xsd_schemas"]:
                    first_xsd_key = list(first_service["xsd_schemas"].keys())[0]
                    xsd_filename = first_xsd_key.replace('/', '_').replace('\\', '_').replace(':', '_')
                    parsed_outputs["files"]["xsd"] = f"{version_dir}/parsed_xsd_{list(multi_wsdl_results['services'].keys())[0]}_{xsd_filename}.json"

            logger.info(f"Successfully processed {len(multi_wsdl_results['services'])} WSDL service(s)")
            logger.info(f"Total WSDL types extracted: {total_wsdl_types}")
            logger.info(f"Total XSD types extracted: {total_xsd_types}")

            if multi_wsdl_results["metadata"]["parsing_errors"]:
                logger.warning(f"Parsing errors encountered: {multi_wsdl_results['metadata']['parsing_errors']}")

        except Exception as e:
            error_msg = f"Error processing WSDL definitions: {str(e)}"
            logger.error(error_msg)
            raise AirflowException(error_msg)

        # OpenAPI Processing
        try:
            print(f"Reading OpenAPI from: {openapi_uri}")
            with open(openapi_uri, 'r') as f:
                openapi_content = f.read()
            parsed_openapi = data_parsers.parse_openapi_content(openapi_content)
            if not validation_utils.validate_parsed_data_schema(parsed_openapi, "openapi_parsed"):
                 raise ValueError("Parsed OpenAPI data does not conform to expected schema.")

            # Store parsed OpenAPI
            openapi_out_path = f"{version_dir}/parsed_openapi.json"
            os.makedirs(os.path.dirname(openapi_out_path), exist_ok=True)
            with open(openapi_out_path, "w") as f:
                json.dump(parsed_openapi, f, indent=2)
            print(f"Stored parsed OpenAPI to {openapi_out_path}")
            parsed_outputs["files"]["parsed_openapi_path"] = openapi_out_path

            # Extract and store OpenAPI type definitions
            try:
                openapi_types = type_definition_parser.extract_openapi_types(openapi_content)
                openapi_types_path = f"{version_dir}/parsed_openapi_types.json"
                with open(openapi_types_path, 'w', encoding='utf-8') as f:
                    json.dump(openapi_types, f, indent=2, ensure_ascii=False)

                parsed_outputs["files"]["openapi_types"] = openapi_types_path
                logger.info(f"OpenAPI type definitions extracted and stored at: {openapi_types_path}")
                logger.info(f"Extracted {openapi_types['metadata']['total_types']} type definitions from OpenAPI")
            except Exception as e:
                logger.warning(f"Failed to extract OpenAPI type definitions: {str(e)}")
                parsed_outputs["files"]["openapi_types_error"] = str(e)
        except Exception as e:
            print(f"Error processing OpenAPI {openapi_uri}: {e}")
            parsed_outputs["files"]["parsed_openapi_error"] = str(e)

        if not parsed_outputs["files"].get("parsed_wsdl_path") and not parsed_outputs["files"].get("parsed_openapi_path"):
            raise ValueError("Failed to parse both WSDL and OpenAPI definitions.")

        # Create manifest file
        manifest_path = f"{version_dir}/manifest.json"
        with open(manifest_path, "w") as f:
            json.dump(parsed_outputs, f, indent=2)

        return parsed_outputs

    @task
    def register_in_catalog(parsed_outputs: dict) -> dict:
        """
        Register the parsed definitions in a central catalog for reuse by other DAGs.
        """
        service_name = parsed_outputs["service_name"]
        timestamp = parsed_outputs["timestamp"]

        # Create a catalog entry with metadata
        catalog_entry = {
            "service_name": service_name,
            "version": timestamp,
            "last_updated": pendulum.now().isoformat(),
            "files": parsed_outputs["files"],
            "status": "available"
        }

        # Get existing catalog or create new one
        try:
            service_catalog = Variable.get("service_definitions_catalog", deserialize_json=True)
        except (KeyError, ValueError):
            service_catalog = {"services": {}, "last_updated": None}

        # Update the catalog with this service
        service_catalog["services"][service_name] = catalog_entry
        service_catalog["last_updated"] = pendulum.now().isoformat()

        # Save back to Variables
        Variable.set("service_definitions_catalog", json.dumps(service_catalog))

        # Create a "latest" directory for easy access
        version_dir = parsed_outputs["version_dir"]
        latest_dir = f"{os.path.dirname(version_dir)}/latest"

        # Remove existing latest directory if it exists
        if os.path.exists(latest_dir) and os.path.isdir(latest_dir):
            import shutil
            shutil.rmtree(latest_dir)

        # Create latest directory and copy files
        os.makedirs(latest_dir, exist_ok=True)

        # Copy all files from version directory to latest
        for filename in os.listdir(version_dir):
            src_file = f"{version_dir}/{filename}"
            dst_file = f"{latest_dir}/{filename}"
            if os.path.isfile(src_file):
                shutil.copy2(src_file, dst_file)

        print(f"Registered service '{service_name}' in catalog with version {timestamp}")
        print(f"Created 'latest' directory at {latest_dir}")

        return catalog_entry

    # --- Task Execution Flow ---
    # Step 1: Validate all input parameters
    validated_params = validate_parameters(
        service_name="{{ params.service_name }}",
        wsdl_definitions="{{ params.wsdl_definitions }}",
        wsdl_uri="{{ params.wsdl_uri }}",
        xsd_uri="{{ params.xsd_uri }}",
        openapi_uri="{{ params.openapi_uri }}",
        output_base_path="{{ params.output_base_path }}"
    )

    # Step 2: Fetch, parse, and store all service definitions
    parsed_outputs = fetch_and_parse_definitions(validated_params)

    # Step 3: Register service in catalog for downstream DAG consumption
    catalog_entry = register_in_catalog(parsed_outputs)

    # Define task dependencies (explicit for clarity)
    validated_params >> parsed_outputs >> catalog_entry

# Instantiate the DAG
ingest_and_parse_definitions_dag_instance = ingest_and_parse_definitions_dag()