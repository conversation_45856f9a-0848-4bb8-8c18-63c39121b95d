#!/usr/bin/env python3
"""
Test DAG to debug import issues.
"""

import sys
import os

print("=== DEBUG INFO ===")
print(f"PYTHONPATH: {os.environ.get('PYTHONPATH', 'Not set')}")
print(f"Current working directory: {os.getcwd()}")
print(f"__file__: {__file__}")
print(f"sys.path before modification:")
for i, path in enumerate(sys.path):
    print(f"  {i}: {path}")

# Add shared directory to Python path
shared_path = '/opt/airflow/shared'
print(f"\nChecking shared path: {shared_path}")
print(f"Path exists: {os.path.exists(shared_path)}")
if os.path.exists(shared_path):
    print(f"Contents: {os.listdir(shared_path)}")

if os.path.exists(shared_path) and shared_path not in sys.path:
    sys.path.insert(0, shared_path)
    print(f"Added {shared_path} to sys.path")
else:
    print(f"Shared path already in sys.path or doesn't exist")

print(f"\nsys.path after modification:")
for i, path in enumerate(sys.path):
    print(f"  {i}: {path}")

# Test import
try:
    print(f"\nTesting import...")
    from shared.common import constants
    print("✅ Import successful!")
except Exception as e:
    print(f"❌ Import failed: {e}")
    print(f"Exception type: {type(e)}")

print("=== END DEBUG ===")
