{"timestamp":"2025-07-19T18:07:13.542212","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:58.771594","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:36.877449","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:07.260368","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:16.299386","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:13.954393","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:45.682307","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:16.487615","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:51.358391","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:24.917688","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:55.041592","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:26.535049","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:04.283886","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:35.546268","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:05.815402","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:36.851735","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:15.787822","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:26.966290","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_branch_python_dop_operator_3.py","logger":"airflow.models.dagbag.DagBag"}
