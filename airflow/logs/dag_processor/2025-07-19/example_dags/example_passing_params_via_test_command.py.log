{"timestamp":"2025-07-19T18:07:20.526338","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:49.797365","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:20.647074","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:53.604131","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:03.516135","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:41.520931","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:16.627876","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:48.290207","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:18.785878","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:12.805361","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:43.125971","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:13.855554","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:44.090110","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:18.325472","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:49.131972","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:19.323368","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:57.253079","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:29.541218","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:26.231164","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_passing_params_via_test_command.py","logger":"airflow.models.dagbag.DagBag"}
