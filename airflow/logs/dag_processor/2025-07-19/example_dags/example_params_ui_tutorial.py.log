{"timestamp":"2025-07-19T18:05:23.469512","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:45.984016","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:01.869708","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:39.629987","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:10.907078","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:41.520930","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:16.621016","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:48.119819","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:18.763576","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:12.648611","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:45.079708","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:16.237262","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:47.531337","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:18.427467","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:49.116009","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:19.301447","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:57.719688","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:29.834948","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:19.382130","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:43:00.778703","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_params_ui_tutorial.py","logger":"airflow.models.dagbag.DagBag"}
