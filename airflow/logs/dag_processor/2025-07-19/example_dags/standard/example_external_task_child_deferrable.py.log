{"timestamp":"2025-07-19T18:05:24.716847","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:49.954194","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:51.101771","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:22.388151","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:54.785178","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:04.205046","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:59.592446","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:32.284098","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:02.758212","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:33.351207","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:20.387746","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:51.032402","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:21.274003","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:59.768209","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:31.238411","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:01.831926","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:34.875339","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:15.394159","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:41:54.250206","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:28.607527","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
