{"timestamp":"2025-07-19T18:06:01.401976","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:09:08.578048","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:02.038638","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:39.668046","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:11.047586","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:05.962795","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:37.367450","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:08.709450","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:39.114991","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:23.601705","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:54.065392","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:25.030197","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:11.808636","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:42.795123","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:13.936060","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:47.197217","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:18.148075","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:10.067923","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:41.392020","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_decorator.py","logger":"airflow.models.dagbag.DagBag"}
