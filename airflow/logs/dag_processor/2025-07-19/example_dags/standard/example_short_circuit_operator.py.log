{"timestamp":"2025-07-19T18:06:51.191051","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:09:43.249801","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:58.366115","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:32.908292","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:06.508213","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:48.863394","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:20.293479","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:50.780072","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:21.967369","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:17.087205","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:47.878543","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:18.530253","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:54.783605","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:26.003510","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:56.730506","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:27.667337","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:14.968317","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:24.702353","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:44:46.006266","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_short_circuit_operator.py","logger":"airflow.models.dagbag.DagBag"}
