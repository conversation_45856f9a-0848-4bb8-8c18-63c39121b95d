{"timestamp":"2025-07-19T18:05:06.337093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:05:06.429025","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":39,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:08:05.314469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:06.685410","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":39,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:15:49.199924","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:49.251598","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":39,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:20.121663","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:20.176432","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":39,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:53.218879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:53.256367","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":39,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:17:56.066870","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:17:56.247173","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":39,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:32:15.977933","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:16.053293","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:13.603309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:13.652455","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:44.965984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:45.037384","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:15.191708","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:15.203310","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:48.722268","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:48.952784","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:24.679905","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:24.719041","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:54.863733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:54.879184","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:36:26.108045","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:26.205580","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:01.927837","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:02.033639","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:33.258606","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:33.337551","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:38:04.342382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:04.359411","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:38:35.196988","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:35.257660","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:39:11.899521","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:12.811037","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:41:51.346942","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:41:51.638828","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:42:28.441107","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:28.471007","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:16.173399","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:16.207330","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:46.868134","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:46.880309","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:18.161756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:18.248559","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:48.755918","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:48.767636","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:25.354106","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:25.408501","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:55.573446","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:55.595303","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:09:26.634802","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:09:26.655609","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:28.839338","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:28.905628","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:59.431332","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:59.452410","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:11:29.638889","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:11:29.655447","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:11:51.456559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:11:51.535359","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:22.017768","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:22.036123","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:52.455302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:52.471487","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:13:23.302370","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:13:23.326779","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:13:54.240594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:13:54.265374","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:24.663881","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:24.684691","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:55.490465","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:55.509642","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:26.586075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:26.610179","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:59.051076","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:59.152007","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:29.469554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:29.483305","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:59.917010","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:59.934948","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:17:32.634526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:17:32.652611","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:03.260463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:03.274302","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:34.221192","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:34.233278","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:26:03.882738","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:03.917716","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:26:34.219688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:34.231891","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:05.218604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:05.234411","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:36.041718","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:36.049964","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:06.784351","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:06.794837","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:37.580005","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:37.592584","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:08.341355","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:08.350965","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:39.084794","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:39.095213","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:09.815639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:09.824460","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:40.472019","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:40.484114","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:11.413710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:11.445360","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:42.027058","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:42.036580","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:12.819754","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:12.829346","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:43.464498","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:43.485192","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:14.294590","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:14.304370","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:45.054827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:45.064822","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:15.893647","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:15.904169","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:46.701276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:46.711751","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:17.511679","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:17.521252","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:48.367410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:48.381985","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:19.027059","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:19.036781","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:49.799353","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:49.809880","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:20.669959","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:20.681599","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:51.438068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:51.447413","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:22.170063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:22.181548","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:52.961835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:52.972304","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:23.794230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:23.805862","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:54.589177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:54.599118","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:25.317520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:25.329683","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:56.179007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:56.189345","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:26.913257","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:26.922864","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:57.886102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:57.898226","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:28.742060","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:28.754427","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:59.458121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:59.467650","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:43:30.174047","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:43:30.184552","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:00.854946","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:00.868835","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:32.489803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:32.524497","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:02.797694","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:02.805980","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:33.165026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:33.174520","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:03.534071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:03.542148","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:33.814182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:33.825749","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:04.808130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:04.817376","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:35.709302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:35.717363","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:06.588023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:06.595820","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:37.579588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:37.587325","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:08.514777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:08.522578","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:39.341909","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:39.349249","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:10.162596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:10.170061","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:40.947950","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:40.955378","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:11.851713","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:11.859325","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:42.162823","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:42.178317","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:13.268895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:13.278033","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:44.096352","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:44.103700","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:14.997575","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:15.005274","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:45.743863","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:45.751075","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:16.496724","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:16.504325","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:47.304096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:47.314713","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:18.386518","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:18.400006","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:49.323678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:49.336362","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:20.422539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:20.433191","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:51.809250","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:51.824157","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:22.499793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:22.513121","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:53.261756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:53.274519","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:24.022776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:24.033881","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:54.727845","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:54.736915","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:25.979097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:26.014196","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:56.324233","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:56.338759","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:27.136004","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:27.147453","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:58.156710","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:58.172195","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:01:29.118648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:01:29.163149","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:00.218316","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:00.229497","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:31.630364","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:31.669032","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:14.486555","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:14.591639","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:44.819288","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:44.834052","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:15.691161","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:15.707384","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:46.728032","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:46.757562","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:17.976363","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:17.994479","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:48.500166","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:48.530993","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:11:18.908821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:11:18.920658","level":"error","event":"Failed to import: /opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/01_ingest_and_parse_definitions_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
