{"timestamp":"2025-07-19T19:15:24.379042","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:24.385822Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.392877Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393059Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393153Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393232Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393301Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393378Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393444Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393515Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393584Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393666Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393759Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393873Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.393951Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394016Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394097Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394159Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394232Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394298Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394390Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394539Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394608Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394670Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394738Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394793Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394841Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394905Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.394961Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.395010Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.395115Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.395181Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.395230Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.395275Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.395359Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:24.395441Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:55.855232","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:57.647306Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.648009Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.648291Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.648523Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.648623Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.648698Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.648789Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.648877Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.649134Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.649234Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.649296Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.649375Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.649433Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.649506Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.649575Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.649665Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.649785Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.650141Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.650449Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.650612Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.650683Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.650746Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.650824Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.651028Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.651205Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.651505Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.651634Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.651820Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.651891Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.652003Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.652142Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.652239Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.652543Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.652643Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:15:57.652711Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.147151","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:29.170198Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.172355Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.172632Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.172708Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.172772Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.173508Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.173582Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.173648Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.173703Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.173748Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.173791Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.173835Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.173878Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.173924Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.174031Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.174085Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.174928Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.183554Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.184135Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.184245Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.185107Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186042Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186118Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186168Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186213Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186259Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186302Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186344Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186385Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186428Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186470Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186512Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186563Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186615Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:29.186656Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.704427","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:59.715630Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.723979Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.725201Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.726253Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.726472Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.726537Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.726594Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.726639Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.726690Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.726748Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.726840Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.726940Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.727007Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.727112Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.727239Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.727290Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.728087Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730142Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730388Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730444Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730521Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730569Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730613Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730656Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730700Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730742Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730785Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730826Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.730868Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.731109Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.731212Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.731265Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.736309Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.736640Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:16:59.736696Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:31.572604","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:17:32.072057Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.073360Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.073586Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.073716Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.073979Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.074138Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.075668Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.076131Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.076357Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.077984Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.078205Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.078398Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.078515Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.078625Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.078772Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.079806Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080058Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080199Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080281Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080362Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080437Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080496Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080541Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080586Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080662Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080730Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080853Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.080933Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.081203Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.081909Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.082033Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.082103Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.087886Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.088552Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:17:32.088791Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.102649","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:03.116288Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.116879Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.117004Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.117166Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.117379Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.117484Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.117647Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.118063Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.118163Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.118273Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.118516Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.118609Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.118800Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.118911Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.119027Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.119128Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.119289Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124016Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124198Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124346Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124455Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124512Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124590Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124660Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124721Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124805Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.124878Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.125024Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.125079Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.125123Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.125183Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.125273Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.125351Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.125425Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:03.125496Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.106438","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:34.112906Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.113135Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.113340Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.113878Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.114009Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.114117Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.114194Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.114254Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.114319Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.114395Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.114462Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.116576Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.117401Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.117489Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.117538Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.117600Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.125878Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.128460Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.128791Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.128870Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.128935Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129051Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129115Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129169Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129221Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129289Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129351Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129444Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129515Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129591Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129660Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129715Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129776Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129833Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:18:34.129883Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:25:59.921262","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:03.379911Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.380961Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.381235Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.381569Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.382067Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.382367Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.382659Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.383140Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.383468Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.383669Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.383902Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.384119Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.384589Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.384898Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.385248Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.385610Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.385746Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.385916Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.386269Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.386495Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.386704Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.386913Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.387119Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.387301Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.387475Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.387756Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.388097Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.388403Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.388627Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.388824Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.389028Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.389228Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.390702Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.391009Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:03.391204Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.002071","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:34.019336Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.019560Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.019752Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.022204Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.022601Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.024390Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.026630Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.026890Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027038Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027122Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027202Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027262Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027332Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027391Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027442Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027508Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027595Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027674Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027736Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027812Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027873Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.027930Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028009Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028086Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028145Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028203Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028274Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028325Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028380Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028425Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028469Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.028534Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.031701Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.031855Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:26:34.031906Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.953177","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:04.969240Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.969488Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.972566Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.972806Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.972990Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.973160Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.973348Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.973487Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.973661Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.973827Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.974118Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.974267Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.974452Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.974816Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.974942Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.975005Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.975053Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.980031Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.980455Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.980706Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.980863Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.981035Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.981187Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.981272Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.981398Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.981484Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.981767Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.981849Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.981960Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.982022Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.982083Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.982151Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.982364Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.982448Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:04.982536Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.926234","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:35.936355Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.936752Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.936842Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.938557Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.939301Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.940263Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.940856Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941244Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941340Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941423Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941488Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941548Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941616Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941677Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941724Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941781Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941841Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941895Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.941961Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942025Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942082Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942138Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942183Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942244Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942289Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942363Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942411Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942484Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942551Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942614Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942680Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.942744Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.945186Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.945349Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:27:35.945402Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.667838","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:06.679733Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.679891Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.679955Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680015Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680090Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680141Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680195Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680256Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680316Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680381Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680435Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680482Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680526Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680568Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680611Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680656Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.680950Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681013Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681064Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681173Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681220Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681264Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681311Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681353Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681395Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681437Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681484Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681524Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681563Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681604Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681654Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681693Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681732Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681777Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:06.681815Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.447515","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:37.457231Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.459090Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.459628Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.460408Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.461089Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463040Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463206Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463278Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463345Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463416Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463465Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463515Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463611Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463785Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463841Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463892Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.463971Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.467180Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.467546Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469025Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469136Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469198Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469244Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469289Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469339Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469385Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469427Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469468Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469512Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469554Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469593Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469633Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469674Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469716Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:28:37.469762Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.244784","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:08.251471Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.252092Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.252174Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.253347Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.254351Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255294Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255450Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255500Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255549Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255596Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255639Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255695Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255756Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255815Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255881Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255939Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.255995Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.256054Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.256107Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.256149Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.256195Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.256239Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.256294Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.256335Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.256389Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.257056Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.257129Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.257175Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.257219Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.257262Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.257307Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.257351Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.259794Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.259878Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:08.260011Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.975241","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:38.982954Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.983200Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.983383Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.984208Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.984372Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.985346Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.985541Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.985741Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.985820Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.985880Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.985928Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.985974Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986026Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986070Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986117Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986279Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986369Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986444Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986507Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986565Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986620Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986667Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986729Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986774Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986825Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986894Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986946Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.986991Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.987034Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.987109Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.987188Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.987249Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.987307Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.987411Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:29:38.987464Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.719627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:09.728049Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728232Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728327Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728414Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728472Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728519Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728581Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728647Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728718Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728779Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728830Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728874Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728921Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.728964Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729010Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729053Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729103Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729155Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729219Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729263Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729315Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729356Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729397Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729466Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729512Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729588Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729633Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729673Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729712Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729753Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729794Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729836Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729877Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729919Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:09.729960Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.361131","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:40.371213Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.371968Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.372166Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.372926Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.373720Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.373899Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.374073Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.375308Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.376154Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.376493Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.376668Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.377306Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.377381Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.377426Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.377470Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.377536Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.377678Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.378911Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.379359Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.379930Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.380120Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.380933Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381017Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381073Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381122Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381169Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381213Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381262Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381305Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381348Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381391Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381434Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381477Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381522Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:30:40.381564Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.217265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:11.224282Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.224404Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.225579Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.227559Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.227668Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.227742Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.227803Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.227853Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.227900Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.227945Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228013Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228058Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228102Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228168Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228258Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228316Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228361Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228412Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228475Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228530Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228576Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228635Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228693Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228746Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228809Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228870Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228917Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.228963Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.229011Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.229054Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.229097Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.229141Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.231385Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.231519Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:11.231686Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.924697","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:41.929496Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.930033Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.930119Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.930669Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.931154Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.931226Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.933231Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.934243Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.934935Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.935041Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.935094Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.935153Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.935201Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.935277Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.935344Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.935395Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.935455Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.938413Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.938566Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.938625Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.938692Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.938782Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.938838Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.938900Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.938963Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939012Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939061Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939116Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939220Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939271Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939325Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939373Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939424Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939503Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:31:41.939673Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.696187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:12.703453Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.703635Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.704225Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.704956Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.705370Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.706417Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.707318Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.707479Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.707659Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.707815Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.707956Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.708102Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.709367Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.710832Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.711308Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.711387Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.711448Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.714739Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715313Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715397Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715455Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715501Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715557Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715603Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715647Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715689Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715732Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715800Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715844Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715885Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.715927Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.716687Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.717358Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.717591Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:12.717805Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.351976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:43.360181Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.360332Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.360472Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.360580Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.360663Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.360980Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.362536Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.363773Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.364161Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.364243Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.364302Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.364352Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.364401Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.364453Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.365207Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.365384Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.365864Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.368473Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.369197Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.369459Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.370353Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.370605Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.370665Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.371297Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.371364Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.371407Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.371449Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.371518Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.371632Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.371678Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.371848Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.371895Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.375117Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.375478Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:32:43.375546Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.181227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:14.187400Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.190262Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.191626Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.192263Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.193014Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.194195Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.196194Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.198202Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.199197Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.199766Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.199888Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.199954Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.200002Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.200072Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.200121Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.200193Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.200251Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.203357Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.203538Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.203644Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.203763Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.204426Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207013Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207129Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207202Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207270Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207325Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207377Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207437Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207501Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207554Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207608Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207666Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207725Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:14.207776Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.948982","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:44.955774Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.956399Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.956527Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.956619Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.956719Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.956820Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.956935Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.957027Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.957161Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.957810Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.957918Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.957999Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.958102Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.958205Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.958297Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.958397Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.958491Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.962981Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963186Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963259Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963320Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963378Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963432Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963476Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963530Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963586Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963643Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963703Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963750Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963801Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963856Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.963927Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.964000Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.964080Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:33:44.964126Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.777602","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:15.782723Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.782935Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.783511Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.783630Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.785971Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.786299Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.786897Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787009Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787090Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787154Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787202Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787252Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787312Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787382Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787438Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787489Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.787535Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.793810Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.793944Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.794040Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.794724Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.794903Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.795281Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.795417Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.795574Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.796428Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.796867Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.797129Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.797811Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.798190Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.798895Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.798987Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.799380Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.799507Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:15.799597Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.587856","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:46.602519Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.605315Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.607246Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.607352Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.607475Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.607523Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.608094Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.608207Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.608348Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.608543Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.608592Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.608643Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.609312Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.611661Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.611783Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.611851Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.611905Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.611962Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612017Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612077Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612137Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612187Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612251Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612316Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612370Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612427Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612517Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612576Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612644Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612727Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612784Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612839Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612891Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612944Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:34:46.612997Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.402687","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:17.411222Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.413754Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.414750Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.415029Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.415784Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.417725Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.419429Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.419817Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.419921Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.419981Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.420053Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.420124Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.420180Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.420232Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.420280Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.420333Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.420481Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.424049Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.424694Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.424872Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.424923Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.424970Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425017Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425061Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425107Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425159Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425220Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425277Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425333Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425390Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425437Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425550Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425618Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425683Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:17.425731Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.248410","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:48.255918Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.256873Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.257890Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.258557Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.258831Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.259883Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.261838Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262088Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262177Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262246Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262310Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262367Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262412Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262527Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262586Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262645Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262700Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262749Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262810Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262865Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262914Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262954Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.262994Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.263035Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.263076Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.263117Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.263188Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.263289Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.263361Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.263442Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.263508Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.263582Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.266466Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.266837Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:35:48.267026Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.923000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:18.931178Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.931436Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.932460Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.932596Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.933412Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.934419Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.936461Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938133Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938255Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938329Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938411Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938507Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938588Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938668Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938718Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938781Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938831Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938911Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.938974Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939019Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939065Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939109Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939155Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939201Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939248Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939295Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939342Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939385Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939429Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939473Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939535Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.939612Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.942210Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.942328Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:18.942400Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.688466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:49.694680Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.694861Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.697387Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.697976Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.698102Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.698664Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.698769Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.699528Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.700474Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.700590Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.700649Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.700712Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.700775Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.700829Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.700875Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.700937Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.700988Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701031Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701078Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701131Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701195Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701248Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701302Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701349Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701391Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701458Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701513Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701556Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.701597Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.704276Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.705153Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.705240Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.705378Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.705426Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:36:49.706008Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.554623","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:20.561392Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.561715Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.564011Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.564361Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.564431Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.564505Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.564576Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.564649Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.564728Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.564822Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.564911Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565009Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565095Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565166Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565252Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565331Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565422Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565492Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565564Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565645Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565749Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.565914Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566019Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566111Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566279Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566374Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566452Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566542Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566625Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566702Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566776Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.566858Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.572145Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.572825Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:20.572905Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.344675","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:51.353495Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.353700Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.353775Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.353829Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.353905Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.353968Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354034Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354103Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354171Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354227Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354271Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354318Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354363Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354407Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354455Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354512Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354566Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354647Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354722Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354779Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354832Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354893Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.354958Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355019Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355072Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355130Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355177Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355222Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355265Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355318Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355405Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355498Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355585Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355694Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:37:51.355763Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.069681","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:22.076978Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.077629Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.077725Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.078355Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.078418Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.079338Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.080187Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.081576Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.082053Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.082426Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.082516Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.082601Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.082658Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.082712Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.082758Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.082808Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.082855Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085051Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085138Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085200Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085276Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085331Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085376Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085448Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085533Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085600Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085651Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085701Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085749Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085806Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085869Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085917Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.085961Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.086007Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:22.086090Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.849102","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:52.856067Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.856198Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.856961Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.857035Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.857425Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.858118Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.858195Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.858814Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.859790Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.860567Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.861816Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.861892Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.861940Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.861986Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.862029Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.862072Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.862113Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.864856Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865009Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865083Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865153Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865219Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865283Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865333Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865381Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865429Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865477Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865532Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865592Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865662Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865751Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865819Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865874Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.865923Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:38:52.866017Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.664434","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:23.670490Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.670805Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.671445Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.671558Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.671655Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.671716Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.671767Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.671818Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.671864Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.671934Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.671999Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.672068Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.672154Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.673882Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.674841Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.674977Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.675293Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.683911Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.684664Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.684879Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.684974Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.685773Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.685834Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.685949Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.685999Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.686216Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.686271Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.686353Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.688105Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.689734Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.690743Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.690820Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.690883Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.690936Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:23.690981Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.477048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:54.483296Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.483799Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.484434Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.485520Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.486546Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.486821Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.487919Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488302Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488387Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488477Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488558Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488622Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488681Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488728Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488775Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488839Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.488904Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.491275Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.491372Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.491455Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.491529Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.491578Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.491720Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.491883Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.491936Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.491992Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.492118Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.492165Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.492732Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.493823Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.494785Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.495450Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.495542Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.495614Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:39:54.495671Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.204073","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:25.209553Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.210147Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.210213Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.210274Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.210331Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.210922Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.212909Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213044Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213121Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213172Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213219Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213268Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213315Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213383Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213455Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213555Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213603Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213654Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213708Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213751Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213795Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213837Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213889Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213932Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.213974Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.214015Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.214058Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.214099Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.214149Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.214190Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.214231Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.214271Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.220076Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.220355Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:25.220598Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.068864","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:56.078883Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079048Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079102Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079150Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079207Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079251Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079306Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079358Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079407Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079504Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079550Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079596Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079642Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079683Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079725Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079774Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079821Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079864Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079915Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.079971Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080016Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080069Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080110Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080151Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080192Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080235Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080275Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080324Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080363Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080403Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080448Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080487Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080528Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080569Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:40:56.080620Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.813420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:26.819841Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.820020Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.822391Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.823446Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.823527Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.823639Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.823783Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.823971Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824054Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824116Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824186Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824269Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824331Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824391Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824439Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824486Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824536Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824588Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824641Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824687Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824739Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824801Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824856Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824902Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824945Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.824991Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.825051Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.825104Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.825164Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.825223Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.825281Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.825353Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.827919Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.830378Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:26.831010Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.678062","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:57.694066Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.694812Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.695446Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.696284Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.697278Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.697521Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.697896Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.698654Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.699335Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.699627Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.699788Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.699984Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.700449Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.701229Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.701923Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.702346Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.702635Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.702937Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.703113Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.703309Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.703473Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.703643Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.703753Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.703916Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.704004Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.704262Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.704742Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.704852Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.705408Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.705678Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.705829Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.705963Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.722470Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.723247Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:41:57.723826Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.590067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:28.599808Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.609872Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.609997Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.610114Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.610575Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.610664Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.619332Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.620245Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.623458Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.623788Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.623908Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.624031Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.624128Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.624267Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.624501Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.624626Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628021Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628162Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628237Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628393Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628468Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628565Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628642Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628716Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628798Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628887Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.628971Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.629042Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.629112Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.629185Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.629737Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.630639Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.631403Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.633695Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:28.634180Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.341170","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:59.347128Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.348898Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.349177Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.350355Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.352316Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.353530Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.354428Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.354652Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.355598Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.356597Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.357345Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.359621Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.360339Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.360598Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.360713Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.360765Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.361417Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.361511Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.361604Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.361832Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.361931Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.362006Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.362076Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.362146Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.363622Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.364590Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.365207Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.365280Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.365333Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.365395Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.365460Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.365512Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.367773Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.367848Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:42:59.367902Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.055297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:43:30.066578Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.067270Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.067604Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.068021Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.068354Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.068892Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070031Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070136Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070201Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070262Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070340Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070394Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070448Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070499Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070552Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070620Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070680Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070738Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070783Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070831Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070888Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070928Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.070969Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071008Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071047Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071099Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071161Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071201Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071245Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071298Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071346Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071386Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071425Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071465Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:43:30.071505Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.758312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:00.765998Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.766175Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.767476Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.769459Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770022Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770103Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770155Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770285Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770417Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770472Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770528Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770603Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770668Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770722Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770790Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770844Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770890Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770941Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.770994Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771036Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771077Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771128Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771190Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771234Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771274Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771324Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771364Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771415Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771459Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771498Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771575Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.771615Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.774425Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.775427Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:00.775496Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.039625","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:32.062929Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.064711Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.065901Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.068717Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.069274Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.069566Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.069700Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.070046Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.070386Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.071140Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.071479Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.071620Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.071893Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.072138Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.072519Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.073408Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.074237Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.074603Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.074722Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.075595Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.076918Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.077765Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.078652Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.078794Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.081046Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.081855Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.082853Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.083767Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.084801Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.085632Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.085846Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.086626Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.088399Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.089055Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:44:32.089251Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.601520","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:02.618070Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.620927Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.621771Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.622489Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.622634Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.622705Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.622769Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.622836Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.622902Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.622960Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623005Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623050Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623098Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623143Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623198Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623294Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623462Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623615Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623707Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623760Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623867Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.623966Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624075Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624129Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624177Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624235Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624332Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624409Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624457Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624515Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624568Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624624Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624712Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624773Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:02.624835Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.011891","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:33.023974Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.028870Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.030711Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.031437Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.031760Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.032640Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.032722Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.032783Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.032851Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.032910Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.032956Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033027Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033075Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033121Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033168Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033221Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033284Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033342Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033401Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033463Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033515Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033558Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033623Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.033685Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.034852Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.035190Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.035655Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.036632Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.036702Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.036750Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.036795Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.036841Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.036883Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.036927Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:45:33.036968Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.348551","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:03.360206Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.362534Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.363418Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.364413Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.364672Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.364961Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.365100Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.365265Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.365613Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.365679Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.365727Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.366323Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.366463Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.366582Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.366672Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.366794Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.366880Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.375612Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.376527Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.376861Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.377061Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.378674Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.378775Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.379555Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.380052Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.380176Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.380411Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.381481Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.381849Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.382019Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.383577Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.387529Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.389466Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.389833Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:03.390027Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.682150","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:33.692393Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.692759Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.692848Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.692910Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.694774Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.695436Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.695765Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.698866Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699031Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699097Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699145Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699203Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699251Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699296Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699341Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699385Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699436Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699491Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699536Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699580Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699621Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699663Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699713Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699755Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699797Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699841Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699884Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699926Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.699991Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.700040Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.700085Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.700127Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.700169Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.700212Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:46:33.700253Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.671382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:04.680528Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.681132Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.681450Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683109Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683240Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683358Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683464Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683549Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683632Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683694Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683760Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683813Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683862Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683926Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.683998Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.684072Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.684142Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.690427Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.691145Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.691837Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.691943Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.692102Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.692837Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.692974Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.693089Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.693203Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.693479Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.693573Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.693645Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.693730Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.693807Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.695313Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.696270Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.697117Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:04.697213Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.596814","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:35.602674Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.602831Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.603435Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.604120Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.604300Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.606313Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.608311Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.608666Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.608754Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.608809Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.608857Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.608905Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.608954Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.609011Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.609072Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.609150Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.609266Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.611826Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.611981Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612053Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612106Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612153Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612200Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612250Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612296Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612359Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612408Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612470Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612531Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612585Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612631Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612678Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612742Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612816Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:47:35.612869Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.454186","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:06.464464Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.465254Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.465557Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.467577Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.467743Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.467838Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.467931Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468017Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468085Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468152Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468239Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468315Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468379Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468453Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468539Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468613Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468690Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468762Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468863Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.468940Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469025Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469105Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469170Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469235Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469306Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469379Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469477Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469561Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469653Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469712Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469756Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.469800Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.474934Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.475713Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:06.476737Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.452947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:37.464983Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.465528Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.466467Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470257Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470373Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470448Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470560Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470637Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470689Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470736Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470790Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470857Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470905Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.470971Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471029Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471105Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471174Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471240Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471296Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471340Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471385Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471436Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471503Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471563Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471616Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471660Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471721Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471788Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471845Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471905Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.471958Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.472011Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.474700Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.474889Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:48:37.474954Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.374326","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:08.386166Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.386961Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387272Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387362Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387425Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387482Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387543Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387600Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387674Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387759Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387835Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387915Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.387986Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388044Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388094Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388159Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388224Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388290Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388354Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388411Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388464Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388520Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388578Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388624Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388671Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388719Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388823Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388892Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388954Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.388999Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.389071Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.389225Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.389292Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.389356Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:08.389402Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.228857","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:39.238982Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.239346Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.239968Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.240181Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.240489Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.241527Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.242479Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.242583Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.242661Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.242736Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.242808Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.242874Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.242939Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.242984Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.243034Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.243091Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.243140Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.246490Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.246706Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.247659Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.248433Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.248591Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.248647Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.249308Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.249411Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.249475Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.249628Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.249680Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.250366Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.250475Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.250609Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.250677Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.250889Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.250964Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:49:39.251032Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.059155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:10.066057Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.066631Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.066796Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.066869Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.066949Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.067001Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.067056Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.067110Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.067839Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.069769Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.069910Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.069962Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.070008Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.070054Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.070137Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.070182Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.070227Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.073332Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.073981Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.074757Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.074933Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.074992Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075038Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075082Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075128Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075172Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075221Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075263Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075306Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075349Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075391Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075433Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075478Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075523Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:10.075565Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.847311","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:40.852584Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.857817Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.858685Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.858788Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.858901Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.858971Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859048Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859112Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859204Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859283Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859350Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859405Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859451Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859536Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859611Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859675Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859741Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859799Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859849Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859897Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859945Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.859992Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860037Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860081Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860124Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860171Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860218Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860265Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860311Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860381Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860439Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860484Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860528Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860579Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:50:40.860639Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.714313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:11.723099Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.724447Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.724720Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.725025Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.726376Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.727117Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.727785Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.727930Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.728017Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.728093Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.728172Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.728253Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.728320Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.728389Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.728473Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.728553Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.729918Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.730282Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.730985Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.731776Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.731860Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.731916Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732027Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732105Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732155Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732205Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732249Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732306Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732348Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732392Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732437Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.732517Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.737148Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.737333Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:11.737429Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.974330","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:41.982991Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.983852Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.983989Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.985378Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.985995Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.986267Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.989253Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.989659Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.989941Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.990151Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.990214Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.990279Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.993188Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.993573Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.993891Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.993959Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.994063Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.996974Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997151Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997255Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997321Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997445Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997526Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997585Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997647Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997753Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997818Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997892Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.997955Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.998018Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.998072Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.998174Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.998338Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.998425Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:51:41.999161Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.144075","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:13.152880Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.155229Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.155362Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.155591Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.157685Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.159812Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.160699Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.160859Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.161696Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.161829Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.161980Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.162113Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.162161Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.162985Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.163076Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.163148Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.163290Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168208Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168404Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168465Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168520Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168574Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168628Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168679Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168732Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168783Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168835Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168919Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.168993Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.169064Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.169122Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.169174Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.169225Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.169276Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:13.169326Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:43.992261","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:43.997710Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:43.997933Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:43.998018Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:43.998590Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:43.998779Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.001282Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.002277Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.002997Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.003105Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.003174Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.003241Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.003297Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.003377Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.003435Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.003482Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.003528Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.003574Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006077Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006249Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006390Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006465Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006530Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006587Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006636Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006702Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006760Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006820Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006888Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.006944Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.007000Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.007090Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.007148Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.007285Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.007342Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:52:44.007505Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.869827","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:14.876638Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.877073Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.877484Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.878066Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.878337Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.878425Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.878563Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.881683Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.881893Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.882015Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.882092Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.882181Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.882276Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.882426Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.882534Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.882637Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.882754Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885176Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885302Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885359Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885406Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885453Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885497Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885539Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885583Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885633Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.885678Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.887774Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.887861Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.887929Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.887983Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.888028Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.888072Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.888117Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:14.888161Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.640252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:45.648206Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.648529Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.649251Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.649328Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.650051Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.650371Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.651059Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.652856Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.653003Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.653095Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.653173Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.653228Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.653276Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.653323Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.653370Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.653424Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.656082Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.656202Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.657060Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.658052Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659054Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659266Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659344Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659402Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659457Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659512Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659564Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659625Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659688Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659782Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659840Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659890Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.659951Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.660018Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:53:45.660069Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.388008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:16.395335Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.395495Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.396132Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.396817Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.397784Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.399883Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.401316Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.401552Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.401652Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.401715Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.401841Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.401908Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.401956Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.402001Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.402071Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.402134Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.402186Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.404875Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.404991Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405059Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405113Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405159Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405205Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405286Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405337Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405394Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405447Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405509Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405571Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405638Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405710Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405811Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405881Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405948Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:16.405997Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.180001","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:47.185146Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.186260Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.186697Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.187223Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.187306Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.189196Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.189330Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.191219Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.192317Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.192514Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.192660Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.192823Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.192902Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.193465Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.193516Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.193562Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.193772Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.197386Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.198320Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.199183Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.200357Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.200625Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.200722Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.200888Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.200941Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.200990Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.201036Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.201085Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.201141Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.201188Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.201232Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.201276Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.201339Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.201408Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:54:47.201456Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.219743","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:18.234773Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.235604Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.236702Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.237632Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.238580Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.238741Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.238854Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.238970Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.239125Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.239205Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.239347Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.239470Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.240536Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.240808Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.241591Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242142Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242250Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242312Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242377Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242435Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242488Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242672Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242738Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242914Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.242968Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.243027Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.243585Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.244095Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.245589Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.246571Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.246646Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.246698Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.246749Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.246802Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:18.246852Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.150048","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:49.158344Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.158544Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.158642Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.158734Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.158847Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.158941Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159032Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159110Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159190Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159270Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159345Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159491Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159577Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159663Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159732Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159797Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159869Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159926Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.159996Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160070Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160133Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160214Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160270Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160326Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160402Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160462Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160519Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160580Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160632Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160699Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160755Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160800Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160853Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.160906Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:55:49.161003Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.301379","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:20.307592Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.309482Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.310426Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.311512Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.311601Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.316659Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317120Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317220Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317306Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317396Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317486Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317549Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317608Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317685Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317761Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317823Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317894Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317952Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.317999Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318046Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318102Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318152Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318195Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318241Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318290Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318356Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318440Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318537Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318598Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318655Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318709Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318756Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318803Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318849Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:20.318893Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.652054","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:51.662981Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667079Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667273Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667391Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667473Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667549Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667612Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667692Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667742Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667792Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667891Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.667960Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.668013Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.668074Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.668153Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.668209Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.670994Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.671448Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.671525Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.671601Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.671681Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.671815Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.671887Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.671941Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.671989Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.672050Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.672119Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.672186Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.672244Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.672340Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.672421Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.672469Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.675260Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.675477Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:56:51.675546Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.387598","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:22.396841Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.397818Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.398265Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.398335Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.398923Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.402740Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.402898Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.402970Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403038Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403090Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403140Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403194Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403239Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403297Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403424Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403541Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403644Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403781Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.403915Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404001Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404065Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404156Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404267Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404355Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404415Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404488Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404549Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404605Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404708Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404796Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404858Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404920Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.404976Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.405028Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:22.405075Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.142466","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:53.150031Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.150624Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.151255Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.151346Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.151408Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.151470Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.151550Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153186Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153361Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153439Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153504Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153553Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153602Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153661Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153716Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153860Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.153938Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154015Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154096Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154163Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154231Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154310Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154389Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154451Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154494Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154537Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154584Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154628Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154672Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.154717Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.155059Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.157851Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.161078Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.161834Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:57:53.161934Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.919469","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:23.925144Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.925712Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.926467Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.927087Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.929102Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.929910Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930115Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930200Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930255Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930303Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930350Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930396Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930443Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930490Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930537Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930588Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.930636Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.932983Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.933087Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.933191Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.933275Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.933379Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.933542Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.933607Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.933999Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.934061Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.934109Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.934151Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.934193Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.934237Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.934282Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.934325Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.934390Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.934433Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:23.935088Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.625529","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:54.630872Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631010Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631110Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631218Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631317Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631402Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631486Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631565Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631681Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631769Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631852Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.631948Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632018Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632075Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632211Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632292Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632359Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632425Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632491Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632547Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632602Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632657Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632709Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632761Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632811Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632893Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.632963Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.633019Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.633069Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.633163Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.633228Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.633284Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.635662Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.635789Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:58:54.635850Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.773094","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:25.782404Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.792012Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.793763Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.795653Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.797102Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.797715Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.797969Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.799148Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.800943Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.801140Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.801218Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.801296Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.801350Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.801422Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.801479Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.801583Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.801709Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.801942Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.802015Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.802095Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.802144Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.802190Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.802935Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803000Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803073Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803130Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803193Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803247Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803295Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803343Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803388Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803432Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803477Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803525Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:25.803588Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.146544","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:56.162825Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.163631Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.163841Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.164606Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.165541Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.165823Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.165997Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.166133Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.166268Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.166460Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.168635Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.169496Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.169628Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.169686Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.169734Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.169883Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.170017Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.176584Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.176695Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.176746Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.177647Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.177738Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.177807Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.177881Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178003Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178384Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178450Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178510Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178558Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178614Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178665Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178710Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178761Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.178805Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T19:59:56.179693Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.023413","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:27.033850Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.034514Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.034689Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.034765Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.034824Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.034891Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.034986Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035043Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035100Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035148Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035191Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035238Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035286Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035335Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035393Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035456Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035508Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035563Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035620Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035674Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035719Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035763Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035825Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035914Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.035970Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036016Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036062Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036117Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036172Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036227Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036288Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036348Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036419Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036478Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:27.036533Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.020015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:58.026974Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.027153Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.029472Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030335Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030437Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030575Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030631Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030677Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030722Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030770Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030820Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030865Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030919Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.030973Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.031017Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.031065Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.031112Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.031162Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.031214Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.031255Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.031298Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.031905Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.031988Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.032065Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.032116Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.032165Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.032211Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.032258Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.032314Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.032406Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.032461Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.032507Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.035731Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.035917Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:00:58.035996Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:28.987505","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:01:28.996101Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:28.998012Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:28.998492Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:28.998953Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:28.999067Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:28.999299Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.000994Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.002254Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.002624Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.002995Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.003882Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.004003Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.004171Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.004259Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.004798Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.004929Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.005029Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010148Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010273Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010329Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010378Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010423Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010467Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010534Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010576Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010618Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010659Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010700Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010741Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010783Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010824Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010865Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010906Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010949Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:01:29.010989Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.062023","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:00.071229Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.071762Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.072031Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.072133Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.072287Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.072495Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.072644Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.075797Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.076062Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.076136Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.076251Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.076310Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.076356Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.076426Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.076479Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.076526Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.076573Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.082605Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.083866Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.084362Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.085157Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.085510Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.086264Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.086860Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.087039Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.087255Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.087620Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.087722Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.087869Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.087955Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.088101Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.088251Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.088322Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.088388Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:00.088509Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.205931","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:31.270717Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.271591Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.271986Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.272580Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.273797Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.274064Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.275214Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.277203Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.277468Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.278014Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.278243Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.278313Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.278507Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.280747Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.283626Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.285854Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.287328Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.289063Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.289391Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.289603Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.289774Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.289955Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.290434Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.290716Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.290846Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.291158Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.291921Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.292585Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.293695Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.294763Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.297529Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.297904Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.305784Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.306027Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:02:31.306139Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:06.080212","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:12.190670Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.191022Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.191125Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.191212Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.191398Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.191470Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.191817Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.192061Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.192238Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.192440Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.192607Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.192822Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.193002Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.193130Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.193224Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.193297Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.193371Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.193497Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.194562Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.194641Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.194708Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.194790Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.194868Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.194922Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.194976Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.195031Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.195087Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.195220Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.195324Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.195384Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.195430Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.195474Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.199740Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.202245Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:12.203853Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.155209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:43.180990Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.205860Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.207352Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.209535Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.212345Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.215235Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.217118Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.217896Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.221170Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.222874Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.224510Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.225200Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.226345Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.227240Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.228197Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.229188Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.231433Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.232746Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233088Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233219Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233335Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233430Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233521Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233610Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233693Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233786Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233874Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.233994Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.234044Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.234092Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.234188Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.234318Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.234414Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.234502Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:08:43.234579Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.501932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:13.511652Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.521342Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.522698Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.522878Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.522966Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523020Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523075Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523145Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523204Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523281Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523344Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523395Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523440Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523491Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523547Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523602Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523667Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523729Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523783Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523845Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523902Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.523972Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524017Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524061Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524106Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524151Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524194Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524240Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524283Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524327Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524370Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524417Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524461Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524506Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:13.524569Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.506224","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:44.515964Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.516141Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.518531Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.519336Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.519458Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.519537Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.519608Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.519685Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.519741Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.519791Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.519855Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.520054Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.521814Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522002Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522104Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522205Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522259Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522331Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522423Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522503Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522573Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522636Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522690Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522741Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522787Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522841Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522885Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522930Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.522973Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.523018Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.523062Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.523108Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.525847Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.526020Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:09:44.526126Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.611577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:15.626127Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.626383Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.626475Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.627457Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.629500Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.630456Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.631129Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.631333Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.631502Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.631645Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.631769Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.631920Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.632032Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.632163Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.632276Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.632362Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.632565Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.632892Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.633189Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.633436Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.633713Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.633839Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.633928Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.634040Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.634134Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.634226Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.634350Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.634492Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.634565Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.634636Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.634694Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.634740Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.638334Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.638469Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:15.638577Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.903361","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:45.920986Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.921821Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.922237Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.923708Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.923937Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.924368Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.924782Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.925791Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.926002Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.926906Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.927760Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.927966Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.928155Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.928902Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.932657Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.933479Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.934789Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.934883Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.934961Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.935190Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.935273Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.935328Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.935426Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.935548Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.935694Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.935874Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.936122Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.936345Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.936482Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.936535Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.936582Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.936626Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.936702Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.937962Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:10:45.938095Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.732183","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/test_import.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:11:16.740186Z","level":"info","event":"=== DEBUG INFO ===","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.740872Z","level":"info","event":"PYTHONPATH: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.741009Z","level":"info","event":"Current working directory: /opt/airflow","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.741658Z","level":"info","event":"__file__: /opt/airflow/dags/test_import.py","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.742125Z","level":"info","event":"sys.path before modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.742337Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.742448Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.742725Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.742796Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.742859Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.743582Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.744565Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.745661Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.746494Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.749425Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.750419Z","level":"info","event":"Checking shared path: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.750580Z","level":"info","event":"Path exists: True","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.757655Z","level":"info","event":"Contents: ['config', '__init__.py', '__pycache__', 'common', 'sample_data']","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.757754Z","level":"info","event":"Shared path already in sys.path or doesn't exist","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.757808Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.757856Z","level":"info","event":"sys.path after modification:","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.757901Z","level":"info","event":"  0: /home/<USER>/.local/bin","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758178Z","level":"info","event":"  1: /opt/airflow/shared","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758229Z","level":"info","event":"  2: /usr/local/lib/python312.zip","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758272Z","level":"info","event":"  3: /usr/local/lib/python3.12","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758323Z","level":"info","event":"  4: /usr/local/lib/python3.12/lib-dynload","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758414Z","level":"info","event":"  5: /home/<USER>/.local/lib/python3.12/site-packages","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758461Z","level":"info","event":"  6: /opt/airflow/config","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758788Z","level":"info","event":"  7: /opt/airflow/plugins","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758840Z","level":"info","event":"  8: /opt/airflow/dags","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758882Z","level":"info","event":"","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758924Z","level":"info","event":"Testing import...","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.758968Z","level":"info","event":"❌ Import failed: No module named 'shared'","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.759012Z","level":"info","event":"Exception type: <class 'ModuleNotFoundError'>","chan":"stdout","logger":"processor"}
{"timestamp":"2025-07-19T20:11:16.759055Z","level":"info","event":"=== END DEBUG ===","chan":"stdout","logger":"processor"}
