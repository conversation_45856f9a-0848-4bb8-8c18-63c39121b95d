{"timestamp":"2025-07-19T18:04:57.461550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:04:57.849307","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:08:00.912124","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:01.310627","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:15:42.845426","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:43.045204","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:19.672756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:19.733701","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:52.327079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:52.694280","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:17:40.961377","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:17:41.200239","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:18:15.320941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:15.501706","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:32:16.022746","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:16.097909","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:13.768360","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:13.837432","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:45.090460","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:45.144619","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:15.243387","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:15.251939","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:49.215673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:49.711117","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:24.740000","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:24.763326","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:54.980579","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:54.988833","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:36:26.392833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:26.430472","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:02.851639","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:03.090648","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:33.822265","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:33.865521","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:38:04.342765","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:04.359410","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:38:35.384898","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:35.449674","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:39:13.853833","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:13.909688","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:41:51.357204","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:41:51.728034","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:42:28.493312","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:28.512915","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:16.330087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:16.361805","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:46.929989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:46.941102","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:18.376895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:18.424994","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:48.777336","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:48.795711","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:25.410403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:25.459327","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:55.625540","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:55.633566","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:09:26.667068","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:09:26.678449","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:28.884894","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:28.976581","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:59.464404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:59.474045","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:11:29.663683","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:11:29.682828","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:00.924997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:00.966680","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:04.321196","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:04.361447","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:34.767187","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:34.790660","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:13:05.384648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:13:05.394613","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:13:36.462561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:13:36.484466","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:06.937130","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:06.946708","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:37.803276","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:37.829242","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:08.758497","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:08.777558","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:58.612128","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:58.707319","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:29.282471","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:29.301216","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:59.819269","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:59.840525","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:17:32.541242","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:17:32.564195","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:03.218935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:03.237124","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:34.185543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:34.192633","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:26:03.706921","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:03.755794","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:26:34.220896","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:34.240448","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:05.129313","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:05.147929","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:36.015840","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:36.030023","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:06.743650","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:06.755733","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:37.530044","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:37.543016","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:08.309938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:08.321994","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:39.044421","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:39.058480","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:09.796490","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:09.806664","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:40.431026","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:40.441952","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:11.279777","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:11.297893","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:41.988810","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:41.998627","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:12.783243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:12.792007","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:43.414263","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:43.423972","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:14.264581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:14.273878","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:45.016508","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:45.026384","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:15.857008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:15.866443","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:46.665645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:46.674179","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:17.475155","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:17.483619","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:48.325763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:48.334755","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:18.990087","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:18.998672","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:49.757420","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:49.768663","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:20.636222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:20.647419","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:51.405317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:51.415779","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:22.134463","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:22.143003","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:52.917182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:52.924954","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:23.746831","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:23.758424","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:54.544835","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:54.555974","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:25.272756","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:25.281921","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:56.139144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:56.148947","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:26.878580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:26.890486","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:57.837818","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:57.849602","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:28.687577","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:28.699458","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:59.415063","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:59.425980","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:43:30.130554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:43:30.140292","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:00.833230","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:00.843751","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:32.418137","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:32.446222","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:02.709803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:02.738046","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:33.139009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:33.152270","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:03.491378","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:03.504536","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:33.780559","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:33.798791","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:04.754097","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:04.764361","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:35.678171","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:35.690398","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:06.536780","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:06.545190","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:37.537653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:37.547868","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:08.453936","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:08.463731","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:39.307733","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:39.316427","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:10.127561","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:10.138284","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:40.912688","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:40.920763","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:11.814266","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:11.826973","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:42.069981","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:42.082868","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:13.219081","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:13.231170","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:44.061009","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:44.069356","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:14.962167","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:14.975087","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:45.703653","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:45.712978","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:16.465725","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:16.477359","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:47.245220","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:47.254708","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:18.316537","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:18.335422","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:49.270467","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:49.283873","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:20.382499","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:20.390295","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:51.760108","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:51.783174","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:22.461441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:22.471883","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:53.248383","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:53.261029","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:23.984938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:23.993009","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:54.685533","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:54.696215","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:25.920118","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:25.935610","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:56.277984","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:56.296965","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:27.094252","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:27.103542","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:58.108997","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:58.125793","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:01:29.055501","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:01:29.068827","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:00.164217","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:00.175135","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:31.590209","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:31.626148","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:14.054543","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:14.212223","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:44.727093","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:44.750945","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:15.633403","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:15.649053","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:46.654337","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:46.669507","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:17.902637","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:17.919935","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:48.312206","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:48.355466","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:11:18.854947","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:11:18.870685","level":"error","event":"Failed to import: /opt/airflow/dags/02_generate_training_data_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/02_generate_training_data_dag.py","lineno":43,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
