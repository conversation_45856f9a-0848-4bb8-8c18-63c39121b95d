{"timestamp":"2025-07-19T18:05:06.676454","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:05:06.784671","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:08:12.253302","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:08:12.782666","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:15:49.361482","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:15:49.475064","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:20.346941","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:20.382680","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:16:53.382751","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:16:53.419935","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:18:03.081226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:18:03.166093","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":41,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:32:08.513121","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:32:09.092336","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:13.195133","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:13.337417","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:33:44.305285","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:33:44.363336","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:14.944690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:14.964726","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:34:46.935847","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:34:46.962465","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:19.356634","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:19.431762","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:35:49.615539","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:35:49.635127","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:36:19.942556","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:19.955588","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:36:55.737989","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:36:55.875789","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:26.457550","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:26.494276","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:37:56.789489","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:37:56.809622","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:38:27.484943","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:38:27.513413","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:39:08.631731","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:08.730353","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:39:40.257600","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:39:40.298651","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:41:37.907677","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:41:39.463162","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T18:42:28.235309","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T18:42:28.271776","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:09.547596","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:09.686244","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:06:45.335763","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:06:45.402061","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:16.542317","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:16.591419","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:07:47.411174","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:07:47.446748","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:24.312382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:24.364351","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:08:55.381766","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:08:55.433329","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:09:26.424883","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:09:26.462412","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:25.053895","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:25.285262","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:10:59.048020","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:10:59.122956","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:11:29.446804","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:11:29.480331","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:00.187793","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:00.306261","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:12:31.467884","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:12:31.493770","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:13:02.217488","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:13:02.251838","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:13:33.148218","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:13:33.172642","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:03.795914","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:03.815050","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:14:34.592101","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:14:34.613487","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:05.453869","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:05.479800","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:15:59.050340","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:15:59.169871","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:29.532772","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:29.547051","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:16:59.962953","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:16:59.995178","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:17:33.073647","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:17:33.144777","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:03.284822","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:03.298226","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:18:34.237572","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:18:34.246522","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:26:03.960144","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:04.033172","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:26:34.278243","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:26:34.295943","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:05.215293","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:05.229566","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:27:36.087028","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:27:36.099999","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:06.801007","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:06.812273","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:28:37.595949","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:28:37.609520","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:08.359776","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:08.372794","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:29:39.102247","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:29:39.115757","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:09.849700","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:09.862173","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:30:40.489592","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:30:40.501457","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:11.454449","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:11.474575","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:31:42.042534","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:31:42.052815","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:12.832329","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:12.847343","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:32:43.476836","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:32:43.492794","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:14.312630","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:14.326332","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:33:45.069648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:33:45.081147","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:15.912195","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:15.921618","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:34:46.720977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:34:46.736418","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:17.526271","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:17.535706","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:35:48.392570","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:35:48.406268","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:19.042031","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:19.051184","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:36:49.816287","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:36:49.827218","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:20.689994","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:20.701370","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:37:51.455977","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:37:51.469404","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:22.185339","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:22.194630","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:38:52.973290","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:38:52.984654","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:23.812597","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:23.824114","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:39:54.605879","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:39:54.617964","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:25.333560","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:25.342281","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:40:56.190588","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:40:56.203342","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:26.928848","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:26.937085","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:41:57.903877","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:41:57.912239","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:28.755696","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:28.772282","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:42:59.469297","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:42:59.477922","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:43:30.190223","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:43:30.199331","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:00.881526","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:00.889858","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:44:32.555554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:44:32.677309","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:03.912734","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:03.923744","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:45:34.421990","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:45:34.461398","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:04.714638","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:04.734734","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:46:34.902067","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:46:34.915429","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:05.875458","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:05.887956","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:47:36.782758","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:47:36.792462","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:07.670648","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:07.683672","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:48:38.652690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:48:38.677599","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:09.606455","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:09.619562","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:49:40.415998","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:49:40.429057","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:11.231372","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:11.242777","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:50:42.053872","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:50:42.068101","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:13.121968","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:13.188538","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:51:43.367865","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:51:43.388077","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:14.327678","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:14.340596","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:52:45.171803","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:52:45.185767","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:16.045809","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:16.061679","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:53:46.789017","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:53:46.798831","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:17.571238","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:17.585471","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:54:48.391860","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:54:48.402714","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:18.437468","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:18.448938","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:55:49.326557","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:55:49.341655","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:20.451223","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:20.463533","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:56:51.839022","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:56:51.854878","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:22.514580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:22.529788","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:57:53.303928","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:57:53.314923","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:24.039655","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:24.049126","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:58:54.767008","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:58:54.781047","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:26.046392","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:26.091265","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T19:59:56.344841","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T19:59:56.359255","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:27.155964","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:27.170055","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:00:58.183554","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:00:58.197592","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:01:29.180938","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:01:29.196315","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:00.235389","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:00.257134","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:02:31.787096","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:02:31.809577","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:14.620992","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:14.701994","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:08:44.853052","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:08:44.873116","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:15.721821","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:15.735186","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:09:46.755084","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:09:46.778986","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:17.991226","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:18.014632","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:10:48.497594","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:10:48.540430","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-19T20:11:18.921382","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-19T20:11:18.938744","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":42,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
