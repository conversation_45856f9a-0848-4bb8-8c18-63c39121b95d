{"timestamp":"2025-07-10T14:04:56.853874","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:04:56.926563","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:05:35.474222","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:05:35.506849","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:06:09.872673","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:09.945588","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:06:43.253563","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:43.831759","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:07:57.606090","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:07:58.160320","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:10:04.877404","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:10:05.476894","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:11:09.334926","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:09.751854","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:12:08.361157","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:08.817618","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:13:17.476935","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:13:18.445307","level":"error","event":"Failed to import: /opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/05_monitoring_and_retraining_trigger_dag.py","lineno":33,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
