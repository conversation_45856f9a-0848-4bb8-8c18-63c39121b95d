{"timestamp":"2025-07-10T14:04:56.998015","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:04:57.049667","level":"error","event":"Failed to import: /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/03_train_ml_model_dag.py","lineno":19,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:05:35.612160","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:05:35.642523","level":"error","event":"Failed to import: /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/03_train_ml_model_dag.py","lineno":19,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:06:10.130227","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:10.171696","level":"error","event":"Failed to import: /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/03_train_ml_model_dag.py","lineno":19,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:06:44.362535","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:44.448183","level":"error","event":"Failed to import: /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/03_train_ml_model_dag.py","lineno":19,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:07:58.985932","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:07:59.264670","level":"error","event":"Failed to import: /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/03_train_ml_model_dag.py","lineno":19,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:10:07.461376","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:10:07.773247","level":"error","event":"Failed to import: /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/03_train_ml_model_dag.py","lineno":19,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:11:11.990565","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:12.435796","level":"error","event":"Failed to import: /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/03_train_ml_model_dag.py","lineno":19,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:12:11.231119","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:11.343272","level":"error","event":"Failed to import: /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/03_train_ml_model_dag.py","lineno":19,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:13:22.182581","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:13:22.671176","level":"error","event":"Failed to import: /opt/airflow/dags/03_train_ml_model_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/03_train_ml_model_dag.py","lineno":19,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
