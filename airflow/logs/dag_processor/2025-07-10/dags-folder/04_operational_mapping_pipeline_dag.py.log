{"timestamp":"2025-07-10T14:04:56.850513","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:04:56.889465","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:05:35.558182","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:05:35.589647","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:06:09.884576","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:09.960433","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:06:43.609079","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:43.830207","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:07:58.110690","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:07:58.277715","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:10:07.086627","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:10:07.758543","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:11:11.301976","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:11.506133","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:12:10.625580","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:11.213086","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:13:19.509645","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:13:20.778066","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":32,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
