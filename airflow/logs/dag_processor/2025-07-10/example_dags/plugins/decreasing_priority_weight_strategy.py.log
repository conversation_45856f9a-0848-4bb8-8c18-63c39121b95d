{"timestamp":"2025-07-10T14:04:58.968971","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:05:36.085425","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:12.126572","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:45.210287","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:08:04.458470","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:10:13.248159","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:15.747805","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:15.868800","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:14:15.460500","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
