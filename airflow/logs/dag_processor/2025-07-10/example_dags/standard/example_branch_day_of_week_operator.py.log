{"timestamp":"2025-07-10T14:05:24.932689","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:04.687604","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:35.792208","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:07:12.610213","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:08:48.824112","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:10:47.018869","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:31.687710","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_day_of_week_operator.py","logger":"airflow.models.dagbag.DagBag"}
