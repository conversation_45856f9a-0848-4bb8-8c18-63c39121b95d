{"timestamp":"2025-07-10T14:05:29.082158","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_bash_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:13.529003","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_bash_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:45.641236","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_bash_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:09:06.794987","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_bash_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:03.031950","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_bash_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:13:02.382770","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_bash_operator.py","logger":"airflow.models.dagbag.DagBag"}
