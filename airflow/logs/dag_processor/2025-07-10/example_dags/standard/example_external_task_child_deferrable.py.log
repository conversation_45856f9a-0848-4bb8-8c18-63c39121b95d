{"timestamp":"2025-07-10T14:05:19.512175","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:05:53.212364","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:23.884021","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:57.057356","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:08:09.719943","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:10:16.989792","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:18.929631","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:19.468155","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:16:22.690572","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_child_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
