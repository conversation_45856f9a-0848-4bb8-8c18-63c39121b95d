{"timestamp":"2025-07-10T14:05:28.214753","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_marker_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:09.072228","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_marker_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:41.392629","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_marker_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:07:57.261257","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_marker_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:01.907731","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_marker_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:54.643445","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_marker_dag.py","logger":"airflow.models.dagbag.DagBag"}
