{"timestamp":"2025-07-10T14:05:27.997307","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:05:28.025089","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:06:09.076293","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:09.136425","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:06:44.668839","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:44.710638","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:08:53.407890","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:08:53.812448","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:11:01.246321","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:01.415277","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-10T14:12:52.871195","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:53.153160","level":"error","event":"Failed to import: /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'tests_common'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_external_task_parent_deferrable.py","lineno":60,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
