{"timestamp":"2025-07-10T14:05:31.625432","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:16.114786","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:48.366002","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:09:35.589745","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:41.637481","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
