{"timestamp":"2025-07-10T14:04:57.581490","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_trigger_controller_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:05:35.917804","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_trigger_controller_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:12.208734","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_trigger_controller_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:45.011846","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_trigger_controller_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:08:01.382112","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_trigger_controller_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:10:11.029740","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_trigger_controller_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:14.417996","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_trigger_controller_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:14.792851","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_trigger_controller_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:13:41.962839","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_trigger_controller_dag.py","logger":"airflow.models.dagbag.DagBag"}
