{"timestamp":"2025-07-10T14:05:28.213281","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:09.262477","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:06:44.693472","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:08:58.840630","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:11:01.964762","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-10T14:12:56.846288","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/example_dynamic_task_mapping_with_no_taskflow_operators.py","logger":"airflow.models.dagbag.DagBag"}
