{"timestamp":"2025-07-11T09:21:22.307241","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-11T09:24:40.065053","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/plugins/decreasing_priority_weight_strategy.py","logger":"airflow.models.dagbag.DagBag"}
