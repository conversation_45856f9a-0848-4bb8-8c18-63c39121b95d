{"timestamp":"2025-07-11T09:21:09.314153","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-11T09:22:29.990268","level":"info","event":"Filling up the DagBag from /home/<USER>/.local/lib/python3.12/site-packages/airflow/example_dags/standard/example_branch_datetime_operator.py","logger":"airflow.models.dagbag.DagBag"}
