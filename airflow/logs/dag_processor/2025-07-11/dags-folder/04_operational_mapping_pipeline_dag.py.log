{"timestamp":"2025-07-11T09:21:01.637441","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-11T09:21:01.833517","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
{"timestamp":"2025-07-11T09:22:10.205745","level":"info","event":"Filling up the DagBag from /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag"}
{"timestamp":"2025-07-11T09:22:10.252445","level":"error","event":"Failed to import: /opt/airflow/dags/04_operational_mapping_pipeline_dag.py","logger":"airflow.models.dagbag.DagBag","error_detail":[{"exc_type":"ModuleNotFoundError","exc_value":"No module named 'shared'","exc_notes":[],"syntax_error":null,"is_cause":false,"frames":[{"filename":"/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py","lineno":394,"name":"parse"},{"filename":"<frozen importlib._bootstrap_external>","lineno":999,"name":"exec_module"},{"filename":"<frozen importlib._bootstrap>","lineno":488,"name":"_call_with_frames_removed"},{"filename":"/opt/airflow/dags/04_operational_mapping_pipeline_dag.py","lineno":40,"name":"<module>"}],"is_group":false,"exceptions":[]}]}
