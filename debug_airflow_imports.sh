#!/bin/bash
# Debug script to check Airflow import issues

echo "🔍 DEBUGGING AIRFLOW IMPORT ISSUES"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "airflow/docker-compose.yaml" ]; then
    echo "❌ Please run this script from the project root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected files: airflow/docker-compose.yaml"
    exit 1
fi

echo "✅ Running from correct directory: $(pwd)"

# Check if shared directory exists
if [ ! -d "shared" ]; then
    echo "❌ Shared directory not found"
    exit 1
fi

echo "✅ Shared directory exists"

# Check if docker-compose has the volume mount
echo ""
echo "🔍 Checking docker-compose.yaml volume mounts..."
if grep -q "/opt/airflow/shared" airflow/docker-compose.yaml; then
    echo "✅ Shared volume mount found in docker-compose.yaml"
else
    echo "❌ Shared volume mount NOT found in docker-compose.yaml"
    echo "   Adding volume mount..."
    
    # Backup original file
    cp airflow/docker-compose.yaml airflow/docker-compose.yaml.backup
    
    # Add the volume mount after the plugins line
    sed -i.tmp '/plugins:/a\
    - ${AIRFLOW_PROJ_DIR:-.}/../shared:/opt/airflow/shared\
    - ${AIRFLOW_PROJ_DIR:-.}/../data:/opt/airflow/data' airflow/docker-compose.yaml
    
    echo "✅ Volume mounts added to docker-compose.yaml"
fi

# Change to airflow directory
cd airflow/

echo ""
echo "🐳 Checking Docker services status..."
if docker-compose ps | grep -q "Up"; then
    echo "⚠️  Airflow services are running. Need to restart for volume changes."
    echo "   Restarting services..."
    docker-compose down
    sleep 5
    docker-compose up -d
    echo "✅ Services restarted"
else
    echo "ℹ️  Starting Airflow services..."
    docker-compose up -d
fi

# Wait for services to be ready
echo ""
echo "⏳ Waiting for services to be ready..."
sleep 30

echo ""
echo "🧪 Testing shared module imports in container..."

# Test 1: Check if shared directory is mounted
echo "Test 1: Checking if shared directory is mounted..."
if docker-compose exec -T airflow-webserver test -d /opt/airflow/shared; then
    echo "✅ /opt/airflow/shared directory exists in container"
else
    echo "❌ /opt/airflow/shared directory NOT found in container"
    echo "   This indicates a volume mount issue"
fi

# Test 2: List contents of shared directory
echo ""
echo "Test 2: Listing shared directory contents..."
docker-compose exec -T airflow-webserver ls -la /opt/airflow/shared/ || echo "❌ Cannot list shared directory"

# Test 3: Check if shared/common exists
echo ""
echo "Test 3: Checking shared/common directory..."
docker-compose exec -T airflow-webserver test -d /opt/airflow/shared/common && echo "✅ shared/common exists" || echo "❌ shared/common NOT found"

# Test 4: Test Python import
echo ""
echo "Test 4: Testing Python import of shared modules..."
docker-compose exec -T airflow-webserver python -c "
import sys
print('Python path:')
for p in sys.path:
    print(f'  {p}')

print('')
print('Testing shared module import...')
sys.path.insert(0, '/opt/airflow/shared')

try:
    from shared.common import constants
    print('✅ Successfully imported shared.common.constants')
except Exception as e:
    print(f'❌ Failed to import shared.common.constants: {e}')

try:
    from shared.common import data_parsers
    print('✅ Successfully imported shared.common.data_parsers')
except Exception as e:
    print(f'❌ Failed to import shared.common.data_parsers: {e}')
"

# Test 5: Check DAG import errors
echo ""
echo "Test 5: Checking DAG import errors..."
docker-compose exec -T airflow-webserver airflow dags list-import-errors

echo ""
echo "🏁 Debug complete!"
echo ""
echo "If you see import errors above, try:"
echo "1. Ensure you're running from the project root directory"
echo "2. Restart Airflow services: cd airflow && docker-compose restart"
echo "3. Check volume mounts: docker-compose exec airflow-webserver ls -la /opt/airflow/"
