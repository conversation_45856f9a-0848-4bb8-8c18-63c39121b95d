#!/usr/bin/env python3
"""
Cleanup Script for Parsed Service Definition Data

This script provides comprehensive cleanup capabilities for parsed service definition data
generated by both the Airflow DAG and standalone parser. It can clean up versioned data,
service catalogs, and orphaned files.

Features:
- Clean all parsed data or specific services
- Keep only the latest N versions of each service
- Remove orphaned data not referenced in catalogs
- Dry run mode to preview changes
- Support for both standalone and Airflow environments
- Comprehensive logging and safety checks

Author: SOADA Airflow MVP Team
Version: 1.0.0
"""

import os
import sys
import json
import shutil
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Set, Optional, Tuple
from datetime import datetime
import re

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ParsedDataCleanup:
    """Main cleanup class for managing parsed service definition data."""
    
    def __init__(self, base_path: str = "data/parsed_definitions", 
                 catalog_path: str = "standalone/service_catalog.json",
                 dry_run: bool = False):
        """
        Initialize the cleanup utility.
        
        Args:
            base_path: Base directory containing parsed definitions
            catalog_path: Path to the service catalog JSON file
            dry_run: If True, only show what would be deleted without actually deleting
        """
        self.base_path = Path(base_path)
        self.catalog_path = Path(catalog_path)
        self.dry_run = dry_run
        self.deleted_files = 0
        self.deleted_dirs = 0
        self.freed_space = 0
        
        logger.info(f"Initialized cleanup utility:")
        logger.info(f"  Base path: {self.base_path}")
        logger.info(f"  Catalog path: {self.catalog_path}")
        logger.info(f"  Dry run mode: {self.dry_run}")
    
    def load_service_catalog(self) -> Dict:
        """Load the service catalog from JSON file."""
        try:
            if self.catalog_path.exists():
                with open(self.catalog_path, 'r') as f:
                    return json.load(f)
            else:
                logger.warning(f"Service catalog not found at {self.catalog_path}")
                return {"services": {}, "last_updated": None}
        except Exception as e:
            logger.error(f"Error loading service catalog: {e}")
            return {"services": {}, "last_updated": None}
    
    def save_service_catalog(self, catalog: Dict) -> None:
        """Save the service catalog to JSON file."""
        if self.dry_run:
            logger.info(f"[DRY RUN] Would update service catalog at {self.catalog_path}")
            return
        
        try:
            # Ensure directory exists
            self.catalog_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.catalog_path, 'w') as f:
                json.dump(catalog, f, indent=2)
            logger.info(f"Updated service catalog at {self.catalog_path}")
        except Exception as e:
            logger.error(f"Error saving service catalog: {e}")
    
    def get_directory_size(self, path: Path) -> int:
        """Calculate total size of directory in bytes."""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
        except Exception as e:
            logger.warning(f"Error calculating size for {path}: {e}")
        return total_size
    
    def format_size(self, size_bytes: int) -> str:
        """Format size in bytes to human readable format."""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def remove_path(self, path: Path) -> None:
        """Remove a file or directory with logging."""
        if not path.exists():
            return
        
        size = 0
        if path.is_file():
            size = path.stat().st_size
            if self.dry_run:
                logger.info(f"[DRY RUN] Would delete file: {path} ({self.format_size(size)})")
            else:
                path.unlink()
                logger.info(f"Deleted file: {path} ({self.format_size(size)})")
                self.deleted_files += 1
        elif path.is_dir():
            size = self.get_directory_size(path)
            if self.dry_run:
                logger.info(f"[DRY RUN] Would delete directory: {path} ({self.format_size(size)})")
            else:
                shutil.rmtree(path)
                logger.info(f"Deleted directory: {path} ({self.format_size(size)})")
                self.deleted_dirs += 1
        
        self.freed_space += size
    
    def get_service_directories(self) -> List[Path]:
        """Get list of all service directories in the base path."""
        if not self.base_path.exists():
            logger.warning(f"Base path {self.base_path} does not exist")
            return []
        
        return [d for d in self.base_path.iterdir() if d.is_dir()]
    
    def get_service_versions(self, service_dir: Path) -> List[Tuple[str, Path]]:
        """
        Get list of version directories for a service.
        
        Returns:
            List of (version_string, path) tuples sorted by version (newest first)
        """
        versions = []
        for item in service_dir.iterdir():
            if item.is_dir() and item.name != "latest":
                # Extract timestamp from version directory name (e.g., "v1751563876")
                match = re.match(r'v(\d+)', item.name)
                if match:
                    timestamp = int(match.group(1))
                    versions.append((timestamp, item))
        
        # Sort by timestamp (newest first)
        versions.sort(key=lambda x: x[0], reverse=True)
        return [(f"v{ts}", path) for ts, path in versions]
    
    def clean_all_data(self) -> None:
        """Remove all parsed data and reset service catalog."""
        logger.info("=== CLEANING ALL PARSED DATA ===")
        
        # Remove all service directories
        service_dirs = self.get_service_directories()
        for service_dir in service_dirs:
            logger.info(f"Removing service: {service_dir.name}")
            self.remove_path(service_dir)
        
        # Reset service catalog
        empty_catalog = {
            "services": {},
            "last_updated": datetime.now().isoformat()
        }
        self.save_service_catalog(empty_catalog)
        
        logger.info(f"Cleaned all parsed data from {self.base_path}")

    def clean_specific_services(self, service_names: List[str]) -> None:
        """Remove data for specific services."""
        logger.info(f"=== CLEANING SPECIFIC SERVICES: {', '.join(service_names)} ===")

        catalog = self.load_service_catalog()

        for service_name in service_names:
            service_dir = self.base_path / service_name

            if service_dir.exists():
                logger.info(f"Removing service directory: {service_name}")
                self.remove_path(service_dir)
            else:
                logger.warning(f"Service directory not found: {service_name}")

            # Remove from catalog
            if service_name in catalog.get("services", {}):
                if not self.dry_run:
                    del catalog["services"][service_name]
                    catalog["last_updated"] = datetime.now().isoformat()
                logger.info(f"Removed {service_name} from service catalog")
            else:
                logger.warning(f"Service {service_name} not found in catalog")

        # Save updated catalog
        if not self.dry_run:
            self.save_service_catalog(catalog)

    def clean_old_versions(self, keep_versions: int = 3) -> None:
        """Keep only the latest N versions of each service."""
        logger.info(f"=== CLEANING OLD VERSIONS (keeping latest {keep_versions}) ===")

        catalog = self.load_service_catalog()
        service_dirs = self.get_service_directories()

        for service_dir in service_dirs:
            service_name = service_dir.name
            logger.info(f"Processing service: {service_name}")

            versions = self.get_service_versions(service_dir)

            if len(versions) <= keep_versions:
                logger.info(f"  Service has {len(versions)} versions, keeping all")
                continue

            # Remove old versions
            versions_to_remove = versions[keep_versions:]
            logger.info(f"  Removing {len(versions_to_remove)} old versions")

            for version_name, version_path in versions_to_remove:
                logger.info(f"    Removing version: {version_name}")
                self.remove_path(version_path)

            # Update catalog to point to latest remaining version
            if versions and service_name in catalog.get("services", {}):
                latest_version = versions[0][0]  # First item is newest
                latest_path = versions[0][1]

                # Update file paths in catalog
                service_entry = catalog["services"][service_name]
                if not self.dry_run:
                    # Update version info
                    version_timestamp = int(latest_version[1:])  # Remove 'v' prefix
                    service_entry["version"] = version_timestamp
                    service_entry["last_updated"] = datetime.now().isoformat()

                    # Update file paths to point to latest version
                    for file_type, old_path in service_entry.get("files", {}).items():
                        if old_path and "../data/parsed_definitions" in old_path:
                            # Update path to latest version
                            new_path = f"../data/parsed_definitions/{service_name}/{latest_version}/{Path(old_path).name}"
                            service_entry["files"][file_type] = new_path

                logger.info(f"  Updated catalog to reference latest version: {latest_version}")

        # Save updated catalog
        if not self.dry_run:
            catalog["last_updated"] = datetime.now().isoformat()
            self.save_service_catalog(catalog)

    def clean_orphaned_data(self) -> None:
        """Remove data that's not referenced in the service catalog."""
        logger.info("=== CLEANING ORPHANED DATA ===")

        catalog = self.load_service_catalog()
        cataloged_services = set(catalog.get("services", {}).keys())

        service_dirs = self.get_service_directories()
        filesystem_services = {d.name for d in service_dirs}

        # Find orphaned services (in filesystem but not in catalog)
        orphaned_services = filesystem_services - cataloged_services

        if orphaned_services:
            logger.info(f"Found {len(orphaned_services)} orphaned services: {', '.join(orphaned_services)}")
            for service_name in orphaned_services:
                service_dir = self.base_path / service_name
                logger.info(f"Removing orphaned service: {service_name}")
                self.remove_path(service_dir)
        else:
            logger.info("No orphaned services found")

        # Find missing services (in catalog but not in filesystem)
        missing_services = cataloged_services - filesystem_services

        if missing_services:
            logger.info(f"Found {len(missing_services)} missing services in filesystem: {', '.join(missing_services)}")
            if not self.dry_run:
                for service_name in missing_services:
                    logger.info(f"Removing missing service from catalog: {service_name}")
                    del catalog["services"][service_name]

                catalog["last_updated"] = datetime.now().isoformat()
                self.save_service_catalog(catalog)
        else:
            logger.info("All cataloged services found in filesystem")

    def list_data_summary(self) -> None:
        """Display a summary of current parsed data."""
        logger.info("=== PARSED DATA SUMMARY ===")

        catalog = self.load_service_catalog()
        service_dirs = self.get_service_directories()

        total_size = 0
        total_versions = 0

        logger.info(f"Base path: {self.base_path}")
        logger.info(f"Catalog path: {self.catalog_path}")
        logger.info(f"Services in catalog: {len(catalog.get('services', {}))}")
        logger.info(f"Service directories: {len(service_dirs)}")
        logger.info("")

        for service_dir in service_dirs:
            service_name = service_dir.name
            versions = self.get_service_versions(service_dir)
            service_size = self.get_directory_size(service_dir)
            total_size += service_size
            total_versions += len(versions)

            in_catalog = service_name in catalog.get("services", {})
            status = "✓ Cataloged" if in_catalog else "⚠ Orphaned"

            logger.info(f"  {service_name}: {len(versions)} versions, {self.format_size(service_size)} [{status}]")

            # Show latest symlink status
            latest_dir = service_dir / "latest"
            if latest_dir.exists():
                if latest_dir.is_symlink():
                    target = latest_dir.readlink()
                    logger.info(f"    Latest symlink: {target}")
                else:
                    logger.info(f"    Latest directory: exists (not symlink)")
            else:
                logger.info(f"    Latest: not found")

        logger.info("")
        logger.info(f"Total: {len(service_dirs)} services, {total_versions} versions, {self.format_size(total_size)}")

        if catalog.get("last_updated"):
            logger.info(f"Catalog last updated: {catalog['last_updated']}")

    def print_summary(self) -> None:
        """Print cleanup summary."""
        logger.info("=== CLEANUP SUMMARY ===")
        logger.info(f"Files deleted: {self.deleted_files}")
        logger.info(f"Directories deleted: {self.deleted_dirs}")
        logger.info(f"Space freed: {self.format_size(self.freed_space)}")

        if self.dry_run:
            logger.info("NOTE: This was a dry run. No files were actually deleted.")


def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(
        description="Cleanup utility for parsed service definition data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Show current data summary
  python cleanup_parsed_data.py --summary

  # Clean all data (with confirmation)
  python cleanup_parsed_data.py --clean-all

  # Clean specific services
  python cleanup_parsed_data.py --clean-services service1 service2

  # Keep only latest 2 versions of each service
  python cleanup_parsed_data.py --clean-old-versions --keep 2

  # Remove orphaned data
  python cleanup_parsed_data.py --clean-orphaned

  # Dry run to see what would be deleted
  python cleanup_parsed_data.py --clean-all --dry-run

  # Use custom paths
  python cleanup_parsed_data.py --base-path /custom/path --catalog-path /custom/catalog.json --summary
        """
    )

    # Path configuration
    parser.add_argument(
        "--base-path",
        default="data/parsed_definitions",
        help="Base directory containing parsed definitions (default: data/parsed_definitions)"
    )

    parser.add_argument(
        "--catalog-path",
        default="standalone/service_catalog.json",
        help="Path to service catalog JSON file (default: standalone/service_catalog.json)"
    )

    # Action options (mutually exclusive)
    action_group = parser.add_mutually_exclusive_group(required=True)

    action_group.add_argument(
        "--summary",
        action="store_true",
        help="Display summary of current parsed data"
    )

    action_group.add_argument(
        "--clean-all",
        action="store_true",
        help="Remove all parsed data and reset service catalog"
    )

    action_group.add_argument(
        "--clean-services",
        nargs="+",
        metavar="SERVICE",
        help="Remove data for specific services"
    )

    action_group.add_argument(
        "--clean-old-versions",
        action="store_true",
        help="Remove old versions, keeping only the latest N versions"
    )

    action_group.add_argument(
        "--clean-orphaned",
        action="store_true",
        help="Remove data not referenced in service catalog"
    )

    # Options
    parser.add_argument(
        "--keep",
        type=int,
        default=3,
        help="Number of versions to keep when using --clean-old-versions (default: 3)"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be deleted without actually deleting"
    )

    parser.add_argument(
        "--force",
        action="store_true",
        help="Skip confirmation prompts (use with caution)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Set logging level (default: INFO)"
    )

    args = parser.parse_args()

    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # Initialize cleanup utility
    cleanup = ParsedDataCleanup(
        base_path=args.base_path,
        catalog_path=args.catalog_path,
        dry_run=args.dry_run
    )

    try:
        # Handle summary action
        if args.summary:
            cleanup.list_data_summary()
            return

        # For destructive actions, show summary first and ask for confirmation
        if not args.force and not args.dry_run:
            logger.info("Current data summary:")
            cleanup.list_data_summary()
            logger.info("")

            if args.clean_all:
                response = input("⚠️  This will DELETE ALL parsed data. Are you sure? (type 'yes' to confirm): ")
            elif args.clean_services:
                response = input(f"⚠️  This will DELETE data for services: {', '.join(args.clean_services)}. Are you sure? (type 'yes' to confirm): ")
            elif args.clean_old_versions:
                response = input(f"⚠️  This will DELETE old versions (keeping latest {args.keep}). Are you sure? (type 'yes' to confirm): ")
            elif args.clean_orphaned:
                response = input("⚠️  This will DELETE orphaned data. Are you sure? (type 'yes' to confirm): ")
            else:
                response = "yes"  # Should not reach here

            if response.lower() != 'yes':
                logger.info("Operation cancelled.")
                return

        # Execute the requested action
        if args.clean_all:
            cleanup.clean_all_data()
        elif args.clean_services:
            cleanup.clean_specific_services(args.clean_services)
        elif args.clean_old_versions:
            cleanup.clean_old_versions(args.keep)
        elif args.clean_orphaned:
            cleanup.clean_orphaned_data()

        # Print summary
        cleanup.print_summary()

    except KeyboardInterrupt:
        logger.info("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
