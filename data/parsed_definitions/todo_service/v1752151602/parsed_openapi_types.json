{"source_format": "openapi", "target_namespace": "Todo Service API", "types": [{"name": "Todo", "category": "schema", "namespace": "Todo Service API", "properties": [{"name": "id", "type": "integer", "format": "int64", "required": false, "documentation": "Unique identifier (auto-generated)", "example": 42, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "title", "type": "string", "format": null, "required": true, "documentation": "Brief title or summary of the todo item", "example": "Complete project documentation", "enum": null, "min_length": 1, "max_length": 255, "minimum": null, "maximum": null, "pattern": null}, {"name": "description", "type": "string", "format": null, "required": false, "documentation": "Detailed description of the todo item", "example": "Write comprehensive API documentation including examples and use cases", "enum": null, "min_length": null, "max_length": 2000, "minimum": null, "maximum": null, "pattern": null}, {"name": "status", "type": "reference", "format": null, "required": true, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "ref": "#/components/schemas/TodoStatus"}, {"name": "priority", "type": "reference", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "ref": "#/components/schemas/Priority"}, {"name": "dueDate", "type": "string", "format": "date-time", "required": false, "documentation": "Due date and time for the todo item", "example": "2024-01-31T17:00:00Z", "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "createdAt", "type": "string", "format": "date-time", "required": false, "documentation": "Timestamp when the todo item was created", "example": "2024-01-01T10:00:00Z", "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "updatedAt", "type": "string", "format": "date-time", "required": false, "documentation": "Timestamp when the todo item was last updated", "example": "2024-01-15T14:30:00Z", "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "assignee", "type": "reference", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "ref": "#/components/schemas/User"}, {"name": "tags", "type": "array", "format": null, "required": false, "documentation": "List of tags for categorizing the todo item", "example": ["documentation", "urgent", "api"], "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "items_type": "string", "items_ref": null}], "restrictions": [], "documentation": "A todo item with all its properties", "source_location": "components/schemas/Todo", "schema_metadata": {"openapi_type": "object", "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "TodoStatus", "category": "schema", "namespace": "Todo Service API", "properties": [], "restrictions": [{"type": "enumeration", "values": ["PENDING", "IN_PROGRESS", "COMPLETED", "CANCELLED", "ON_HOLD"]}], "documentation": "Current status of the todo item", "source_location": "components/schemas/TodoStatus", "schema_metadata": {"openapi_type": "string", "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "Priority", "category": "schema", "namespace": "Todo Service API", "properties": [], "restrictions": [{"type": "enumeration", "values": ["LOW", "MEDIUM", "HIGH", "URGENT"]}], "documentation": "Priority level of the todo item", "source_location": "components/schemas/Priority", "schema_metadata": {"openapi_type": "string", "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "User", "category": "schema", "namespace": "Todo Service API", "properties": [{"name": "id", "type": "integer", "format": "int64", "required": true, "documentation": "Unique user identifier", "example": 123, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "username", "type": "string", "format": null, "required": true, "documentation": "Unique username", "example": "john_doe", "enum": null, "min_length": 3, "max_length": 30, "minimum": null, "maximum": null, "pattern": "^[a-zA-Z0-9_]{3,30}$"}, {"name": "email", "type": "string", "format": "email", "required": true, "documentation": "User's email address", "example": "<EMAIL>", "enum": null, "min_length": null, "max_length": 254, "minimum": null, "maximum": null, "pattern": null}, {"name": "fullName", "type": "string", "format": null, "required": false, "documentation": "User's full display name", "example": "<PERSON>", "enum": null, "min_length": null, "max_length": 100, "minimum": null, "maximum": null, "pattern": null}], "restrictions": [], "documentation": "User who can be assigned to todo items", "source_location": "components/schemas/User", "schema_metadata": {"openapi_type": "object", "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "TodoCreateRequest", "category": "schema", "namespace": "Todo Service API", "properties": [{"name": "title", "type": "string", "format": null, "required": true, "documentation": null, "example": "Complete project documentation", "enum": null, "min_length": 1, "max_length": 255, "minimum": null, "maximum": null, "pattern": null}, {"name": "description", "type": "string", "format": null, "required": false, "documentation": null, "example": "Write comprehensive API documentation", "enum": null, "min_length": null, "max_length": 2000, "minimum": null, "maximum": null, "pattern": null}, {"name": "status", "type": "reference", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "ref": "#/components/schemas/TodoStatus"}, {"name": "priority", "type": "reference", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "ref": "#/components/schemas/Priority"}, {"name": "dueDate", "type": "string", "format": "date-time", "required": false, "documentation": null, "example": "2024-01-31T17:00:00Z", "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "assigneeId", "type": "integer", "format": "int64", "required": false, "documentation": "ID of the user to assign this todo to", "example": 123, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "tags", "type": "array", "format": null, "required": false, "documentation": null, "example": ["documentation", "urgent"], "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "items_type": "string", "items_ref": null}], "restrictions": [], "documentation": "Request payload for creating a new todo item", "source_location": "components/schemas/TodoCreateRequest", "schema_metadata": {"openapi_type": "object", "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "TodoUpdateRequest", "category": "schema", "namespace": "Todo Service API", "properties": [{"name": "title", "type": "string", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": 1, "max_length": 255, "minimum": null, "maximum": null, "pattern": null}, {"name": "description", "type": "string", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": 2000, "minimum": null, "maximum": null, "pattern": null}, {"name": "status", "type": "reference", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "ref": "#/components/schemas/TodoStatus"}, {"name": "priority", "type": "reference", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "ref": "#/components/schemas/Priority"}, {"name": "dueDate", "type": "string", "format": "date-time", "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "assigneeId", "type": "integer", "format": "int64", "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "tags", "type": "array", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "items_type": "string", "items_ref": null}], "restrictions": [], "documentation": "Request payload for updating an existing todo item", "source_location": "components/schemas/TodoUpdateRequest", "schema_metadata": {"openapi_type": "object", "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "TodoResponse", "category": "schema", "namespace": "Todo Service API", "properties": [], "restrictions": [], "documentation": null, "source_location": "components/schemas/TodoResponse", "schema_metadata": {"openapi_type": null, "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "TodoListResponse", "category": "schema", "namespace": "Todo Service API", "properties": [{"name": "todos", "type": "array", "format": null, "required": true, "documentation": "Array of todo items", "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "items_type": null, "items_ref": "#/components/schemas/Todo"}, {"name": "totalCount", "type": "integer", "format": null, "required": true, "documentation": "Total number of todo items matching the criteria", "example": 150, "enum": null, "min_length": null, "max_length": null, "minimum": 0, "maximum": null, "pattern": null}, {"name": "hasMore", "type": "boolean", "format": null, "required": true, "documentation": "Indicates if there are more results available", "example": true, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "limit", "type": "integer", "format": null, "required": true, "documentation": "Maximum number of items returned in this response", "example": 20, "enum": null, "min_length": null, "max_length": null, "minimum": 1, "maximum": null, "pattern": null}, {"name": "offset", "type": "integer", "format": null, "required": true, "documentation": "Number of items skipped", "example": 0, "enum": null, "min_length": null, "max_length": null, "minimum": 0, "maximum": null, "pattern": null}], "restrictions": [], "documentation": "Response containing a list of todo items with pagination info", "source_location": "components/schemas/TodoListResponse", "schema_metadata": {"openapi_type": "object", "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "UserListResponse", "category": "schema", "namespace": "Todo Service API", "properties": [{"name": "users", "type": "array", "format": null, "required": true, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null, "items_type": null, "items_ref": "#/components/schemas/User"}, {"name": "totalCount", "type": "integer", "format": null, "required": true, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": 0, "maximum": null, "pattern": null}, {"name": "limit", "type": "integer", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": 1, "maximum": null, "pattern": null}, {"name": "offset", "type": "integer", "format": null, "required": false, "documentation": null, "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": 0, "maximum": null, "pattern": null}], "restrictions": [], "documentation": "Response containing a list of users", "source_location": "components/schemas/UserListResponse", "schema_metadata": {"openapi_type": "object", "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "Error", "category": "schema", "namespace": "Todo Service API", "properties": [{"name": "code", "type": "string", "format": null, "required": true, "documentation": "Error code identifying the type of error", "example": "VALIDATION_ERROR", "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "message", "type": "string", "format": null, "required": true, "documentation": "Human-readable error message", "example": "The title field is required", "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "timestamp", "type": "string", "format": "date-time", "required": true, "documentation": "When the error occurred", "example": "2024-01-15T10:30:00Z", "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}, {"name": "details", "type": "object", "format": null, "required": false, "documentation": "Additional error details", "example": null, "enum": null, "min_length": null, "max_length": null, "minimum": null, "maximum": null, "pattern": null}], "restrictions": [], "documentation": "Standard error response", "source_location": "components/schemas/Error", "schema_metadata": {"openapi_type": "object", "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}, {"name": "ValidationError", "category": "schema", "namespace": "Todo Service API", "properties": [], "restrictions": [], "documentation": null, "source_location": "components/schemas/ValidationError", "schema_metadata": {"openapi_type": null, "format": null, "nullable": false, "read_only": false, "write_only": false, "deprecated": false}}], "metadata": {"extraction_timestamp": "2025-07-10T14:46:42.461465", "total_types": 11, "openapi_version": "3.0.3", "api_info": {"title": "Todo Service API", "description": "A comprehensive REST API for managing todo items with full CRUD operations,\nsearch functionality, user management, and advanced features like priorities,\ndue dates, and task assignments.\n\n## Features\n- Create, read, update, and delete todo items\n- Advanced search and filtering capabilities\n- User assignment and management\n- Priority levels and due date tracking\n- Tag-based categorization\n- Pagination support for large datasets\n", "version": "1.0.0", "contact": {"name": "Todo Service API Support", "email": "<EMAIL>", "url": "https://api.todoservice.com/support"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "termsOfService": "https://api.todoservice.com/terms"}, "namespaces": {}}}