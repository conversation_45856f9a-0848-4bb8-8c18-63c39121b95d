{"service_name": "gts_sales_service", "timestamp": 1752151819, "version_dir": "../data/parsed_definitions//gts_sales_service/v1752151819", "files": {"wsdl": "../data/parsed_definitions//gts_sales_service/v1752151819/parsed_wsdl.json", "wsdl_types": "../data/parsed_definitions//gts_sales_service/v1752151819/parsed_wsdl_types.json", "xsd": "../data/parsed_definitions//gts_sales_service/v1752151819/parsed_xsd.json", "xsd_types": "../data/parsed_definitions//gts_sales_service/v1752151819/parsed_xsd_types.json", "openapi_error": "Object of type datetime is not JSON serializable"}, "processing_start": "2025-07-10T14:50:19.491120"}