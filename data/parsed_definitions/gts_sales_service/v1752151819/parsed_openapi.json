{
  "extracted": {
    "openapi_version": "3.0.3",
    "info": {
      "title": "SilverRail API",
      "version": "1.0.43",
      "description": "Compatible with OSDM version 3.2.0\n\nThe OSDM specification supports two modes of operation: Retailer Mode \nand Distributor Mode. The API works identically in both modes, except that in distributor mode the API also \nreturns fare information.\n\nThe following resources are key to get started:\n\n  -  [Processes](https://osdm.io/spec/processes/)\n  -  [Models](https://osdm.io/spec/models/)\n  -  [Getting started](https://osdm.io/spec/getting-started/)\n",
      "contact": {
        "name": "SilverRail Technologies",
        "url": "https://silverrailtech.com/",
        "email": "<EMAIL>"
      },
      "license": {
        "name": "Apache 2.0",
        "url": "https://www.apache.org/licenses/LICENSE-2.0.html"
      }
    },
    "servers": [],
    "paths": {
      "/places": {
        "get": {
          "operationId": "getPlaces",
          "summary": "Returns all places.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/ifNoneMatch"
            },
            {
              "name": "page",
              "in": "query",
              "description": "Can be used for pagination. When not given, the service returns the first page.\nClients should not generate page ids themselves. Instead they should follow the\nentry in response _links object with rel=next.\nThat link expires when the data on the server changes. This ensures that clients\ndo not combine inconsistent pages into a corrupted data set. An expired page\nwill return HTTP 400 error. Typically links have a fairly long lifetime,\nhowever there is no guarantee of any specific minimum lifetime.\n",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "places found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PlaceResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                },
                "ETag": {
                  "schema": {
                    "type": "string"
                  },
                  "description": "version tag"
                }
              }
            },
            "303": {
              "$ref": "#/components/responses/SeeOtherResponse"
            },
            "304": {
              "$ref": "#/components/responses/NotModifiedResponse"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "post": {
          "operationId": "postPlaces",
          "summary": "returns place information for a given place request",
          "description": "Returns places for a given place request based on the OJP specification.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            }
          ],
          "requestBody": {
            "description": "request for place\n",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/PlaceRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "places found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PlaceResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/places/{placeId}": {
        "get": {
          "operationId": "getPlacesId",
          "summary": "returns a place",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "placeId",
              "in": "path",
              "description": "id of the place to get.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "place found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PlaceResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/trips-collection": {
        "post": {
          "operationId": "postTrips",
          "summary": "returns a collection of trips for a given OJP trip request",
          "description": "Returns trips for a given trip request based on the OJP specification.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            }
          ],
          "requestBody": {
            "description": "request for trips\n",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TripSearchCriteria"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "trips found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/TripCollectionResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/trips-collections/{tripsCollectionId}": {
        "get": {
          "operationId": "getTripsCollectionId",
          "summary": "Returns a collection of trips.",
          "description": "Returns a collection of trips for a for a given trips collection id.\nThe unique codes of the origin and destination can be resolved using the places service.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "tripsCollectionId",
              "in": "path",
              "description": "id of the trips\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "page",
              "in": "query",
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "embed",
              "in": "query",
              "description": "Influences whether referenced resources are returned in full or as references only.\nDefault value: ALL\n",
              "schema": {
                "type": "array",
                "maxItems": 2,
                "items": {
                  "$ref": "#/components/schemas/TripsCollectionResponseContent"
                }
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "trips found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/TripCollectionResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/trips/{tripId}": {
        "get": {
          "operationId": "getTripsId",
          "summary": "Returns a trip element representing a travel trip.",
          "description": "A trip consists of one or more legs for a given tripId. Depending on the\nembed either references or full location definitions is returned.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "tripId",
              "in": "path",
              "description": "id of the trip to get.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "stopBehavior",
              "in": "query",
              "description": "Influences what stops are to be returned in response\n(ORIGIN_DESTINATION_ONLY returns no intermediate stops;\nREAL_BOARDING_ALIGHTING returns all stops except virtual stops).\nDefault value: ORIGIN_DESTINATION_ONLY\n",
              "schema": {
                "$ref": "#/components/schemas/StopBehavior"
              }
            },
            {
              "name": "embed",
              "in": "query",
              "description": "Influences whether referenced resources are returned in full or as references only.\nDefault value: ALL\n",
              "schema": {
                "type": "array",
                "maxItems": 1,
                "items": {
                  "$ref": "#/components/schemas/TripResponseContent"
                }
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "trip found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/TripResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/offers": {
        "post": {
          "operationId": "createOffers",
          "summary": "Returns travel offers matching the search criteria.",
          "description": "Returns available travel offers based on user-specified search criteria\nsuch as origin, destination, departure time, and passenger details.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            }
          ],
          "requestBody": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/OfferCollectionRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "A collection of offers matching the search criteria.\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/OfferCollectionResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/booked-offers/{bookedOfferId}/additional-offers": {
        "get": {
          "operationId": "getBookingBookedOffersAdditionalOffers",
          "summary": "Get additional offers of booked offer for a given booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "bookedOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "additional offers found for booked offer\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/AdditionalOfferCollectionResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/on-hold-offer": {
        "post": {
          "operationId": "createOnHoldOffer",
          "summary": "Creates an on hold offer.",
          "description": "On hold offer created\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/OnHoldOfferRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "on-hold offer created\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/OnHoldOfferResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/on-hold-offer/{onHoldOfferId}": {
        "patch": {
          "operationId": "confirmOnHoldOffer",
          "summary": "Confirms an on hold offer.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "onHoldOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/OnHoldOfferPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "on-hold offer confirmed\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/OnHoldOfferResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/passengers/{passengerId}": {
        "get": {
          "operationId": "getBookingPassengersId",
          "summary": "Returns the passenger's information at booking step.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "passengerId",
              "in": "path",
              "description": "id of the passenger\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "passenger found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PassengerResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "patchBookingPassenger",
          "summary": "Allows updating a passenger's information at booking step.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking the passenger is in.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "passengerId",
              "in": "path",
              "description": "id of the passenger\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Passenger"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "passenger successfully patched\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PassengerResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/purchaser": {
        "get": {
          "operationId": "getBookingPurchaser",
          "summary": "Returns the purchaser's information at booking step.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking the purchaser is in.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "purchaser found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PurchaserResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "patchBookingPurchaser",
          "summary": "Allows updating a purchaser's information at booking step.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking the purchaser is in.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Purchaser"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "purchaser patched\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PurchaserResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/booked-offers": {
        "post": {
          "operationId": "postBookingBookedOffers",
          "summary": "Creates bookedOffers from offers and adds them in a booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookedOfferRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "booking pre-booked\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookedOfferResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/booked-offers/{bookedOfferId}": {
        "get": {
          "operationId": "getBookingBookedOffersId",
          "summary": "Gets a bookedOffer of a booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "bookedOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "bookedOffer found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookedOfferResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "delete": {
          "operationId": "deleteBookingBookedOffersId",
          "summary": "Delete a bookedOffer from a booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "bookedOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/booked-offers/{offerId}/passengers/{passengerId}": {
        "delete": {
          "operationId": "deleteBookingBookedOffersIdPassengerId",
          "summary": "Delete a passenger from bookedOffer of a booking.",
          "description": "Deletes all booked offer parts of the passenger only if this valid from a tariff point of view. This implies that the offer part are sufficiently partitioned and no repricing is needed.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "offerId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "passengerId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/booked-offers/{bookedOfferId}/reservations": {
        "post": {
          "operationId": "createBookingBookedOffersReservations",
          "summary": "Adds an optional reservation to a pre-booked booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "bookedOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookedOfferReservationRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "reservation added to booked offer\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookedOfferReservationResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/booked-offers/{bookedOfferId}/reservations/{reservationId}": {
        "delete": {
          "operationId": "deleteBookingBookedOffersReservations",
          "summary": "Removes an optional reservation from pre-booked booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "bookedOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "reservationId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/booked-offers/{bookedOfferId}/ancillaries": {
        "post": {
          "operationId": "createBookingBookedOffersAncillaries",
          "summary": "Adds an ancillary to a pre-booked booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "bookedOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookedOfferAncillaryRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "ancillary added to booked offer\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookedOfferAncillaryResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/booked-offers/{bookedOfferId}/ancillaries/{ancillaryId}": {
        "delete": {
          "operationId": "deleteBookingBookedOffersAncillary",
          "summary": "Removes an ancillary from pre-booked booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "bookedOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "ancillaryId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/booked-offers/{bookedOfferId}/admissions/{admissionId}": {
        "delete": {
          "operationId": "deleteBookingBookedOffersAdmission",
          "summary": "Removes an admission from pre-booked booking.",
          "description": "Removes an admission from pre-booked booking.It is up to the provider\nto change or remove dependent bookedOfferParts or to reject the request.\nA repricing might occur.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "bookedOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "admissionId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings": {
        "post": {
          "operationId": "postBookings",
          "summary": "Creates a booking based on a previously requested offer.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookingRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "booking pre-booked\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookingResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}": {
        "get": {
          "operationId": "getBookingsId",
          "summary": "Returns a booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking to get.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "embed",
              "in": "query",
              "description": "Influences whether referenced resources are returned in full or as references only.\nDefault value: ALL\n",
              "schema": {
                "type": "array",
                "maxItems": 7,
                "items": {
                  "$ref": "#/components/schemas/BookingResponseContent"
                }
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "booking found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookingResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "updateBooking",
          "summary": "Updates a booking but does *not* confirm the booking.",
          "description": "Updates fulfillment types, place selection and add payment. Does *not* confirm the booking. The booking is confirmed by calling 'POST /bookings/{bookingId}/fulfillments'.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking to be patched\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookingPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "booking fulfillment type updated\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookingResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "delete": {
          "operationId": "deleteBookingsId",
          "summary": "Deletes a booking.",
          "description": "It is only possible before the booking is confirmed or in case of a technical problem in confirming multiple\nindependent bookings within a sales transaction. Deletes on a confirmed booking must be documented\nand evidence on the issue must be provided on request.\nThe delete on a confirmed booking is allowed immediately after the confirmation of the booking,\nbut must be repeated according to the error handling rules in case the delete fails.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking to delete.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/cleanup": {
        "post": {
          "operationId": "postBookingCleanup",
          "summary": "Performs a complete cleanup of a booking in a single step",
          "description": "The booking is cleaned up completely: confirmed items are refunded, and unconfirmed items are deleted.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookingCleanupRequest"
                }
              }
            }
          },
          "responses": {
            "202": {
              "$ref": "#/components/responses/CleanupRequestAcceptedResponse"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/history": {
        "get": {
          "operationId": "getBookingsIdHistory",
          "summary": "Returns the history of changes to a booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking to get.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "booking history found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookingHistoryResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings-search": {
        "post": {
          "operationId": "searchBookings",
          "summary": "Search for bookings based on search parameters.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "page",
              "in": "query",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookingSearchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "booking search results found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookingSearchResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/split": {
        "post": {
          "operationId": "splitBookings",
          "summary": "Split a booking into multiple bookings.",
          "description": "Splits a booking into a set of bookings according to the defined groups of a passenger(s). Only possible if allowed by the underlying tariff, if the bookedOffers are sufficiently partitioned and no pricing is needed.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking to be split\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/BookingSplitRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "booking split\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/BookingSplitResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/fulfillments": {
        "post": {
          "operationId": "postFulfillments",
          "summary": "Confirms the booking a triggers the fulfillment of the booking synchronously or asynchronously.",
          "description": "Confirms the booking and triggers the fulfillment of the booking.\n\nIf the fulfillments are created synchronously the service directly returns the fulfillments. The fulfillments are then in state FULFILLED.\n\nIf the fulfillments are created asynchronously the service starts the creation of the fulfillments. No fulfillmentIds are returned. The fulfillments are in stage CONFIRMED. The booking needs to be retrieved later to obtain the fulfillments.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking to be patched\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": false,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/FulfillmentPostRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Fulfillment successfully completed\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/FulfillmentCollectionResponse"
                  }
                }
              }
            },
            "202": {
              "$ref": "#/components/responses/FulfillmentRequestAcceptedResponse"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "finalizeFulfillments",
          "summary": "Finalizes the fulfillments in asynchronous mode.",
          "description": "Finalizes the fulfillment in case of an asynchronous fulfillment mode.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking to be patched\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/FulfillmentPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Fulfillment successfully completed\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/FulfillmentCollectionResponse"
                  }
                }
              }
            },
            "202": {
              "$ref": "#/components/responses/FulfillmentRequestAcceptedResponse"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/fulfillments/{fulfillmentId}": {
        "get": {
          "operationId": "getFulfillmentId",
          "summary": "Returns the fulfillment, aka. ticket for the provided id.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "fulfillmentId",
              "in": "path",
              "description": "id of the fulfillment to get.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "fulfillment found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/FulfillmentResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "patchFulfillmentId",
          "summary": "Activates a fulfillment, i.e. changes the status to AVAILABLE.",
          "description": "Changes the fulfillment to status AVAILABLE. In the case of multi-journey product, one of the fulfillment is now 'activated' and can be used to travel.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "fulfillmentId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/FulfillmentActivationPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "fulfillment patched\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/FulfillmentResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/refund-offers": {
        "post": {
          "operationId": "postRefundOffers",
          "summary": "Initiates a refund process by creating a refundOffer resource.",
          "description": "The RefundOffer contains the required information on the potential operation. One refund offer can then be\naccepted via a PATCH, deleted or left to die at the end of its lifetime.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/RefundOfferRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "refund offer created\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/RefundOfferCollectionResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/refund-offers/{refundOfferId}": {
        "get": {
          "operationId": "getRefundOffers",
          "summary": "Returns the refund offer for the ids provided.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "refundOfferId",
              "in": "path",
              "description": "id of the refund offer to get.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "refund offer found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/RefundOfferResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "patchRefundOffers",
          "summary": "Allows to accept and confirm a refund offer.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "refundOfferId",
              "in": "path",
              "description": "id of the refund offer\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/RefundOfferPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "refund offer confirmed\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/RefundOfferResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "delete": {
          "operationId": "deleteRefundOffers",
          "summary": "Deletes a refundOffer without waiting for expiry.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "refundOfferId",
              "in": "path",
              "description": "id of the refund offer\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "refund offer deleted\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/release-offers": {
        "post": {
          "operationId": "postReleaseOffers",
          "summary": "Initiates a release process by creating a releaseOffers resource.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ReleaseOfferRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "release offer created\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ReleaseOfferCollectionResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/release-offers/{releaseOfferId}": {
        "get": {
          "operationId": "getReleaseOffer",
          "summary": "Returns the release offer for the ids provided.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "releaseOfferId",
              "in": "path",
              "description": "id of the refund offer to get.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "release offer found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ReleaseOfferResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "patchReleaseOffers",
          "summary": "Allows to accept and confirm a release offer.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "releaseOfferId",
              "in": "path",
              "description": "id of the refund offer\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ReleaseOfferPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "release offer confirmed\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ReleaseOfferResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "delete": {
          "operationId": "deleteReleaseOffers",
          "summary": "Deletes a release offer without waiting for expiry.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "releaseOfferId",
              "in": "path",
              "description": "id of the refund offer\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "release offer deleted\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/cancel-fulfillments-offers": {
        "post": {
          "operationId": "postCancelFulfillmentsOffers",
          "summary": "Initiates a cancel fulfillments process",
          "description": "Initiates a cancel fulfillments process by creating a\ncancelFulfillmentsOffers resource.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CancelFulfillmentsOfferRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Refund offer created\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/CancelFulfillmentsOfferCollectionResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/cancel-fulfillments-offers/{cancelFulfillmentsOfferId}": {
        "get": {
          "operationId": "getCancelFulfillmentOffers",
          "summary": "Returns the cancel fulfillments offer for the ids provided.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "cancelFulfillmentsOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "RefundOffer found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/CancelFulfillmentsOfferResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "patchCancelFulfillmentsOffers",
          "summary": "Allows to accept and confirm a cancel fulfillments offer.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "cancelFulfillmentsOfferId",
              "in": "path",
              "description": "id of the refund offer\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CancelFulfillmentsOfferPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "RefundOffer confirmed\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/CancelFulfillmentsOfferResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "delete": {
          "operationId": "deleteCancelFulfillmentOffers",
          "summary": "Deletes a cancel fulfillments offer without waiting for expiry.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "cancelFulfillmentsOfferId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/exchange-offers": {
        "post": {
          "operationId": "createExchangeOffersCollection",
          "summary": "Returns exchange offers for specified fulfillments",
          "description": "Returns exchange offers for specified fulfillments submitted given\nrequested new trip characteristics.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string",
                "nullable": false
              }
            },
            {
              "name": "embed",
              "in": "query",
              "required": true,
              "schema": {
                "$ref": "#/components/schemas/ExchangeOfferCollectionResponseContent"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ExchangeOfferCollectionRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Collection of exchange offers found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExchangeOfferCollectionResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/exchange-operations": {
        "post": {
          "operationId": "createBookingsExchangeOperations",
          "summary": "Pre-books an exchangeOffer as part of an exchange operation.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string",
                "nullable": false
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ExchangeOperationRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "exchange offer pre-booked\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExchangeOperationResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/exchange-operations/{exchangeOperationId}": {
        "get": {
          "operationId": "getBookingsExchangeOperations",
          "summary": "Returns the exchange operation with the id provided.",
          "description": "Returns the exchange operation with the id provided. It may be a\nprovisional or a confirmed exchange.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking\n",
              "required": true,
              "schema": {
                "type": "string",
                "nullable": false
              }
            },
            {
              "name": "exchangeOperationId",
              "in": "path",
              "description": "id of the exchange operation\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "embed",
              "in": "query",
              "description": "Influences whether referenced resources are returned in full or as references only.\nDefault value: ALL\n",
              "schema": {
                "type": "array",
                "maxItems": 5,
                "items": {
                  "$ref": "#/components/schemas/ExchangeOperationResponseContent"
                }
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "ExchangeOperation found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExchangeOperationResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "updateBookingsExchangeOperations",
          "summary": "Allows to update an ongoing exchange operation.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking to be exchanged.\n",
              "required": true,
              "schema": {
                "type": "string",
                "nullable": false
              }
            },
            {
              "name": "exchangeOperationId",
              "in": "path",
              "description": "id of the exchange operation.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ExchangeOperationPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Exchange successfully completed\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ExchangeOperationResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "delete": {
          "operationId": "deleteBookingsExchangeOperation",
          "summary": "Cancels an ongoing exchange operation in provisional state.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "description": "id of the booking containing the exchange operation\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "exchangeOperationId",
              "in": "path",
              "description": "id of the exchangeOperation to delete.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/complaints": {
        "post": {
          "operationId": "createComplaint",
          "summary": "Allows adding a complaint.",
          "description": "Create a complaint request for part of a booking.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            }
          ],
          "requestBody": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/Complaint"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "complaint\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ComplaintResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/complaints/{complaintId}": {
        "get": {
          "operationId": "getComplaint",
          "summary": "Returns a complaint.",
          "description": "Get a complaint including its current state.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "complaintId",
              "in": "path",
              "description": "id of the complaint\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "the requested complaint\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ComplaintResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "patchComplaint",
          "summary": "Allows updating a complaint.",
          "description": "Update a complaint request, i.e add missing documents or change state.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "complaintId",
              "in": "path",
              "description": "id of the complaint to be patched\n",
              "required": true,
              "schema": {
                "type": "string",
                "format": "uuid"
              }
            }
          ],
          "requestBody": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ComplaintPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "complaint updated\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ComplaintResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/coach-layouts": {
        "get": {
          "operationId": "getCoachLayouts",
          "summary": "Returns all coach layouts.",
          "description": "Retrieve the coach layout description needed for graphical reservation. The coach\nlayouts can either be retrieved as a complete list or specificity for a train identified via offerId and reservationId or fareId\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "page",
              "in": "query",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "coach layouts\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/CoachLayoutCollectionResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/coach-layouts/{layoutId}": {
        "get": {
          "operationId": "getCoachLayoutsLayoutId",
          "summary": "Returns a coach layout for a provided id.",
          "description": "Retrieve a coach layout description needed for graphical reservation for a given layout id.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "layoutId",
              "in": "path",
              "description": "id of the layout\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "coach layouts\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/CoachLayoutResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/reduction-cards": {
        "get": {
          "operationId": "getReductionCards",
          "summary": "Returns all reduction card definitions.",
          "description": "returns a collection of reduction card definitions\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "page",
              "in": "query",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "Reduction cards provided\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ReductionCardCollectionResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                },
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/zones": {
        "get": {
          "operationId": "getZones",
          "summary": "Returns all zone definitions.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "page",
              "in": "query",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "zones provided\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ZoneCollectionResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                },
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                },
                "Expires": {
                  "schema": {
                    "type": "string",
                    "description": "Gives the date/time after which the response is considered stale (in 'HTTP-date' format as defined by RFC 7231).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/documents": {
        "post": {
          "operationId": "createDocuments",
          "summary": "Allows adding a document to a booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DocumentRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "booking documents created\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/DocumentCollectionResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/documents/{documentId}": {
        "get": {
          "operationId": "getDocument",
          "summary": "Returns a booking document.",
          "description": "booking documents found\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "documentId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "booking document found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/DocumentResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "delete": {
          "operationId": "deleteBookingsDocumentsId",
          "summary": "Delete a document from a booking.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/idempotencyKey"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "documentId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "204": {
              "description": "In case that a request was successful only status code 204 will be returned but no response content will be provided (aka as return type 'void' in many programming languages).\n"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/reimbursements": {
        "post": {
          "operationId": "createReimbursement",
          "summary": "Create reimbursement for a booking.",
          "description": "Create a reimbursement request for part of a booking.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string",
                "nullable": false
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ReimbursementRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Reimbursement created\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ReimbursementResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/bookings/{bookingId}/reimbursements/{reimbursementId}": {
        "get": {
          "operationId": "getReimbursement",
          "summary": "Get reimbursement of a booking.",
          "description": "Get reimbursement including its current state.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string",
                "nullable": false
              }
            },
            {
              "name": "reimbursementId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string",
                "nullable": false
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "reimbursement found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ReimbursementResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "patch": {
          "operationId": "updateReimbursement",
          "summary": "Update reimbursement of a booking.",
          "description": "Update a reimbursement request, i.e add missing documents or change state.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "bookingId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string",
                "nullable": false
              }
            },
            {
              "name": "reimbursementId",
              "in": "path",
              "required": true,
              "schema": {
                "type": "string",
                "nullable": false
              }
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ReimbursementPatchRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "reimbursement updated\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ReimbursementResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "409": {
              "$ref": "#/components/responses/ConflictResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/travel-accounts": {
        "get": {
          "operationId": "getTravelAccounts",
          "summary": "Returns a travel account.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "issuer",
              "in": "query",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "travelAccount",
              "in": "query",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "embed",
              "in": "query",
              "description": "Default value: ALL\n",
              "schema": {
                "type": "array",
                "items": {
                  "$ref": "#/components/schemas/TravelAccountResponseContent"
                }
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "travel account found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/TravelAccountResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/availabilities/place-map": {
        "get": {
          "operationId": "getAvailabilitiesPlaceMap",
          "summary": "Get place map including availabilities.",
          "description": "Get place map including availabilities.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "contextId",
              "in": "query",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "contextType",
              "in": "query",
              "required": true,
              "schema": {
                "$ref": "#/components/schemas/ContextType"
              }
            },
            {
              "name": "resourceId",
              "in": "query",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "resourceType",
              "in": "query",
              "required": true,
              "schema": {
                "$ref": "#/components/schemas/ResourceType"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PlaceAvailabilityResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "post": {
          "operationId": "getAvailabilitiesPlaceMaps",
          "summary": "Get multiple place maps including availabilities.",
          "description": "Get multiple place maps including availabilities.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            }
          ],
          "requestBody": {
            "required": true,
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/AvailabilityScope"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "place availabilities for the requested fares and offer parts\n",
              "content": {
                "application/json": {
                  "schema": {
                    "type": "array",
                    "maxItems": 50,
                    "items": {
                      "$ref": "#/components/schemas/PlaceAvailability"
                    }
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/availabilities/nearby": {
        "get": {
          "operationId": "getAvailabilitiesNearby",
          "summary": "Get availabilities nearby a given place.",
          "description": "Get availabilities nearby a given place.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "contextId",
              "in": "query",
              "description": "denotes the offerId when contextType is OFFER, bookingId when contextType is BOOKING\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "contextType",
              "in": "query",
              "required": true,
              "schema": {
                "$ref": "#/components/schemas/ContextType"
              }
            },
            {
              "name": "resourceId",
              "in": "query",
              "description": "denotes the reservationId wen resourceType is RESERVATION, fareId when resourceType is FARE\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "resourceType",
              "in": "query",
              "required": true,
              "schema": {
                "$ref": "#/components/schemas/ResourceType"
              }
            },
            {
              "name": "coachNumber",
              "in": "query",
              "description": "coach number of the referenced place\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "placeNumber",
              "in": "query",
              "description": "place number of the referenced place\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "place availabilities found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PlaceAvailabilityCollectionResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/availabilities/preferences": {
        "get": {
          "operationId": "getAvailabilitiesPreferences",
          "summary": "Get availabilities for a set of preferences.",
          "description": "Get availabilities for a set of preferences.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "contextId",
              "in": "query",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "contextType",
              "in": "query",
              "required": true,
              "schema": {
                "$ref": "#/components/schemas/ContextType"
              }
            },
            {
              "name": "resourceId",
              "in": "query",
              "required": true,
              "schema": {
                "type": "string"
              }
            },
            {
              "name": "resourceType",
              "in": "query",
              "required": true,
              "schema": {
                "$ref": "#/components/schemas/ResourceType"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "place availabilities found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/PlaceAvailabilityCollectionResponse"
                  }
                }
              },
              "headers": {
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/products": {
        "get": {
          "operationId": "getProducts",
          "summary": "Returns all products.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "page",
              "in": "query",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "Product found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ProductCollectionResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                },
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/products/{productId}": {
        "get": {
          "operationId": "getProductsId",
          "summary": "Returns a product for the provided id.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "productId",
              "in": "path",
              "description": "id of the product to get\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "Product found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ProductResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                },
                "Content-Language": {
                  "schema": {
                    "type": "string",
                    "description": "The language of translatable strings in the response (see RFC2616-sec14.12).\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/routes": {
        "get": {
          "operationId": "getRoutes",
          "summary": "Returns all routes.",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "$ref": "#/components/parameters/ifNoneMatch"
            },
            {
              "name": "page",
              "in": "query",
              "description": "Can be used for pagination. When not given, the service returns the first page.\nClients should not generate page ids themselves. Instead they should follow the\nentry in response _links object with rel=next.\nThat link expires when the data on the server changes. This ensures that clients\ndo not combine inconsistent pages into a corrupted data set. An expired page\nwill return HTTP 400 error. Typically links have a fairly long lifetime,\nhowever there is no guarantee of any specific minimum lifetime.\n",
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "routes found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/RouteReferenceResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                },
                "ETag": {
                  "schema": {
                    "type": "string"
                  },
                  "description": "version tag"
                }
              }
            },
            "303": {
              "$ref": "#/components/responses/SeeOtherResponse"
            },
            "304": {
              "$ref": "#/components/responses/NotModifiedResponse"
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        },
        "post": {
          "operationId": "postRoutes",
          "summary": "Returns route information for a given route request",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            }
          ],
          "requestBody": {
            "description": "request for route\n",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/RouteReferenceRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "places found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/RouteReferenceResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/routes/{routeId}": {
        "get": {
          "operationId": "getRoutesId",
          "summary": "returns a route",
          "description": null,
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            },
            {
              "name": "routeId",
              "in": "path",
              "description": "id of the route to get.\n",
              "required": true,
              "schema": {
                "type": "string"
              }
            }
          ],
          "requestBody": null,
          "responses": {
            "200": {
              "description": "route found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/RouteReferenceResponse"
                  }
                }
              },
              "headers": {
                "Cache-Control": {
                  "schema": {
                    "type": "string",
                    "default": "public, max-age=10000",
                    "description": "Resource is fairly persistent and has a medium time to live to allow short-term caching.\n"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/stop-events": {
        "post": {
          "operationId": "postStopEvents",
          "summary": "Returns a collection of departure/arrival stop events for a given OJP StopEventRequest.",
          "description": "Returns stop events for a given stop event request based on the OJP specification.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            }
          ],
          "requestBody": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/StopEventRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "trips found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/StopEventResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      },
      "/service-info": {
        "post": {
          "operationId": "postServiceInfo",
          "summary": "Returns information about specific journeys.",
          "description": "Returns information about journeys that run on a given day\nand either match a given journeyRef or routeRef.\n",
          "parameters": [
            {
              "$ref": "#/components/parameters/requestor"
            },
            {
              "$ref": "#/components/parameters/acceptLanguage"
            },
            {
              "$ref": "#/components/parameters/traceParent"
            },
            {
              "$ref": "#/components/parameters/traceState"
            }
          ],
          "requestBody": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceInfoRequest"
                }
              }
            }
          },
          "responses": {
            "200": {
              "description": "Results found\n",
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/ServiceInfoResponse"
                  }
                }
              }
            },
            "400": {
              "$ref": "#/components/responses/BadRequestResponse"
            },
            "401": {
              "$ref": "#/components/responses/UnauthorizedResponse"
            },
            "403": {
              "$ref": "#/components/responses/ForbiddenResponse"
            },
            "404": {
              "$ref": "#/components/responses/NotFoundResponse"
            },
            "406": {
              "$ref": "#/components/responses/NotAcceptableResponse"
            },
            "415": {
              "$ref": "#/components/responses/UnsupportedMediaTypeResponse"
            },
            "500": {
              "$ref": "#/components/responses/InternalServerErrorResponse"
            },
            "501": {
              "$ref": "#/components/responses/NotImplementedResponse"
            },
            "503": {
              "$ref": "#/components/responses/ServiceUnavailableResponse"
            },
            "default": {
              "$ref": "#/components/responses/DefaultErrorResponse"
            }
          }
        }
      }
    },
    "components": {
      "schemas": {
        "AbstractBookingPart": {
          "type": "object",
          "additionalProperties": false,
          "discriminator": {
            "propertyName": "objectType"
          },
          "required": [
            "objectType",
            "id",
            "createdOn",
            "validFrom",
            "price",
            "status",
            "passengerIds",
            "refundable",
            "exchangeable"
          ],
          "properties": {
            "objectType": {
              "description": "The type of object.\n",
              "type": "string"
            },
            "id": {
              "type": "string",
              "nullable": false
            },
            "summary": {
              "type": "string",
              "nullable": true
            },
            "createdOn": {
              "description": "Validity of offer towards passenger\n",
              "type": "string",
              "format": "date-time",
              "nullable": false
            },
            "confirmableUntil": {
              "description": "Date until the booking part needs to be confirmed. Must be provided for a booking part in PREBOOKED stated.\nFor later states, the value is ignored and can be null.\n",
              "type": "string",
              "format": "date-time",
              "nullable": false
            },
            "validFrom": {
              "type": "string",
              "format": "date-time",
              "nullable": false
            },
            "validUntil": {
              "description": "Validity of offer towards passenger\n",
              "type": "string",
              "format": "date-time",
              "nullable": true
            },
            "confirmedOn": {
              "type": "string",
              "format": "date-time",
              "nullable": true
            },
            "price": {
              "$ref": "#/components/schemas/Price"
            },
            "refundAmount": {
              "$ref": "#/components/schemas/Price",
              "description": "Amount to be refunded to the purchaser"
            },
            "tripCoverage": {
              "$ref": "#/components/schemas/TripCoverage"
            },
            "summaryProductId": {
              "type": "string",
              "description": "Id of the product representing the commercial attributes of this booking part. Although not currently\nmandatory, this attribute should in all cases be filled in order to allow matching a booking response\nto the data in the booking request\n"
            },
            "products": {
              "description": "Information about the specific products and journey legs linked to the offer.",
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/ProductLegAssociation"
              }
            },
            "status": {
              "$ref": "#/components/schemas/BookingPartStatus"
            },
            "offerMode": {
              "$ref": "#/components/schemas/OfferMode"
            },
            "bookingPartCode": {
              "type": "string",
              "description": "The unique booking code for the part in the provider system.\n"
            },
            "distributorBookingRef": {
              "description": "reference to the booking in the downstream distributor system\n",
              "type": "string",
              "nullable": true,
              "deprecated": true
            },
            "retailerBookingRef": {
              "description": "reference to the booking in the downstream distributor system\n",
              "type": "string",
              "nullable": true,
              "deprecated": true
            },
            "passengerIds": {
              "description": "Id of the passenger\n",
              "type": "array",
              "items": {
                "type": "string"
              },
              "minItems": 1,
              "nullable": false
            },
            "availableFulfillmentOptions": {
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/FulfillmentOption"
              }
            },
            "refundable": {
              "description": "Whether the offer is refundable and under what conditions.",
              "type": "string",
              "enum": [
                "FULLY_REFUNDABLE",
                "WITH_CONDITION",
                "PARTIALLY_REFUNABLE",
                "NON_REFUNDABLE"
              ]
            },
            "exchangeable": {
              "$ref": "#/components/schemas/ExchangeableType"
            },
            "afterSaleConditions": {
              "description": "Fine grained specification of the after sale condition of the booking.\n",
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/AfterSaleCondition"
              }
            },
            "appliedCorporateCodes": {
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/CorporateCode"
              }
            },
            "appliedPassengerTypes": {
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/AppliedPassengerType"
              }
            },
            "appliedPromotionCodes": {
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/PromotionCode"
              }
            },
            "appliedReductions": {
              "type": "array",
              "items": {
                "$ref": "#/components/schemas/CardReference"
              }
            },
            "indicatedConsumption": {
              "$ref": "#/components/schemas/IndicatedConsumption"
            },
            "accountingRef": {
              "$ref": "#/components/schemas/AccountingRef"
            }
          }
        },
        "AccountingRef": {
          "description": "reference to accounting data in case the accounting is not provided by the API provider\n",
          "type": "object",
          "required": [
            "accountingCompany"
          ],
          "properties": {
            "accountingCompany": {
              "$ref": "#/components/schemas/CompanyRef"
            },
            "accountingIds": {
              "description": "ids of accounting data used to link the booking part with accounting data in the billing send by the accounting company\n",
              "type": "array",
              "items": {
                "type": "string"
              }
            }
          }
        },
        "AbstractOfferPart": {
          "type": "object",
          "additionalProperties": false,
          "discriminator": {
            "propertyName": "objectType"
          },
          "required": [
            "objectType",
            "id",
            "createdOn",
            "validFrom",
            "price",
            "offerMode",
            "passengerRefs",
            "refundable",
            "exchangeable",
            "products",
            "availableFulfillmentOptions"
          ],
          "properties": {
            "objectType": {
              "description": "The type of offer component or object referenced.  In SilverOne, AdmissionOfferPart is the equivalent of a ticket.\n",
              "type": "string",
              "example": "AdmissionOfferPart"
            },
            "id": {
              "description": "A unique identifier for the object, using a structured format combining product code, owner or operator code, and region/station/product codes.\n",
              "type": "string",
              "nullable": false
            },
            "summary": {
              "description": "A human-readable description of the offer.\n",
              "type": "string",
              "nullable": true
            },
            "createdOn": {
              "description": "An ISO 8601 formatted timestamp for when the offer was created.\n",
              "type": "string",
              "format": "date-time",
              "nullable": false,
              "example": 