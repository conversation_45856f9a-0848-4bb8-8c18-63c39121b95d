#!/usr/bin/env python3
"""
Simple verification script to check that the Airflow import fix is correctly applied.

This script checks the DAG files and docker-compose.yaml to ensure the fix is in place.
"""

import os
import re
from pathlib import Path

def check_docker_compose():
    """Check that docker-compose.yaml has the shared volume mount."""
    print("🔍 Checking docker-compose.yaml...")
    
    docker_compose_path = Path("airflow/docker-compose.yaml")
    
    if not docker_compose_path.exists():
        print("❌ docker-compose.yaml not found")
        return False
    
    with open(docker_compose_path, 'r') as f:
        content = f.read()
    
    # Check for shared volume mount
    if "/opt/airflow/shared" in content:
        print("✅ Shared volume mount found")
        return True
    else:
        print("❌ Shared volume mount not found")
        print("   Expected: - ${AIRFLOW_PROJ_DIR:-.}/../shared:/opt/airflow/shared")
        return False

def check_dag_imports():
    """Check that all DAG files have the updated import logic."""
    print("\n🔍 Checking DAG import logic...")
    
    dags_dir = Path("airflow/dags")
    
    if not dags_dir.exists():
        print("❌ DAGs directory not found")
        return False
    
    # Find all DAG files
    dag_files = list(dags_dir.glob("*_dag.py"))
    
    if not dag_files:
        print("❌ No DAG files found")
        return False
    
    print(f"Found {len(dag_files)} DAG files")
    
    success_count = 0
    
    for dag_file in dag_files:
        print(f"\n  Checking {dag_file.name}...")
        
        with open(dag_file, 'r') as f:
            content = f.read()
        
        # Check for shared imports
        if "from shared.common" not in content:
            print(f"    ℹ️  No shared imports found")
            success_count += 1
            continue
        
        # Check for updated import logic
        checks = [
            ("Docker path check", "shared_path = '/opt/airflow/shared'"),
            ("Path existence check", "os.path.exists(shared_path)"),
            ("Fallback logic", "os.path.join(os.path.dirname(__file__), '..', '..', 'shared')"),
        ]
        
        all_checks_passed = True
        
        for check_name, pattern in checks:
            if pattern in content:
                print(f"    ✅ {check_name}")
            else:
                print(f"    ❌ {check_name}")
                all_checks_passed = False
        
        if all_checks_passed:
            success_count += 1
    
    print(f"\n📊 Summary: {success_count}/{len(dag_files)} DAGs have correct import logic")
    return success_count == len(dag_files)

def check_file_structure():
    """Check that the required directories exist."""
    print("\n🔍 Checking file structure...")
    
    required_paths = [
        "airflow/",
        "airflow/dags/",
        "airflow/docker-compose.yaml",
        "shared/",
        "shared/common/",
        "shared/common/constants.py",
        "shared/common/data_parsers.py",
        "shared/common/validation_utils.py",
    ]
    
    all_exist = True
    
    for path in required_paths:
        if Path(path).exists():
            print(f"  ✅ {path}")
        else:
            print(f"  ❌ {path}")
            all_exist = False
    
    return all_exist

def main():
    """Run all verification checks."""
    print("🔧 Verifying Airflow Import Fix")
    print("=" * 40)
    
    # Check file structure
    structure_ok = check_file_structure()
    
    # Check docker-compose
    docker_ok = check_docker_compose()
    
    # Check DAG imports
    dags_ok = check_dag_imports()
    
    print("\n" + "=" * 40)
    print("📋 VERIFICATION SUMMARY")
    print("=" * 40)
    
    print(f"File structure: {'✅ OK' if structure_ok else '❌ FAIL'}")
    print(f"Docker compose: {'✅ OK' if docker_ok else '❌ FAIL'}")
    print(f"DAG imports:    {'✅ OK' if dags_ok else '❌ FAIL'}")
    
    if structure_ok and docker_ok and dags_ok:
        print("\n🎉 All checks passed! The fix is correctly applied.")
        print("\nNext steps:")
        print("1. Test with: ./test_docker_setup.sh")
        print("2. Or manually: cd airflow && docker-compose up -d")
        return 0
    else:
        print("\n❌ Some checks failed. Please review the output above.")
        print("\nTo apply the fix:")
        print("1. Update docker-compose.yaml with shared volume mount")
        print("2. Update DAG files with new import logic")
        print("3. See AIRFLOW_IMPORT_FIX.md for detailed instructions")
        return 1

if __name__ == "__main__":
    exit(main())
