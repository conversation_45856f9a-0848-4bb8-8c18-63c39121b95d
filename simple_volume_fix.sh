#!/bin/bash
# Simple fix for Airflow volume mount issues

set -e

echo "🔧 SIMPLE AIRFLOW VOLUME MOUNT FIX"
echo "=================================="

# Get current directory
PROJECT_ROOT=$(pwd)
echo "Project root: $PROJECT_ROOT"

# Check if we're in the right directory
if [ ! -f "airflow/docker-compose.yaml" ] || [ ! -d "shared" ]; then
    echo "❌ Please run this script from the project root directory"
    echo "   Current directory: $PROJECT_ROOT"
    echo "   Required: airflow/docker-compose.yaml and shared/ directory"
    exit 1
fi

echo "✅ Found required files and directories"

# Step 1: Stop Airflow services
echo ""
echo "🛑 Stopping Airflow services..."
cd airflow/
docker-compose down --volumes
sleep 5

# Step 2: Fix the volume mount with absolute path
echo ""
echo "📝 Fixing volume mount with absolute path..."

# Backup original
cp docker-compose.yaml docker-compose.yaml.backup.$(date +%s)

# Replace the relative path with absolute path
sed -i.tmp "s|\${AIRFLOW_PROJ_DIR:-.}/../shared:/opt/airflow/shared|$PROJECT_ROOT/shared:/opt/airflow/shared|g" docker-compose.yaml
sed -i.tmp "s|\${AIRFLOW_PROJ_DIR:-.}/../data:/opt/airflow/data|$PROJECT_ROOT/data:/opt/airflow/data|g" docker-compose.yaml

# Remove temp file
rm -f docker-compose.yaml.tmp

echo "✅ Updated volume mounts:"
echo "   $PROJECT_ROOT/shared -> /opt/airflow/shared"
echo "   $PROJECT_ROOT/data -> /opt/airflow/data"

# Step 3: Start services
echo ""
echo "🚀 Starting Airflow services..."
docker-compose up -d

# Step 4: Wait and test
echo ""
echo "⏳ Waiting for services (30 seconds)..."
sleep 30

echo ""
echo "🧪 Testing volume mount..."

# Test if shared directory exists
if docker-compose exec -T airflow-webserver test -d /opt/airflow/shared; then
    echo "✅ /opt/airflow/shared directory exists!"
    
    # List contents
    echo ""
    echo "📁 Contents of /opt/airflow/shared:"
    docker-compose exec -T airflow-webserver ls -la /opt/airflow/shared/
    
    # Test import
    echo ""
    echo "🐍 Testing Python import..."
    docker-compose exec -T airflow-webserver python -c "
import sys
sys.path.insert(0, '/opt/airflow/shared')
from shared.common import constants
print('✅ Import successful!')
"
    
    echo ""
    echo "🎉 VOLUME MOUNT FIX SUCCESSFUL!"
    echo ""
    echo "🌐 Access Airflow UI: http://localhost:8080 (airflow/airflow)"
    
else
    echo "❌ /opt/airflow/shared still not found"
    echo ""
    echo "🔍 Debugging info:"
    echo "Available directories in /opt/airflow:"
    docker-compose exec -T airflow-webserver ls -la /opt/airflow/
    
    echo ""
    echo "Docker volume mounts:"
    docker-compose exec -T airflow-webserver mount | grep airflow
fi

cd ..  # Return to project root
